#pragma once

#include "../Common/Types.h"
#include <string>
#include <vector>
#include <memory>

/**
 * @brief 事件数据基类
 */
struct EventData {
    DWORD timestamp;
    
    EventData() : timestamp(GetCurrentTime()) {}
    virtual ~EventData() = default;
};

/**
 * @brief 玩家登录事件
 */
struct PlayerLoginEventData : public EventData {
    std::string playerName;
    std::string account;
    class PlayObject* player;
    std::string ipAddress;
    DWORD loginTime;
};

/**
 * @brief 玩家登出事件
 */
struct PlayerLogoutEventData : public EventData {
    std::string playerName;
    std::string account;
    DWORD onlineTime;
    std::string reason;
};

/**
 * @brief 物品掉落事件
 */
struct ItemDropEventData : public EventData {
    std::string playerName;
    TUserItem item;
    Point position;
    std::string mapName;
    std::string reason; // "monster_drop", "player_drop", "death_drop"
};

/**
 * @brief 物品获得事件
 */
struct ItemObtainEventData : public EventData {
    std::string playerName;
    TUserItem item;
    std::string source; // "quest_reward", "monster_drop", "trade", etc.
};

/**
 * @brief 怪物击杀事件
 */
struct MonsterKilledEventData : public EventData {
    std::string killerName;
    std::string monsterName;
    int monsterLevel;
    Point position;
    std::string mapName;
    int experience;
};

/**
 * @brief 任务完成事件
 */
struct QuestCompletedEventData : public EventData {
    std::string playerName;
    int questId;
    std::string questName;
    int experience;
    std::vector<TUserItem> rewards;
};

/**
 * @brief 地图切换事件
 */
struct MapChangeEventData : public EventData {
    std::string playerName;
    std::string fromMap;
    std::string toMap;
    Point fromPosition;
    Point toPosition;
};

/**
 * @brief PK事件
 */
struct PKEventData : public EventData {
    std::string killerName;
    std::string victimName;
    Point position;
    std::string mapName;
    int pkPoints;
    bool isRedName;
};

/**
 * @brief 玩家死亡事件
 */
struct PlayerDeathEventData : public EventData {
    std::string playerName;
    std::string killerName;
    Point position;
    std::string mapName;
    int lostExp;
    bool isPlayerKill;
};

/**
 * @brief 配置变更事件
 */
struct ConfigChangedEventData : public EventData {
    std::string configSection;
    std::string configKey;
    std::string oldValue;
    std::string newValue;
};

/**
 * @brief 系统错误事件
 */
struct SystemErrorEventData : public EventData {
    std::string errorMessage;
    std::string errorCode;
    std::string component;
    int severity; // 0=Info, 1=Warning, 2=Error, 3=Critical
};

/**
 * @brief 物品使用事件数据
 */
struct ItemUsedEventData : public EventData {
    std::string playerName;     // 玩家名称
    int itemIndex;              // 物品索引
    int count;                  // 使用数量
    std::string mapName;        // 地图名称
    int x, y;                   // 使用位置

    ItemUsedEventData() : itemIndex(0), count(1), x(0), y(0) {}
};

/**
 * @brief 对象生成事件数据
 */
struct ObjectSpawnedEventData : public EventData {
    std::string objectType;     // 对象类型 ("monster", "npc", "item")
    std::string objectName;     // 对象名称
    std::string mapName;        // 地图名称
    Point position;             // 生成位置

    ObjectSpawnedEventData() : position{0, 0} {}
};

/**
 * @brief NPC交互事件数据
 */
struct NPCInteractionEventData : public EventData {
    std::string playerName;     // 玩家名称
    std::string npcName;        // NPC名称
    std::string interactionType; // 交互类型 ("talk", "shop", "quest")
    std::string mapName;        // 地图名称

    NPCInteractionEventData() {}
};

/**
 * @brief 魔法施放事件数据
 */
struct MagicCastEventData : public EventData {
    std::string casterName;     // 施法者名称
    std::string targetName;     // 目标名称
    int magicId;                // 魔法ID
    Point targetPosition;       // 目标位置
    std::string mapName;        // 地图名称
    bool success;               // 是否成功

    MagicCastEventData() : magicId(0), targetPosition{0, 0}, success(false) {}
};

/**
 * @brief 对象死亡事件数据
 */
struct ObjectDeathEventData : public EventData {
    std::string objectName;     // 对象名称
    std::string objectType;     // 对象类型 ("player", "monster", "npc")
    std::string killerName;     // 击杀者名称
    Point position;             // 死亡位置
    std::string mapName;        // 地图名称

    ObjectDeathEventData() : position{0, 0} {}
};
