#pragma once

// Mir200 UserEngine - User management system
// Based on delphi/EM2Engine/UsrEngn.pas - Following original project structure
// Phase 1 Implementation - Complete UserEngine implementation

#include "Common/M2Share.h"
#include "Common/GameStructs.h"
#include "Objects/PlayObject.h"
#include <memory>
#include <vector>
#include <unordered_map>
#include <mutex>
#include <list>
#include <string>
#include <atomic>
#include <thread>

// Forward declarations
class PlayObject;
class Environment;
class BaseObject;
class Merchant;
class NormNpc;
class AnimalObject;
class LocalDatabase;
enum class ServerState : BYTE;

// Forward declarations - using types from Types.h and M2Share.h
// Note: Temporarily removed to avoid conflicts

// Server statistics structure
// ServerStatistics structure removed - using the one from GameStructs.h

// Monster generation info structure (based on TMonGenInfo)
struct MonGenInfo {
    std::string map_name;           // sMapName
    int race;                       // nRace
    int range;                      // nRange
    int mission_gen_rate;           // nMissionGenRate
    DWORD start_tick;               // dwStartTick
    int x, y;                       // nX, nY
    std::string mon_name;           // sMonName
    int area_x, area_y;             // nAreaX, nAreaY
    int count;                      // nCount
    DWORD zen_time;                 // dwZenTime
    DWORD start_time;               // dwStartTime
    std::list<std::shared_ptr<BaseObject>> cert_list;  // CertList
    std::shared_ptr<Environment> envir;                 // Envir
    std::list<std::shared_ptr<BaseObject>> list_3c;    // TList_3C
};

// Map monster generation count structure (based on TMapMonGenCount)
struct MapMonGenCount {
    std::string map_name;           // sMapName
    int mon_gen_count;              // nMonGenCount
    DWORD not_hum_time_tick;        // dwNotHumTimeTick
    int clear_count;                // nClearCount
    bool not_hum;                   // boNotHum
    DWORD make_mon_gen_time_tick;   // dwMakeMonGenTimeTick
    int mon_gen_rate;               // nMonGenRate
    DWORD regen_monsters_time;      // dwRegenMonstersTime
};

// User open info structure (based on TUserOpenInfo)
// Temporarily simplified for compilation
struct UserOpenInfo {
    std::string account;            // sAccount
    std::string char_name;          // sChrName
    // HumDataInfo hum_data;           // HumanRcd - temporarily removed
    // LoadUserInfo load_user;         // LoadUser - temporarily removed
};

// Gold change info structure (based on TGoldChangeInfo)
struct GoldChangeInfo {
    std::string game_master_name;   // sGameMasterName
    std::string get_gold_user;      // sGetGoldUser
    int gold;                       // nGold
    DWORD request_time;             // dwRequestTime

    GoldChangeInfo() : gold(0), request_time(GetTickCount()) {}
};

// UserEngine class - Main user management system (based on TUserEngine)
class UserEngine {
private:
    // Critical sections and thread safety
    std::mutex m_load_play_section;                     // m_LoadPlaySection

    // User management lists
    std::vector<std::shared_ptr<UserOpenInfo>> m_load_play_list;        // m_LoadPlayList
    std::unordered_map<std::string, std::shared_ptr<PlayObject>> m_play_object_list;  // m_PlayObjectList
    std::vector<std::string> m_string_list_0c;                          // m_StringList_0C
    std::list<std::shared_ptr<PlayObject>> m_play_object_free_list;     // m_PlayObjectFreeList
    std::list<std::shared_ptr<GoldChangeInfo>> m_change_human_db_gold_list;  // m_ChangeHumanDBGoldList

    // Timing variables
    DWORD m_show_online_tick;                           // dwShowOnlineTick
    DWORD m_send_online_hum_time;                       // dwSendOnlineHumTime
    DWORD m_process_map_door_tick;                      // dwProcessMapDoorTick
    DWORD m_process_missions_time;                      // dwProcessMissionsTime
    DWORD m_regen_monsters_tick;                        // dwRegenMonstersTick
    DWORD m_calc_time;                                  // CalceTime
    DWORD m_process_load_play_tick;                     // m_dwProcessLoadPlayTick
    DWORD m_time_34;                                    // dwTime_34

    // Processing indices and counters
    int m_curr_mon_gen;                                 // m_nCurrMonGen
    int m_mon_gen_list_position;                        // m_nMonGenListPosition
    int m_mon_gen_cert_list_position;                   // m_nMonGenCertListPosition
    int m_proc_hum_idx;                                 // m_nProcHumIDx
    int m_process_human_loop_time;                      // nProcessHumanLoopTime
    int m_merchant_position;                            // nMerchantPosition
    int m_npc_position;                                 // nNpcPosition

    // Data lists - following original project structure
    std::list<std::shared_ptr<StdItem>> m_std_item_list;        // StdItemList
    std::list<std::shared_ptr<MonsterInfo>> m_monster_list;     // MonsterList
    std::list<std::shared_ptr<MonGenInfo>> m_mon_gen_list;      // m_MonGenList
    std::list<std::shared_ptr<BaseObject>> m_mon_free_list;     // m_MonFreeList
    std::list<std::shared_ptr<Magic>> m_magic_list;             // m_MagicList
    std::list<std::shared_ptr<Magic>> m_old_magic_list;         // m_OldMagicList
    std::list<std::shared_ptr<MapMonGenCount>> m_map_mon_gen_count_list;  // m_MapMonGenCountList
    std::list<std::shared_ptr<BaseObject>> m_admin_list;       // m_AdminList
    std::list<std::shared_ptr<Merchant>> m_merchant_list;      // m_MerchantList
    std::list<std::shared_ptr<NormNpc>> m_quest_npc_list;      // QuestNPCList
    std::list<std::shared_ptr<BaseObject>> m_list_70;          // List_70
    std::list<std::shared_ptr<BaseObject>> m_change_server_list;  // m_ChangeServerList
    std::list<std::shared_ptr<BaseObject>> m_magic_event_list; // m_MagicEventList
    std::list<std::shared_ptr<PlayObject>> m_play_object_level_list;  // m_PlayObjectLevelList

    // Monster processing
    int m_monster_count;                                // nMonsterCount
    int m_monster_process_position;                     // nMonsterProcessPostion
    int m_84;                                           // n84
    int m_monster_process_count;                        // nMonsterProcessCount
    bool m_item_event;                                  // boItemEvent
    int m_90;                                           // n90
    DWORD m_process_monsters_tick;                      // dwProcessMonstersTick
    DWORD m_process_merchant_time_min;                  // dwProcessMerchantTimeMin
    DWORD m_process_merchant_time_max;                  // dwProcessMerchantTimeMax
    DWORD m_process_npc_time_min;                       // dwProcessNpcTimeMin
    DWORD m_process_npc_time_max;                       // dwProcessNpcTimeMax

    // Additional lists
    std::list<std::shared_ptr<PlayObject>> m_new_human_list;   // m_NewHumanList
    std::list<int> m_list_of_gate_idx;                         // m_ListOfGateIdx
    std::list<int> m_list_of_socket;                           // m_ListOfSocket
    // std::list<std::shared_ptr<Magic>> m_old_magic_list;        // OldMagicList - temporarily removed

    // Limits and configuration
    int m_limit_user_count;                             // m_nLimitUserCount
    int m_limit_number;                                 // m_nLimitNumber
    bool m_start_load_magic;                            // m_boStartLoadMagic
    DWORD m_save_data_tick;                             // dwSaveDataTick
    DWORD m_search_tick;                                // m_dwSearchTick
    DWORD m_get_today_date_tick;                        // m_dwGetTodyDayDateTick
    std::chrono::system_clock::time_point m_today_date; // m_TodayDate

    // State management
    bool m_initialized;
    bool m_running;

    // Statistics
    std::atomic<int> m_online_user_count;
    std::atomic<int> m_total_user_count;
    int m_max_user_count;

    // Component references
    LocalDatabase* m_local_database;

    // Private methods (based on original UsrEngn.pas private methods)
    void ProcessHumans();
    void ProcessMonsters();
    void ProcessMerchants();
    void ProcessNpcs();
    void ProcessMissions();
    void Process4AECFC();
    void ProcessEvents();
    void ProcessMapDoor();
    void NPCInitialize();
    void MerchantInitialize();
    std::shared_ptr<PlayObject> MakeNewHuman(std::shared_ptr<UserOpenInfo> user_open_info);
    void ProcessLineNotices(std::shared_ptr<PlayObject> play_object, DWORD current_tick);
    int MonGetRandomItems(std::shared_ptr<BaseObject> mon);
    bool RegenMonsters(std::shared_ptr<MonGenInfo> mon_gen, int count);
    void WriteShiftUserData();
    int GetGenMonCount(std::shared_ptr<MonGenInfo> mon_gen);
    std::shared_ptr<BaseObject> AddBaseObject(const std::string& map_name, int x, int y, int mon_race, const std::string& mon_name);
    std::shared_ptr<BaseObject> AddPlayObject(std::shared_ptr<PlayObject> play_object, int x, int y, const std::string& mon_name);

    void GenShiftUserData();
    void KickOnlineUser(const std::string& chr_name);
    bool SendSwitchData(std::shared_ptr<PlayObject> play_object, int server_index);
    void SendChangeServer(std::shared_ptr<PlayObject> play_object, int server_index);
    void SaveHumanRcd(std::shared_ptr<PlayObject> play_object);
    void AddToHumanFreeList(std::shared_ptr<PlayObject> play_object);

    // void GetHumData(std::shared_ptr<PlayObject> play_object, HumDataInfo& human_rcd); // temporarily removed
    std::string GetHomeInfo(int& x, int& y);
    int GetRandHomeX(std::shared_ptr<PlayObject> play_object);
    int GetRandHomeY(std::shared_ptr<PlayObject> play_object);
    std::shared_ptr<BaseObject> GetSwitchData(const std::string& chr_name, int code);
    void LoadSwitchData(std::shared_ptr<BaseObject> switch_data, std::shared_ptr<PlayObject> play_object);
    void DelSwitchData(std::shared_ptr<BaseObject> switch_data);
    void MonInitialize(std::shared_ptr<BaseObject> base_object, const std::string& mon_name);
    bool MapRageHuman(const std::string& map_name, int map_x, int map_y, int rage);
    // Statistics access methods moved to public section

    // Message processing helper methods - temporarily removed for compilation
    // void ProcessQueryUserState(std::shared_ptr<PlayObject> play_object, DefaultMessage* def_msg, char* buff);
    // void ProcessQueryBagItems(std::shared_ptr<PlayObject> play_object, DefaultMessage* def_msg, char* buff);
    // void ProcessQueryUserSet(std::shared_ptr<PlayObject> play_object, DefaultMessage* def_msg, char* buff);
    // void ProcessItemUpgrade(std::shared_ptr<PlayObject> play_object, DefaultMessage* def_msg, char* buff);
    // void ProcessDropItem(std::shared_ptr<PlayObject> play_object, DefaultMessage* def_msg, char* buff);
    // void ProcessPickupItem(std::shared_ptr<PlayObject> play_object, DefaultMessage* def_msg, char* buff);
    // void ProcessSayMessage(std::shared_ptr<PlayObject> play_object, DefaultMessage* def_msg, char* buff);
    // void ProcessWalkMessage(std::shared_ptr<PlayObject> play_object, DefaultMessage* def_msg, char* buff);
    // void ProcessRunMessage(std::shared_ptr<PlayObject> play_object, DefaultMessage* def_msg, char* buff);
    // void ProcessHitMessage(std::shared_ptr<PlayObject> play_object, DefaultMessage* def_msg, char* buff);
    // void ProcessSpellMessage(std::shared_ptr<PlayObject> play_object, DefaultMessage* def_msg, char* buff);

    // Enhanced processing methods
    void ProcessMonsterRegeneration();
    void ProcessMerchantLogic(std::shared_ptr<Merchant> merchant);

    // String processing methods moved to public section

    // Message processing helper methods
    void ProcessQueryUserState(std::shared_ptr<PlayObject> play_object, DefaultMessage* def_msg, char* buff);
    void ProcessQueryBagItems(std::shared_ptr<PlayObject> play_object, DefaultMessage* def_msg, char* buff);
    void ProcessQueryUserSet(std::shared_ptr<PlayObject> play_object, DefaultMessage* def_msg, char* buff);
    void ProcessItemUpgrade(std::shared_ptr<PlayObject> play_object, DefaultMessage* def_msg, char* buff);
    void ProcessDropItem(std::shared_ptr<PlayObject> play_object, DefaultMessage* def_msg, char* buff);
    void ProcessPickupItem(std::shared_ptr<PlayObject> play_object, DefaultMessage* def_msg, char* buff);
    void ProcessSayMessage(std::shared_ptr<PlayObject> play_object, DefaultMessage* def_msg, char* buff);
    void ProcessWalkMessage(std::shared_ptr<PlayObject> play_object, DefaultMessage* def_msg, char* buff);
    void ProcessRunMessage(std::shared_ptr<PlayObject> play_object, DefaultMessage* def_msg, char* buff);
    void ProcessHitMessage(std::shared_ptr<PlayObject> play_object, DefaultMessage* def_msg, char* buff);
    void ProcessSpellMessage(std::shared_ptr<PlayObject> play_object, DefaultMessage* def_msg, char* buff);

    // Statistics and monitoring
    void GetServerStatistics(ServerStatistics& stats);
    int CalculateMemoryUsage();

    // Maintenance and cleanup
    void PerformMaintenance();
    void CompactDataStructures();
    void UpdateStatistics();
    void SaveCriticalData();

    // Enhanced monster and merchant processing - methods already declared above

public:
    // Constructor and destructor
    UserEngine();
    ~UserEngine();

    // Core lifecycle methods (based on original structure)
    bool Initialize();
    void Finalize();
    void ClearItemList();
    void SwitchMagicList();

    // Main processing methods
    void Run();
    void ProcessData();
    void Execute();

    // Statistics methods (public access)
    int GetOnlineHumCount();
    int GetUserCount();
    int GetLoadPlayCount();
    int GetAutoAddExpPlayCount();

    // String processing methods (public access)
    std::string DecodeString(const std::string& encoded_str);
    std::string EncodeString(const std::string& plain_str);

    // Monster management
    std::shared_ptr<BaseObject> RegenMonsterByName(const std::string& map, int x, int y, const std::string& mon_name);
    std::shared_ptr<BaseObject> RegenPlayByName(std::shared_ptr<PlayObject> play_object, int x, int y, const std::string& mon_name);

    // Item management - temporarily removed for compilation
    // std::shared_ptr<StdItem> GetStdItem(int item_idx);
    // std::shared_ptr<StdItem> GetStdItem(const std::string& item_name);
    // int GetStdItemWeight(int item_idx);
    // std::string GetStdItemName(int item_idx);
    // int GetStdItemIdx(const std::string& item_name);

    // Player management
    bool FindOtherServerUser(const std::string& name, int& server_index);
    void CryCry(WORD ident, std::shared_ptr<Environment> map, int x, int y, int wide, BYTE ft_color, BYTE b_color, const std::string& msg);
    void ProcessUserMessage(std::shared_ptr<PlayObject> play_object, DefaultMessage* def_msg, char* buff);
    void SendServerGroupMsg(int code, int server_idx, const std::string& msg);
    int GetMonRace(const std::string& mon_name);
    bool InsMonstersList(std::shared_ptr<MonGenInfo> mon_gen, std::shared_ptr<AnimalObject> monster);
    std::shared_ptr<PlayObject> GetPlayObject(const std::string& name);
    std::shared_ptr<PlayObject> GetPlayObjectEx(const std::string& account, const std::string& name);
    std::shared_ptr<PlayObject> GetPlayObjectExOfAutoGetExp(const std::string& account);
    bool InPlayObjectList(std::shared_ptr<PlayObject> play_object);
    void KickPlayObjectEx(const std::string& account, const std::string& name);

    // NPC and Merchant management
    std::shared_ptr<Merchant> FindMerchant(std::shared_ptr<BaseObject> merchant);
    std::shared_ptr<NormNpc> FindNPC(std::shared_ptr<BaseObject> guild_official);
    bool InMerchantList(std::shared_ptr<Merchant> merchant);
    bool InQuestNPCList(std::shared_ptr<NormNpc> npc);
    // bool CopyToUserItemFromName(const std::string& item_name, UserItem* item); // temporarily removed
    int GetMapOfRangeHumanCount(std::shared_ptr<Environment> envir, int x, int y, int range);
    bool GetHumPermission(const std::string& user_name, std::string& ip_addr, BYTE& permission);
    void AddUserOpenInfo(std::shared_ptr<UserOpenInfo> user_open_info);
    // void RandomUpgradeItem(UserItem* item); // temporarily removed
    // void GetUnknowItemValue(UserItem* item); // temporarily removed

    // Map and door management
    bool OpenDoor(std::shared_ptr<Environment> envir, int x, int y);
    bool CloseDoor(std::shared_ptr<Environment> envir, std::shared_ptr<BaseObject> door);
    void SendDoorStatus(std::shared_ptr<Environment> envir, int x, int y, WORD ident, WORD w_x, int door_x, int door_y, int a, const std::string& str);

    // Magic management - temporarily removed for compilation
    // std::shared_ptr<Magic> FindMagic(const std::string& magic_name);
    // std::shared_ptr<Magic> FindMagic(int mag_idx);
    // bool AddMagic(std::shared_ptr<Magic> magic);
    // bool DelMagic(WORD magic_id);

    // Merchant and NPC lists
    void AddMerchant(std::shared_ptr<Merchant> merchant);
    int GetMerchantList(std::shared_ptr<Environment> envir, int x, int y, int range, std::list<std::shared_ptr<Merchant>>& tmp_list);
    int GetNpcList(std::shared_ptr<Environment> envir, int x, int y, int range, std::list<std::shared_ptr<NormNpc>>& tmp_list);
    void ReloadMerchantList();
    void ReloadNpcList();
    void HumanExpire(const std::string& account);

    // Monster and map management
    int GetMapMonster(std::shared_ptr<Environment> envir, std::list<std::shared_ptr<BaseObject>>& list);
    int GetMapRangeMonster(std::shared_ptr<Environment> envir, int x, int y, int range, std::list<std::shared_ptr<BaseObject>>& list);
    int GetMapHuman(const std::string& map_name);
    int GetMapRageHuman(std::shared_ptr<Environment> envir, int rage_x, int rage_y, int rage, std::list<std::shared_ptr<PlayObject>>& list);

    // Broadcasting and messaging - temporarily simplified
    void SendBroadCastMsg(const std::string& msg, int msg_type);
    void SendBroadCastMsgExt(const std::string& msg, int msg_type);
    void sub_4AE514(std::shared_ptr<GoldChangeInfo> gold_change_info);
    void ClearMonSayMsg();
    void SendQuestMsg(const std::string& quest_name);
    void ClearMerchantData();

    // Map monster generation
    std::shared_ptr<MapMonGenCount> GetMapMonGenCount(const std::string& map_name);
    int AddMapMonGenCount(const std::string& map_name, int mon_gen_count);
    bool ClearMonsters(const std::string& map_name);

    // Properties (based on original property declarations)
    int GetMonsterCount() const { return m_monster_count; }
    int GetOnlinePlayObjectCount() const { return m_online_user_count.load(); }
    int GetPlayObjectCount() const { return m_total_user_count.load(); }
    int GetAutoAddExpPlayObjectCount() const;
    int GetLoadPlayObjectCount() const { return static_cast<int>(m_load_play_list.size()); }

    // State management
    bool IsInitialized() const { return m_initialized; }
    bool IsRunning() const { return m_running; }
    bool Start();
    void Stop();
    void SaveAllUsers();

    // Additional methods for M2Server compatibility
    void ProcessUsers() { ProcessHumans(); }  // Alias for ProcessHumans
    void CleanupExpiredSessions();
    void EmergencyStop();

    // Server state notifications
    void OnServerStateChanged(ServerState new_state);

    // Component management
    void SetLocalDatabase(LocalDatabase* local_database) { m_local_database = local_database; }
    LocalDatabase* GetLocalDatabase() const { return m_local_database; }

    // Statistics access methods already declared in public section above
};
