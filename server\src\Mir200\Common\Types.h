#pragma once

#include <cstring>
#include <array>
#include <memory>
#include <chrono>
#include <cstdlib>

#ifdef _WIN32
#include <windows.h>
#endif

// Basic types
typedef unsigned char BYTE;
typedef unsigned short WORD;
typedef unsigned long DWORD;

// Direction constants (matching original project)
const BYTE DR_UP = 0;
const BYTE DR_UPRIGHT = 1;
const BYTE DR_RIGHT = 2;
const BYTE DR_DOWNRIGHT = 3;
const BYTE DR_DOWN = 4;
const BYTE DR_DOWNLEFT = 5;
const BYTE DR_LEFT = 6;
const BYTE DR_UPLEFT = 7;

// Point structure
struct Point {
    int x;
    int y;
    
    Point() : x(0), y(0) {}
    Point(int x_, int y_) : x(x_), y(y_) {}
};

// TUserItem structure (matching TUserItem from Grobal2.pas line 1181-1187)
struct TUserItem {
    int MakeIndex;
    WORD wIndex;
    WORD Dura;
    WORD DuraMax;
    BYTE btValue[14];

    TUserItem() {
        MakeIndex = 0;
        wIndex = 0;
        Dura = 0;
        DuraMax = 0;
        std::memset(btValue, 0, sizeof(btValue));
    }
};

// TDefaultMessage structure (matching TDefaultMessage from Grobal2.pas)
struct TDefaultMessage {
    int Recog;              // 识别码 (Integer in Delphi)
    WORD Ident;             // 消息标识
    WORD Param;             // 参数
    WORD tag;               // 标签
    WORD Series;            // 序列号

    TDefaultMessage() {
        Recog = 0;
        Ident = 0;
        Param = 0;
        tag = 0;
        Series = 0;
    }
};

// Compatibility alias
using DefaultMessage = TDefaultMessage;

// Utility functions
inline DWORD GetCurrentTime() {
#ifdef _WIN32
    return static_cast<DWORD>(GetTickCount());
#else
    return static_cast<DWORD>(std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::steady_clock::now().time_since_epoch()).count());
#endif
}

inline int Random(int max) {
    return rand() % max;
}

inline int Random(int min, int max) {
    return min + (rand() % (max - min + 1));
}
