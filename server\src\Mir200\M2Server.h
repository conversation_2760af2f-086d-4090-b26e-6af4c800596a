#pragma once

// Mir200 M2Server - Main server class
// Based on delphi/EM2Engine/M2Server.dpr and related files
// Manages the entire server lifecycle and core components

#include "Common/SimpleTypes.h"
#include <thread>
#include <atomic>
#include <mutex>
#include <vector>
#include <unordered_map>
#include <string>
#include <memory>
#include <condition_variable>

// Forward declarations
class Environment;
class Guild;
class Castle;

// Server state enumeration
enum class ServerState : BYTE {
    STOPPED = 0,
    INITIALIZING = 1,
    STARTING = 2,
    RUNNING = 3,
    STOPPING = 4,
    SERVER_ERROR = 5  // Renamed to avoid conflict with Windows ERROR macro
};

// Forward declaration - ServerStatistics is defined in UserEngine.h
struct ServerStatistics;

// M2Server class - Main server controller
class M2Server {
private:
    // Server state
    std::atomic<ServerState> m_server_state;
    std::atomic<bool> m_shutdown_requested;
    std::atomic<bool> m_restart_requested;
    
    // Core components (matching original M2Server structure) - temporarily disabled
    // std::unique_ptr<UserEngine> m_user_engine;
    // std::unique_ptr<LocalDatabase> m_local_database;
    // std::unique_ptr<RunSocket> m_run_socket;
    // std::unique_ptr<GateSocket> m_gate_socket;

    // Environment management - temporarily disabled
    // std::vector<std::shared_ptr<Environment>> m_environments;
    // mutable std::mutex m_env_mutex;

    // Guild and castle management - temporarily disabled
    // std::unordered_map<std::string, std::shared_ptr<Guild>> m_guilds;
    // std::shared_ptr<Castle> m_castle;
    // mutable std::mutex m_guild_mutex;
    
    // Threading
    bool m_is_running;
    std::atomic<bool> m_main_thread_running;
    std::atomic<bool> m_network_thread_running;
    std::atomic<bool> m_database_thread_running;
    std::atomic<bool> m_timer_thread_running;
    std::thread m_main_thread;
    std::thread m_network_thread;
    std::thread m_database_thread;
    std::thread m_timer_thread;
    std::mutex m_server_mutex;
    std::condition_variable m_server_cv;
    
    // Configuration
    std::string m_config_file;
    std::string m_server_path;
    std::string m_data_path;
    std::string m_log_path;
    
    // Timing and performance
    DWORD m_last_process_time;
    DWORD m_process_interval;
    DWORD m_save_interval;
    DWORD m_last_save_time;
    DWORD m_start_time;

    // Server configuration
    std::string m_server_name;
    int m_max_user;
    bool m_test_server;
    bool m_service_mode;

    // Statistics - temporarily simplified
    // ServerStatistics m_statistics;
    std::mutex m_stats_mutex;
    int m_online_user_count;
    int m_total_user_count;
    int m_environment_count;
    int m_guild_count;
    DWORD m_server_uptime;
    DWORD m_memory_usage;

    // Error handling
    std::string m_last_error;
    int m_error_count;
    DWORD m_last_error_time;

public:
    M2Server();
    ~M2Server();

    // Core server lifecycle (matching original M2Server methods)
    bool Initialize(const std::string& config_file);
    bool Start();
    void Run();
    void Stop();
    void Finalize();
    
    // Server state management
    ServerState GetServerState() const { return m_server_state.load(); }
    bool IsRunning() const { return m_server_state.load() == ServerState::RUNNING; }
    bool IsStopping() const { return m_server_state.load() == ServerState::STOPPING; }
    bool IsShutdownRequested() const { return m_shutdown_requested.load(); }
    bool IsRestartRequested() const { return m_restart_requested.load(); }
    
    // Server control
    void RequestShutdown() { m_shutdown_requested.store(true); }
    void RequestRestart() { m_restart_requested.store(true); }
    void EmergencyStop();
    
    // Component accessors - temporarily disabled
    // UserEngine* GetUserEngine() const { return m_user_engine.get(); }
    // LocalDatabase* GetLocalDatabase() const { return m_local_database.get(); }
    // RunSocket* GetRunSocket() const { return m_run_socket.get(); }
    // GateSocket* GetGateSocket() const { return m_gate_socket.get(); }
    
    // Environment management - temporarily disabled
    // std::shared_ptr<Environment> GetEnvironment(const MapName& map_name);
    // std::shared_ptr<Environment> CreateEnvironment(const MapName& map_name);
    // void RemoveEnvironment(const MapName& map_name);
    // std::vector<std::shared_ptr<Environment>> GetAllEnvironments();
    int GetEnvironmentCount() const;
    
    // Guild management - temporarily disabled
    // std::shared_ptr<Guild> GetGuild(const std::string& guild_name);
    // std::shared_ptr<Guild> CreateGuild(const std::string& guild_name);
    // void RemoveGuild(const std::string& guild_name);
    // std::vector<std::shared_ptr<Guild>> GetAllGuilds();
    int GetGuildCount() const;

    // Castle management - temporarily disabled
    // std::shared_ptr<Castle> GetCastle() const { return m_castle; }
    // void SetCastle(std::shared_ptr<Castle> castle) { m_castle = castle; }
    
    // Configuration management
    const std::string& GetConfigFile() const { return m_config_file; }
    const std::string& GetServerPath() const { return m_server_path; }
    const std::string& GetDataPath() const { return m_data_path; }
    const std::string& GetLogPath() const { return m_log_path; }
    
    bool LoadConfiguration();
    bool SaveConfiguration();
    bool ReloadConfiguration();
    
    // Statistics and monitoring - temporarily disabled
    // const ServerStatistics& GetStatistics() const;
    void UpdateStatistics();
    void ResetStatistics();
    
    // User management
    int GetCurrentUserCount() const;
    int GetMaxUserCount() const;
    void SetMaxUserCount(int max_users);
    
    // Message broadcasting - temporarily simplified
    void BroadcastMessage(const std::string& message, int type = 0);
    void BroadcastNotice(const std::string& notice);
    void SendSystemMessage(const std::string& message);
    
    // Server maintenance
    void SaveServerData();
    void BackupServerData();
    void CleanupExpiredData();
    void OptimizeDatabase();
    
    // Error handling
    const std::string& GetLastError() const { return m_last_error; }
    int GetErrorCount() const { return m_error_count; }
    void SetLastError(const std::string& error);
    void ClearErrors();
    
    // Performance monitoring
    DWORD GetUptime() const;
    double GetCPUUsage() const;
    size_t GetMemoryUsage() const;
    int GetNetworkLoad() const;
    
    // Debug and testing
    void DumpServerState() const;
    void DumpStatistics() const;
    void RunDiagnostics();
    bool ValidateServerState() const;

private:
    // Internal initialization
    bool InitializeComponents();
    bool InitializeNetwork();
    bool InitializeDatabase();
    bool InitializeEnvironments();
    bool InitializeGuilds();
    bool InitializeCastle();
    
    // Internal finalization
    void FinalizeComponents();
    void FinalizeNetwork();
    void FinalizeDatabase();
    void FinalizeEnvironments();
    void FinalizeGuilds();
    void FinalizeCastle();
    
    // Thread management
    void StartThreads();
    void StopThreads();
    void MainThreadProc();
    void NetworkThreadProc();
    void DatabaseThreadProc();
    void TimerThreadProc();
    
    // Internal processing
    void ProcessServerTick();
    void ProcessNetworkMessages();
    void ProcessDatabaseOperations();
    void ProcessTimerEvents();
    void ProcessMaintenanceTasks();
    void ProcessEnvironments();
    void ProcessGuilds();
    void ProcessCastle();
    void ProcessScheduledEvents();
    
    // Configuration helpers
    bool LoadServerConfig();
    bool LoadDatabaseConfig();
    bool LoadNetworkConfig();
    bool LoadGameConfig();
    bool ValidateConfiguration() const;
    
    // Path management
    void InitializePaths();
    bool CreateDirectories();
    bool ValidatePaths() const;
    
    // Error handling internals
    void HandleCriticalError(const std::string& error);
    void HandleWarning(const std::string& warning);
    void LogError(const std::string& error);
    void LogWarning(const std::string& warning);
    void LogInfo(const std::string& info);
    
    // Statistics helpers
    void UpdateUserStatistics();
    void UpdateNetworkStatistics();
    void UpdatePerformanceStatistics();
    void UpdateServerStatistics();
    
    // Maintenance helpers
    void PerformHourlyMaintenance();
    void PerformDailyMaintenance();
    void PerformWeeklyMaintenance();
    void CheckSystemResources();
    void CleanupEnvironments();
    void CleanupLogFiles();
    void SaveGuildData();
    void SaveCastleData();
    void SaveEnvironmentData();
    
    // Validation helpers
    bool ValidateComponents() const;
    bool ValidateNetwork() const;
    bool ValidateDatabase() const;
    bool ValidateEnvironments() const;
    
    // Utility methods
    void SetServerState(ServerState state);
    void NotifyStateChange(ServerState old_state, ServerState new_state);
    void WaitForShutdown();
    bool ShouldContinueRunning() const;
    std::string ServerStateToString(ServerState state) const;
    
    // Signal and event handling - temporarily disabled
    // void OnUserLogin(PlayObject* player);
    // void OnUserLogout(PlayObject* player);
    // void OnUserMessage(PlayObject* player, const std::string& message);
    void OnSystemEvent(const std::string& event);
    void OnCriticalError(const std::string& error);
    
    // Resource management
    void MonitorMemoryUsage();
    void MonitorCPUUsage();
    void MonitorNetworkUsage();
    void CleanupResources();
    
    // Security and anti-cheat - temporarily disabled
    void ProcessSecurityChecks();
    void ValidateUserConnections();
    void DetectAbnormalActivity();
    // void HandleSecurityViolation(PlayObject* player, const std::string& violation);
};

// Global server utilities
namespace ServerUtils {
    // Server management
    bool IsServerRunning();
    M2Server* GetServerInstance();
    
    // Configuration utilities
    std::string GetServerConfigPath();
    std::string GetServerDataPath();
    std::string GetServerLogPath();
    
    // System utilities
    std::string GetSystemInfo();
    std::string GetServerVersion();
    DWORD GetServerUptime();
    
    // Network utilities
    std::string GetLocalIPAddress();
    int GetAvailablePort(int start_port);
    bool IsPortAvailable(int port);
    
    // File and directory utilities
    bool CreateServerDirectories();
    bool BackupServerFiles();
    bool CleanupTempFiles();
    
    // Performance utilities
    double GetSystemCPUUsage();
    size_t GetSystemMemoryUsage();
    size_t GetAvailableMemory();
    
    // Validation utilities
    bool ValidateServerEnvironment();
    bool ValidateSystemRequirements();
    bool ValidateNetworkConfiguration();
}
