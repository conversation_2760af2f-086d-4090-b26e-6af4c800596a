#pragma once

#include "IManager.h"
#include "../Common/Types.h"
#include <string>
#include <vector>
#include <memory>
#include <unordered_map>
#include <shared_mutex>

// 数据结构定义 - 基于原项目Grobal2.pas中的定义

// TStdItem结构 (对应原项目Grobal2.pas line 866-886)
struct StdItem {
    std::string Name;       // 对应原项目的Name: string[14]
    BYTE StdMode;          // 对应原项目的StdMode: Byte
    BYTE Shape;            // 对应原项目的Shape: Byte
    BYTE Weight;           // 对应原项目的Weight: Byte
    BYTE AniCount;         // 对应原项目的AniCount: Byte
    char Source;           // 对应原项目的Source: ShortInt
    BYTE Reserved;         // 对应原项目的Reserved: Byte
    BYTE NeedIdentify;     // 对应原项目的NeedIdentify: Byte
    WORD Looks;            // 对应原项目的Looks: Word
    WORD DuraMax;          // 对应原项目的DuraMax: Word
    WORD Reserved1;        // 对应原项目的Reserved1: Word
    int AC;                // 对应原项目的AC: Integer
    int MAC;               // 对应原项目的MAC: Integer
    int DC;                // 对应原项目的DC: Integer
    int MC;                // 对应原项目的MC: Integer
    int SC;                // 对应原项目的SC: Integer
    int Need;              // 对应原项目的Need: Integer
    int NeedLevel;         // 对应原项目的NeedLevel: Integer
    int Price;             // 对应原项目的Price: Integer

    StdItem() : StdMode(0), Shape(0), Weight(0), AniCount(0), Source(0),
               Reserved(0), NeedIdentify(0), Looks(0), DuraMax(0), Reserved1(0),
               AC(0), MAC(0), DC(0), MC(0), SC(0), Need(0), NeedLevel(0), Price(0) {}
};

// TMagic结构 (对应原项目Grobal2.pas line 956-976)
struct Magic {
    WORD wMagicId;                    // 对应原项目的wMagicId: Word
    std::string sMagicName;           // 对应原项目的sMagicName: string[12]
    BYTE btEffectType;                // 对应原项目的btEffectType: Byte
    BYTE btEffect;                    // 对应原项目的btEffect: Byte
    BYTE bt11;                        // 对应原项目的bt11: Byte
    WORD wSpell;                      // 对应原项目的wSpell: Word
    WORD wPower;                      // 对应原项目的wPower: Word
    BYTE TrainLevel[4];               // 对应原项目的TrainLevel: array[0..3] of Byte
    WORD w02;                         // 对应原项目的w02: Word
    int MaxTrain[4];                  // 对应原项目的MaxTrain: array[0..3] of Integer
    BYTE btTrainLv;                   // 对应原项目的btTrainLv: Byte
    BYTE btJob;                       // 对应原项目的btJob: Byte
    WORD wMagicIdx;                   // 对应原项目的wMagicIdx: Word
    DWORD dwDelayTime;                // 对应原项目的dwDelayTime: LongWord
    BYTE btDefSpell;                  // 对应原项目的btDefSpell: Byte
    BYTE btDefPower;                  // 对应原项目的btDefPower: Byte
    WORD wMaxPower;                   // 对应原项目的wMaxPower: Word
    BYTE btDefMaxPower;               // 对应原项目的btDefMaxPower: Byte
    std::string sDescr;               // 对应原项目的sDescr: string[18]

    Magic() : wMagicId(0), btEffectType(0), btEffect(0), bt11(0), wSpell(0),
             wPower(0), w02(0), btTrainLv(0), btJob(0), wMagicIdx(0),
             dwDelayTime(0), btDefSpell(0), btDefPower(0), wMaxPower(0), btDefMaxPower(0) {
        std::memset(TrainLevel, 0, sizeof(TrainLevel));
        std::memset(MaxTrain, 0, sizeof(MaxTrain));
    }
};

// TMonsterInfo结构 (对应原项目Grobal2.pas line 1198-1202)
struct MonsterInfo {
    std::string Name;       // 对应原项目的Name: string
    // ItemList在C++中用vector替代TList
    std::vector<void*> ItemList;  // 对应原项目的ItemList: TList

    MonsterInfo() {}
};

// TMerchantInfo结构 (对应原项目Grobal2.pas line 1157-1167) - 用作NPCInfo
struct NPCInfo {
    std::string sScript;    // 对应原项目的sScript: string[14]
    std::string sMapName;   // 对应原项目的sMapName: string[14]
    int nX;                 // 对应原项目的nX: Integer
    int nY;                 // 对应原项目的nY: Integer
    std::string sNPCName;   // 对应原项目的sNPCName: string[40]
    int nFace;              // 对应原项目的nFace: Integer
    int nBody;              // 对应原项目的nBody: Integer
    bool boCastle;          // 对应原项目的boCastle: Boolean

    NPCInfo() : nX(0), nY(0), nFace(0), nBody(0), boCastle(false) {}
};

// TMapInfo结构 (对应原项目Grobal2.pas line 1012-1035)
struct MapInfo {
    std::string sName;              // 对应原项目的sName: string
    std::string sMapNO;             // 对应原项目的sMapNO: string
    int nL;                         // 对应原项目的nL: Integer
    int nServerIndex;               // 对应原项目的nServerIndex: Integer
    int nNEEDONOFFFlag;            // 对应原项目的nNEEDONOFFFlag: Integer
    bool boNEEDONOFFFlag;          // 对应原项目的boNEEDONOFFFlag: Boolean
    std::string sShowName;          // 对应原项目的sShowName: string
    std::string sReConnectMap;      // 对应原项目的sReConnectMap: string
    bool boSAFE;                   // 对应原项目的boSAFE: Boolean
    bool boDARK;                   // 对应原项目的boDARK: Boolean
    bool boFIGHT;                  // 对应原项目的boFIGHT: Boolean
    bool boFIGHT3;                 // 对应原项目的boFIGHT3: Boolean
    bool boDAY;                    // 对应原项目的boDAY: Boolean
    bool boQUIZ;                   // 对应原项目的boQUIZ: Boolean
    bool boNORECONNECT;           // 对应原项目的boNORECONNECT: Boolean
    bool boNEEDHOLE;              // 对应原项目的boNEEDHOLE: Boolean
    bool boNORECALL;              // 对应原项目的boNORECALL: Boolean
    bool boNORANDOMMOVE;          // 对应原项目的boNORANDOMMOVE: Boolean
    bool boNODRUG;                // 对应原项目的boNODRUG: Boolean
    bool boMINE;                  // 对应原项目的boMINE: Boolean
    bool boNOPOSITIONMOVE;        // 对应原项目的boNOPOSITIONMOVE: Boolean

    MapInfo() : nL(0), nServerIndex(0), nNEEDONOFFFlag(0), boNEEDONOFFFlag(false),
               boSAFE(false), boDARK(false), boFIGHT(false), boFIGHT3(false),
               boDAY(false), boQUIZ(false), boNORECONNECT(false), boNEEDHOLE(false),
               boNORECALL(false), boNORANDOMMOVE(false), boNODRUG(false),
               boMINE(false), boNOPOSITIONMOVE(false) {}
};

// 玩家数据结构 (简化版本，用于数据保存/加载)
struct PlayerData {
    std::string account;
    std::string charName;
    int level;
    int job;
    int gender;
    // 这里应该包含TAbility等结构，暂时简化

    PlayerData() : level(1), job(0), gender(0) {}
};

/**
 * @brief 数据库连接配置
 */
struct DatabaseConfig {
    std::string connectionString;
    std::string databaseType; // "FILE", "SQLITE", "MYSQL", etc.
    std::string dataDirectory;
    int connectionTimeout;
    int queryTimeout;
    bool enableCache;
    
    DatabaseConfig() : databaseType("FILE"), dataDirectory("data/"),
                      connectionTimeout(30), queryTimeout(30), enableCache(true) {}
};

/**
 * @brief 数据库事务类
 */
class DatabaseTransaction {
private:
    bool m_active;
    bool m_committed;
    
public:
    DatabaseTransaction() : m_active(true), m_committed(false) {}
    ~DatabaseTransaction() {
        if (m_active && !m_committed) {
            Rollback();
        }
    }
    
    bool Commit();
    bool Rollback();
    bool IsActive() const { return m_active; }
    bool IsCommitted() const { return m_committed; }
};

/**
 * @brief 数据库管理器
 * 负责统一管理所有数据访问
 */
class DatabaseManager : public IManager {
private:
    DatabaseConfig m_config;
    std::string m_managerName;
    
    // 数据缓存
    std::vector<std::unique_ptr<StdItem>> m_stdItems;
    std::vector<std::unique_ptr<Magic>> m_magics;
    std::unordered_map<std::string, std::unique_ptr<MonsterInfo>> m_monsters;
    std::unordered_map<std::string, std::unique_ptr<NPCInfo>> m_npcs;
    std::unordered_map<std::string, std::unique_ptr<MapInfo>> m_maps;
    
    mutable std::shared_mutex m_dataMutex;
    
    // 统计信息
    std::atomic<uint64_t> m_totalQueries;
    std::atomic<uint64_t> m_cacheHits;
    std::atomic<uint64_t> m_cacheMisses;
    
public:
    DatabaseManager();
    virtual ~DatabaseManager();
    
    // IManager接口实现
    bool Initialize() override;
    void Finalize() override;
    void Update() override;
    const std::string& GetManagerName() const override;
    
    // 配置管理
    void SetDatabaseConfig(const DatabaseConfig& config);
    const DatabaseConfig& GetDatabaseConfig() const;
    
    // 数据加载接口
    bool LoadStdItems();
    bool LoadMagicData();
    bool LoadMonsterData();
    bool LoadNPCData();
    bool LoadMapData();
    bool LoadAllData();
    
    // 数据查询接口
    const StdItem* GetStdItem(int itemIndex) const;
    const Magic* GetMagic(int magicId) const;
    const MonsterInfo* GetMonsterInfo(const std::string& monsterName) const;
    const NPCInfo* GetNPCInfo(const std::string& npcName) const;
    const MapInfo* GetMapInfo(const std::string& mapName) const;
    
    // 数据保存接口
    bool SavePlayerData(const std::string& account, const std::string& charName, const class PlayerData& data);
    bool LoadPlayerData(const std::string& account, const std::string& charName, class PlayerData& data);
    
    // 事务支持
    std::unique_ptr<DatabaseTransaction> BeginTransaction();
    
    // 缓存管理
    void ClearCache();
    void RefreshCache();
    
    // 统计信息
    uint64_t GetTotalQueries() const { return m_totalQueries; }
    uint64_t GetCacheHits() const { return m_cacheHits; }
    uint64_t GetCacheMisses() const { return m_cacheMisses; }
    double GetCacheHitRatio() const;
    
private:
    // 文件数据加载
    bool LoadStdItemsFromFile(const std::string& fileName);
    bool LoadMagicFromFile(const std::string& fileName);
    bool LoadMonsterFromFile(const std::string& fileName);
    bool LoadNPCFromFile(const std::string& fileName);
    bool LoadMapFromFile(const std::string& fileName);
    
    // 数据验证
    bool ValidateStdItem(const StdItem& item) const;
    bool ValidateMagic(const Magic& magic) const;
    bool ValidateMonster(const MonsterInfo& monster) const;
    
    // 缓存操作
    template<typename T>
    const T* GetFromCache(const std::unordered_map<std::string, std::unique_ptr<T>>& cache, 
                         const std::string& key) const;
    
    template<typename T>
    const T* GetFromCache(const std::vector<std::unique_ptr<T>>& cache, int index) const;
    
    // 文件路径获取
    std::string GetDataFilePath(const std::string& fileName) const;
    
    // 错误处理
    void LogDatabaseError(const std::string& operation, const std::string& error) const;
};

// 模板函数实现
template<typename T>
const T* DatabaseManager::GetFromCache(const std::unordered_map<std::string, std::unique_ptr<T>>& cache, 
                                       const std::string& key) const {
    ++m_totalQueries;
    
    std::shared_lock<std::shared_mutex> lock(m_dataMutex);
    auto it = cache.find(key);
    if (it != cache.end()) {
        ++m_cacheHits;
        return it->second.get();
    }
    
    ++m_cacheMisses;
    return nullptr;
}

template<typename T>
const T* DatabaseManager::GetFromCache(const std::vector<std::unique_ptr<T>>& cache, int index) const {
    ++m_totalQueries;
    
    std::shared_lock<std::shared_mutex> lock(m_dataMutex);
    if (index >= 0 && index < static_cast<int>(cache.size()) && cache[index]) {
        ++m_cacheHits;
        return cache[index].get();
    }
    
    ++m_cacheMisses;
    return nullptr;
}
