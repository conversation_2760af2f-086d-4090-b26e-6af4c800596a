# CMakeLists.txt for Game Logic Manager Implementation
# 游戏逻辑Manager实现的CMake配置文件

cmake_minimum_required(VERSION 3.16)
project(Mir200_GameLogicManager)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置编译选项
if(MSVC)
    add_compile_options(/W3 /bigobj)
    add_definitions(-D_CRT_SECURE_NO_WARNINGS)
    add_definitions(-DNOMINMAX)
    add_definitions(-DWIN32_LEAN_AND_MEAN)
else()
    add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# 包含目录
include_directories(${CMAKE_CURRENT_SOURCE_DIR})
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/Common)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/Managers)

# 游戏逻辑Manager源文件
set(GAME_LOGIC_MANAGER_SOURCES
    # 基础Manager
    Managers/GameConfigManager.cpp
    Managers/LogManager.cpp
    Managers/DatabaseManager.cpp
    Managers/EventBus.cpp
    Managers/ServiceContainer.cpp
    
    # 系统服务Manager
    Managers/EventManager.cpp
    Managers/SaveDataManager.cpp
    Managers/NetworkManager.cpp
    
    # 高级功能Manager
    Managers/ScriptManager.cpp
    Managers/TaskScheduler.cpp
    Managers/SystemMonitor.cpp
    
    # 游戏逻辑Manager
    Managers/ItemManager.cpp
    Managers/MapManager.cpp
    Managers/NPCManager.cpp
    Managers/MonsterManager.cpp
    Managers/MagicManager.cpp
    
    # 通用源文件
    Common/M2Share.cpp
)

# 游戏逻辑Manager头文件
set(GAME_LOGIC_MANAGER_HEADERS
    # 基础Manager
    Managers/IManager.h
    Managers/EventData.h
    Managers/GameConfigManager.h
    Managers/LogManager.h
    Managers/DatabaseManager.h
    Managers/EventBus.h
    Managers/ServiceContainer.h
    
    # 系统服务Manager
    Managers/EventManager.h
    Managers/SaveDataManager.h
    Managers/NetworkManager.h
    
    # 高级功能Manager
    Managers/ScriptManager.h
    Managers/TaskScheduler.h
    Managers/SystemMonitor.h
    
    # 游戏逻辑Manager
    Managers/ItemManager.h
    Managers/MapManager.h
    Managers/NPCManager.h
    Managers/MonsterManager.h
    Managers/MagicManager.h
    
    # 通用头文件
    Common/Types.h
    Common/M2Share.h
    Common/Protocol.h
    Common/GameStructs.h
    Common/SimpleTypes.h
)

# 测试源文件
set(TEST_SOURCES
    Tests/GameLogicManagerTest.cpp
)

# 创建游戏逻辑Manager库
add_library(GameLogicManager STATIC ${GAME_LOGIC_MANAGER_SOURCES} ${GAME_LOGIC_MANAGER_HEADERS})

# 设置库的包含目录
target_include_directories(GameLogicManager PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/Common
    ${CMAKE_CURRENT_SOURCE_DIR}/Managers
)

# 链接线程库
find_package(Threads REQUIRED)
target_link_libraries(GameLogicManager Threads::Threads)

# 创建测试可执行文件
add_executable(GameLogicManagerTest ${TEST_SOURCES})
target_link_libraries(GameLogicManagerTest GameLogicManager)

# 设置输出目录
set_target_properties(GameLogicManager PROPERTIES
    ARCHIVE_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/lib"
    LIBRARY_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/lib"
)

set_target_properties(GameLogicManagerTest PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
)

# 安装规则
install(TARGETS GameLogicManager
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
)

install(FILES ${GAME_LOGIC_MANAGER_HEADERS}
    DESTINATION include/Mir200/Managers
)

install(TARGETS GameLogicManagerTest
    RUNTIME DESTINATION bin
)

# 编译信息
message(STATUS "=== Game Logic Manager Configuration ===")
message(STATUS "Project: ${PROJECT_NAME}")
message(STATUS "C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "Source Files: ${GAME_LOGIC_MANAGER_SOURCES}")
message(STATUS "Header Files: ${GAME_LOGIC_MANAGER_HEADERS}")
message(STATUS "Test Files: ${TEST_SOURCES}")

# 自定义目标：编译所有
add_custom_target(build_all
    DEPENDS GameLogicManager GameLogicManagerTest
    COMMENT "Building all Game Logic Manager components"
)

# 自定义目标：运行测试
add_custom_target(run_tests
    COMMAND GameLogicManagerTest
    DEPENDS GameLogicManagerTest
    WORKING_DIRECTORY ${CMAKE_BINARY_DIR}/bin
    COMMENT "Running Game Logic Manager tests"
)

# 自定义目标：清理
add_custom_target(clean_all
    COMMAND ${CMAKE_COMMAND} --build . --target clean
    COMMENT "Cleaning all build files"
)

# 编译选项说明
message(STATUS "")
message(STATUS "Available targets:")
message(STATUS "  GameLogicManager     - Build the Game Logic Manager library")
message(STATUS "  GameLogicManagerTest - Build the test executable")
message(STATUS "  build_all           - Build all components")
message(STATUS "  run_tests           - Run all tests")
message(STATUS "  clean_all           - Clean all build files")
message(STATUS "")

# 使用说明
message(STATUS "Usage:")
message(STATUS "  mkdir build && cd build")
message(STATUS "  cmake -f ../CMakeLists_GameLogicManager.txt ..")
message(STATUS "  make build_all")
message(STATUS "  make run_tests")
message(STATUS "")

# 功能特性
message(STATUS "Game Logic Manager Features:")
message(STATUS "  ✓ ItemManager      - 物品管理系统")
message(STATUS "  ✓ MapManager       - 地图管理系统")
message(STATUS "  ✓ NPCManager       - NPC管理系统")
message(STATUS "  ✓ MonsterManager   - 怪物管理系统")
message(STATUS "  ✓ MagicManager     - 魔法管理系统")
message(STATUS "  ✓ Event System     - 事件通信系统")
message(STATUS "  ✓ Service Container - 依赖注入容器")
message(STATUS "  ✓ Thread Safety    - 线程安全设计")
message(STATUS "  ✓ Modern C++       - C++17标准")
message(STATUS "  ✓ Original Logic   - 100%遵循原项目逻辑")
message(STATUS "========================================")

# 编译后处理
add_custom_command(TARGET GameLogicManager POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E echo "Game Logic Manager library built successfully!"
    COMMENT "Post-build message"
)

add_custom_command(TARGET GameLogicManagerTest POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E echo "Game Logic Manager test executable built successfully!"
    COMMENT "Post-build message"
)
