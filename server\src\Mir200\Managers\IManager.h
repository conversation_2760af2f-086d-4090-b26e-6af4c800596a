#pragma once

#include "../Common/Types.h"
#include <string>
#include <memory>
#include <vector>

/**
 * @brief 通用Manager接口
 * 所有Manager都必须实现此接口
 */
class IManager {
public:
    virtual ~IManager() = default;
    
    /**
     * @brief 初始化Manager
     * @return true 初始化成功，false 初始化失败
     */
    virtual bool Initialize() = 0;
    
    /**
     * @brief 清理Manager资源
     */
    virtual void Finalize() = 0;
    
    /**
     * @brief 更新Manager状态（每帧调用）
     */
    virtual void Update() = 0;
    
    /**
     * @brief 获取Manager名称
     * @return Manager名称
     */
    virtual const std::string& GetManagerName() const = 0;
};

/**
 * @brief 事件发布者接口
 */
class IEventPublisher {
public:
    virtual ~IEventPublisher() = default;
    
    /**
     * @brief 发布事件
     * @param eventType 事件类型
     * @param data 事件数据
     */
    virtual void PublishEvent(const std::string& eventType, const class EventData& data) = 0;
};

/**
 * @brief 事件订阅者接口
 */
class IEventSubscriber {
public:
    virtual ~IEventSubscriber() = default;
    
    /**
     * @brief 处理事件
     * @param eventType 事件类型
     * @param data 事件数据
     */
    virtual void OnEvent(const std::string& eventType, const class EventData& data) = 0;
};

/**
 * @brief 玩家相关操作接口
 */
class IPlayerProvider {
public:
    virtual ~IPlayerProvider() = default;
    
    /**
     * @brief 查找玩家
     * @param playerName 玩家名称
     * @return 玩家对象指针，未找到返回nullptr
     */
    virtual class PlayObject* FindPlayer(const std::string& playerName) = 0;
    
    /**
     * @brief 获取范围内的玩家
     * @param center 中心点
     * @param range 范围
     * @return 玩家列表
     */
    virtual std::vector<class PlayObject*> GetPlayersInRange(const struct Point& center, int range) = 0;
    
    /**
     * @brief 获取在线玩家数量
     * @return 在线玩家数量
     */
    virtual int GetOnlinePlayerCount() const = 0;
};

/**
 * @brief 物品相关操作接口
 */
class IItemProvider {
public:
    virtual ~IItemProvider() = default;
    
    /**
     * @brief 获取标准物品
     * @param itemIndex 物品索引
     * @return 标准物品指针，未找到返回nullptr
     */
    virtual const struct StdItem* GetStdItem(int itemIndex) const = 0;
    
    /**
     * @brief 创建物品
     * @param itemIndex 物品索引
     * @return 用户物品
     */
    virtual TUserItem CreateItem(int itemIndex) const = 0;

    /**
     * @brief 验证物品
     * @param item 用户物品
     * @return true 物品有效，false 物品无效
     */
    virtual bool ValidateItem(const TUserItem& item) const = 0;

    /**
     * @brief 生成怪物掉落物品
     * @param monsterName 怪物名称
     * @param level 怪物等级
     * @return 掉落物品列表
     */
    virtual std::vector<TUserItem> GenerateMonsterDrops(const std::string& monsterName, int level) = 0;
};

/**
 * @brief 地图相关操作接口
 */
class IMapProvider {
public:
    virtual ~IMapProvider() = default;
    
    /**
     * @brief 获取环境
     * @param mapName 地图名称
     * @return 环境指针，未找到返回nullptr
     */
    virtual class Environment* GetEnvironment(const std::string& mapName) = 0;
    
    /**
     * @brief 检查是否可以行走
     * @param mapName 地图名称
     * @param x X坐标
     * @param y Y坐标
     * @return true 可以行走，false 不可以行走
     */
    virtual bool CanWalk(const std::string& mapName, int x, int y) = 0;
    
    /**
     * @brief 获取范围内的对象
     * @param mapName 地图名称
     * @param center 中心点
     * @param range 范围
     * @return 对象列表
     */
    virtual std::vector<class BaseObject*> GetObjectsInRange(const std::string& mapName,
                                                             const struct Point& center, int range) = 0;
};

/**
 * @brief NPC相关操作接口
 */
class INPCProvider {
public:
    virtual ~INPCProvider() = default;

    /**
     * @brief 获取NPC信息
     * @param npcName NPC名称
     * @return NPC信息指针，未找到返回nullptr
     */
    virtual const struct NPCInfo* GetNPCInfo(const std::string& npcName) const = 0;

    /**
     * @brief 创建NPC
     * @param npcName NPC名称
     * @param mapName 地图名称
     * @param x X坐标
     * @param y Y坐标
     * @return NPC指针，创建失败返回nullptr
     */
    virtual class NPC* CreateNPC(const std::string& npcName, const std::string& mapName, int x, int y) = 0;

    /**
     * @brief 执行NPC脚本
     * @param npcName NPC名称
     * @param player 玩家指针
     * @param label 脚本标签
     * @return true 执行成功，false 执行失败
     */
    virtual bool ExecuteNPCScript(const std::string& npcName, class PlayObject* player, const std::string& label) = 0;
};

/**
 * @brief 怪物相关操作接口
 */
class IMonsterProvider {
public:
    virtual ~IMonsterProvider() = default;

    /**
     * @brief 获取怪物信息
     * @param monsterName 怪物名称
     * @return 怪物信息指针，未找到返回nullptr
     */
    virtual const struct MonsterInfo* GetMonsterInfo(const std::string& monsterName) const = 0;

    /**
     * @brief 创建怪物
     * @param monsterName 怪物名称
     * @param mapName 地图名称
     * @param x X坐标
     * @param y Y坐标
     * @return 怪物指针，创建失败返回nullptr
     */
    virtual class Monster* CreateMonster(const std::string& monsterName, const std::string& mapName, int x, int y) = 0;

    /**
     * @brief 刷新怪物
     * @param mapName 地图名称
     * @return 刷新的怪物数量
     */
    virtual int SpawnMonsters(const std::string& mapName) = 0;
};

/**
 * @brief 魔法相关操作接口
 */
class IMagicProvider {
public:
    virtual ~IMagicProvider() = default;

    /**
     * @brief 获取魔法信息
     * @param magicId 魔法ID
     * @return 魔法信息指针，未找到返回nullptr
     */
    virtual const struct Magic* GetMagic(int magicId) const = 0;

    /**
     * @brief 执行魔法
     * @param caster 施法者
     * @param target 目标
     * @param magicId 魔法ID
     * @param targetX 目标X坐标
     * @param targetY 目标Y坐标
     * @return true 执行成功，false 执行失败
     */
    virtual bool CastMagic(class BaseObject* caster, class BaseObject* target, int magicId, int targetX, int targetY) = 0;

    /**
     * @brief 检查魔法条件
     * @param caster 施法者
     * @param magicId 魔法ID
     * @return true 条件满足，false 条件不满足
     */
    virtual bool CheckMagicConditions(class BaseObject* caster, int magicId) = 0;
};
