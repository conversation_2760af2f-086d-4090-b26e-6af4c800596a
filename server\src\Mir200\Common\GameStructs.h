#pragma once

#include "Types.h"

// Basic game structures for compilation

// StdItem structure (following original project)
struct StdItem {
    int idx;
    std::string name;
    int stdmode;
    int shape;
    int weight;
    int anicount;
    int source;
    int reserved;
    int looks;
    int duramax;
    int ac;
    int mac;
    int dc;
    int mc;
    int sc;
    int need;
    int needlevel;
    int price;
    int stock;
    
    StdItem() : idx(0), stdmode(0), shape(0), weight(0), anicount(0), 
                source(0), reserved(0), looks(0), duramax(0), ac(0), 
                mac(0), dc(0), mc(0), sc(0), need(0), needlevel(0), 
                price(0), stock(0) {}
};

// Magic structure (following original project)
struct Magic {
    int magic_id;
    std::string magic_name;
    int effect_type;
    int effect_point;
    int def_spell;
    int def_power;
    int max_power;
    int job;
    int need_l1;
    int l1train;
    int need_l2;
    int l2train;
    int need_l3;
    int l3train;
    int delay;
    int descr;
    
    Magic() : magic_id(0), effect_type(0), effect_point(0), def_spell(0),
              def_power(0), max_power(0), job(0), need_l1(0), l1train(0),
              need_l2(0), l2train(0), need_l3(0), l3train(0), delay(0), descr(0) {}
};

// MonsterInfo structure (following original project)
struct MonsterInfo {
    std::string name;
    int race;
    int race_img;
    int level;
    int cool_eye;
    int exp;
    int hp;
    int mp;
    int ac;
    int mac;
    int dc;
    int max_dc;
    int mc;
    int sc;
    int speed;
    int hit;
    int walk_speed;
    int walk_step;
    int walk_wait;
    int attack_speed;
    
    MonsterInfo() : race(0), race_img(0), level(0), cool_eye(0), exp(0),
                    hp(0), mp(0), ac(0), mac(0), dc(0), max_dc(0), mc(0),
                    sc(0), speed(0), hit(0), walk_speed(0), walk_step(0),
                    walk_wait(0), attack_speed(0) {}
};

// ServerStatistics structure for enhanced monitoring
struct ServerStatistics {
    int online_players;
    int total_players;
    int loading_players;
    int auto_exp_players;
    int total_monsters;
    int total_merchants;
    int total_npcs;
    int free_list_count;
    int monster_generations;
    int memory_usage_mb;
    DWORD human_process_time;
    DWORD merchant_process_time_min;
    DWORD merchant_process_time_max;
    DWORD npc_process_time_min;
    DWORD npc_process_time_max;
    
    ServerStatistics() : online_players(0), total_players(0), loading_players(0),
                        auto_exp_players(0), total_monsters(0), total_merchants(0),
                        total_npcs(0), free_list_count(0), monster_generations(0),
                        memory_usage_mb(0), human_process_time(0), merchant_process_time_min(0),
                        merchant_process_time_max(0), npc_process_time_min(0), npc_process_time_max(0) {}
};
