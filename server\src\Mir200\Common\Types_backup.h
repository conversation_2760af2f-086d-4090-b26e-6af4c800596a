#pragma once

#include <cstring>
#include <array>
#include <memory>

// Basic types
typedef unsigned char BYTE;
typedef unsigned short WORD;
typedef unsigned long DWORD;

// Point structure
struct Point {
    int x;
    int y;
    
    Point() : x(0), y(0) {}
    Point(int x_, int y_) : x(x_), y(y_) {}
};

// TUserItem structure (matching TUserItem from Grobal2.pas line 1181-1187)
struct TUserItem {
    int MakeIndex;          // 对应原项目的MakeIndex: Integer
    WORD wIndex;            // 对应原项目的wIndex: Word - 物品id
    WORD Dura;              // 对应原项目的Dura: Word - 当前持久值
    WORD DuraMax;           // 对应原项目的DuraMax: Word - 最大持久值
    BYTE btValue[14];       // 对应原项目的btValue: array[0..13] of Byte

    TUserItem() {
        MakeIndex = 0;
        wIndex = 0;
        Dura = 0;
        DuraMax = 0;
        std::memset(btValue, 0, sizeof(btValue));
    }
};

// TDefaultMessage structure (matching TDefaultMessage from Grobal2.pas)
struct TDefaultMessage {
    int Recog;              // 识别码 (Integer in Delphi)
    WORD Ident;             // 消息标识
    WORD Param;             // 参数
    WORD tag;               // 标签
    WORD Series;            // 序列号

    TDefaultMessage() {
        Recog = 0; 
        Ident = 0; 
        Param = 0; 
        tag = 0; 
        Series = 0;
    }
};
