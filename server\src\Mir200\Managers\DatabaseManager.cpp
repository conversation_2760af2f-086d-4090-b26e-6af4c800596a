#include "DatabaseManager.h"
#include "../Common/M2Share.h"
#include <fstream>
#include <sstream>
#include <iostream>
#include <filesystem>

DatabaseManager::DatabaseManager() 
    : m_managerName("DatabaseManager"),
      m_totalQueries(0),
      m_cacheHits(0),
      m_cacheMisses(0) {
}

DatabaseManager::~DatabaseManager() {
    Finalize();
}

bool DatabaseManager::Initialize() {
    std::cout << "[DatabaseManager] Initializing..." << std::endl;
    
    // 确保数据目录存在
    std::filesystem::create_directories(m_config.dataDirectory);
    
    // 加载所有数据
    if (!LoadAllData()) {
        std::cerr << "[DatabaseManager] Failed to load data" << std::endl;
        return false;
    }
    
    std::cout << "[DatabaseManager] Initialized successfully" << std::endl;
    std::cout << "[DatabaseManager] Loaded " << m_stdItems.size() << " items, " 
              << m_magics.size() << " magics, " << m_monsters.size() << " monsters" << std::endl;
    
    return true;
}

void DatabaseManager::Finalize() {
    std::cout << "[DatabaseManager] Finalizing..." << std::endl;
    
    // 清理缓存
    ClearCache();
    
    std::cout << "[DatabaseManager] Statistics:" << std::endl;
    std::cout << "  Total Queries: " << m_totalQueries << std::endl;
    std::cout << "  Cache Hits: " << m_cacheHits << std::endl;
    std::cout << "  Cache Misses: " << m_cacheMisses << std::endl;
    std::cout << "  Cache Hit Ratio: " << GetCacheHitRatio() << "%" << std::endl;
    
    std::cout << "[DatabaseManager] Finalized" << std::endl;
}

void DatabaseManager::Update() {
    // 数据库管理器通常不需要每帧更新
    // 可以在这里检查数据文件是否有变化并重新加载
}

const std::string& DatabaseManager::GetManagerName() const {
    return m_managerName;
}

void DatabaseManager::SetDatabaseConfig(const DatabaseConfig& config) {
    m_config = config;
}

const DatabaseConfig& DatabaseManager::GetDatabaseConfig() const {
    return m_config;
}

bool DatabaseManager::LoadAllData() {
    bool success = true;
    
    success &= LoadStdItems();
    success &= LoadMagicData();
    success &= LoadMonsterData();
    success &= LoadNPCData();
    success &= LoadMapData();
    
    return success;
}

bool DatabaseManager::LoadStdItems() {
    return LoadStdItemsFromFile("StdItems.txt");
}

bool DatabaseManager::LoadMagicData() {
    return LoadMagicFromFile("Magic.txt");
}

bool DatabaseManager::LoadMonsterData() {
    return LoadMonsterFromFile("Monster.txt");
}

bool DatabaseManager::LoadNPCData() {
    return LoadNPCFromFile("NPC.txt");
}

bool DatabaseManager::LoadMapData() {
    return LoadMapFromFile("MapInfo.txt");
}

const StdItem* DatabaseManager::GetStdItem(int itemIndex) const {
    return GetFromCache(m_stdItems, itemIndex);
}

const Magic* DatabaseManager::GetMagic(int magicId) const {
    // 在魔法列表中查找指定ID的魔法
    std::shared_lock<std::shared_mutex> lock(m_dataMutex);
    for (const auto& magic : m_magics) {
        if (magic && magic->wMagicId == magicId) {
            ++m_cacheHits;
            return magic.get();
        }
    }
    
    ++m_cacheMisses;
    return nullptr;
}

const MonsterInfo* DatabaseManager::GetMonsterInfo(const std::string& monsterName) const {
    return GetFromCache(m_monsters, monsterName);
}

const NPCInfo* DatabaseManager::GetNPCInfo(const std::string& npcName) const {
    return GetFromCache(m_npcs, npcName);
}

const MapInfo* DatabaseManager::GetMapInfo(const std::string& mapName) const {
    return GetFromCache(m_maps, mapName);
}

bool DatabaseManager::SavePlayerData(const std::string& account, const std::string& charName, const class PlayerData& data) {
    // 实现玩家数据保存
    // 这里应该根据配置的数据库类型来选择保存方式
    std::string fileName = GetDataFilePath("Players/" + account + "_" + charName + ".dat");
    
    std::ofstream file(fileName, std::ios::binary);
    if (!file.is_open()) {
        LogDatabaseError("SavePlayerData", "Failed to open file: " + fileName);
        return false;
    }
    
    // 这里应该实现具体的数据序列化
    // file.write(reinterpret_cast<const char*>(&data), sizeof(data));
    
    return true;
}

bool DatabaseManager::LoadPlayerData(const std::string& account, const std::string& charName, class PlayerData& data) {
    // 实现玩家数据加载
    std::string fileName = GetDataFilePath("Players/" + account + "_" + charName + ".dat");
    
    std::ifstream file(fileName, std::ios::binary);
    if (!file.is_open()) {
        LogDatabaseError("LoadPlayerData", "Failed to open file: " + fileName);
        return false;
    }
    
    // 这里应该实现具体的数据反序列化
    // file.read(reinterpret_cast<char*>(&data), sizeof(data));
    
    return true;
}

std::unique_ptr<DatabaseTransaction> DatabaseManager::BeginTransaction() {
    return std::make_unique<DatabaseTransaction>();
}

void DatabaseManager::ClearCache() {
    std::unique_lock<std::shared_mutex> lock(m_dataMutex);
    
    m_stdItems.clear();
    m_magics.clear();
    m_monsters.clear();
    m_npcs.clear();
    m_maps.clear();
}

void DatabaseManager::RefreshCache() {
    ClearCache();
    LoadAllData();
}

double DatabaseManager::GetCacheHitRatio() const {
    uint64_t total = m_totalQueries.load();
    if (total == 0) return 0.0;
    
    return (static_cast<double>(m_cacheHits.load()) / total) * 100.0;
}

std::string DatabaseManager::GetDataFilePath(const std::string& fileName) const {
    return m_config.dataDirectory + fileName;
}

void DatabaseManager::LogDatabaseError(const std::string& operation, const std::string& error) const {
    std::cerr << "[DatabaseManager] Error in " << operation << ": " << error << std::endl;
}

bool DatabaseManager::LoadStdItemsFromFile(const std::string& fileName) {
    std::string filePath = GetDataFilePath(fileName);
    std::ifstream file(filePath);

    if (!file.is_open()) {
        LogDatabaseError("LoadStdItemsFromFile", "Failed to open file: " + filePath);
        return false;
    }

    std::unique_lock<std::shared_mutex> lock(m_dataMutex);
    m_stdItems.clear();

    std::string line;
    int lineNumber = 0;

    while (std::getline(file, line)) {
        ++lineNumber;

        if (line.empty() || line[0] == ';' || line[0] == '#') {
            continue;
        }

        // 解析物品数据行
        std::istringstream iss(line);
        std::string token;
        std::vector<std::string> tokens;

        while (std::getline(iss, token, '\t')) {
            tokens.push_back(token);
        }

        if (tokens.size() < 20) { // 假设至少需要20个字段
            LogDatabaseError("LoadStdItemsFromFile",
                           "Invalid item data at line " + std::to_string(lineNumber));
            continue;
        }

        try {
            auto item = std::make_unique<StdItem>();

            // 解析各个字段（根据原项目的StdItem结构）
            item->Name = tokens[1];
            item->StdMode = std::stoi(tokens[2]);
            item->Shape = std::stoi(tokens[3]);
            item->Weight = std::stoi(tokens[4]);
            item->AniCount = std::stoi(tokens[5]);
            item->Source = std::stoi(tokens[6]);
            item->Reserved = std::stoi(tokens[7]);
            item->Looks = std::stoi(tokens[8]);
            item->DuraMax = std::stoi(tokens[9]);

            // AC值 (tokens[10], tokens[11]) - 根据原项目，AC是Integer类型
            item->AC = std::stoi(tokens[10]);

            // MAC值 (tokens[12], tokens[13]) - 根据原项目，MAC是Integer类型
            item->MAC = std::stoi(tokens[12]);

            // DC值 (tokens[14], tokens[15]) - 根据原项目，DC是Integer类型
            item->DC = std::stoi(tokens[14]);

            // MC值 (tokens[16], tokens[17]) - 根据原项目，MC是Integer类型
            item->MC = std::stoi(tokens[16]);

            // SC值 (tokens[18], tokens[19]) - 根据原项目，SC是Integer类型
            item->SC = std::stoi(tokens[18]);

            if (tokens.size() > 20) {
                item->Need = std::stoi(tokens[20]);
            }
            if (tokens.size() > 21) {
                item->NeedLevel = std::stoi(tokens[21]);
            }
            if (tokens.size() > 22) {
                item->Price = std::stoi(tokens[22]);
            }

            // 验证物品数据
            if (ValidateStdItem(*item)) {
                // 确保索引正确
                int itemIndex = std::stoi(tokens[0]);
                if (itemIndex >= 0) {
                    // 扩展向量大小以容纳新索引
                    if (itemIndex >= static_cast<int>(m_stdItems.size())) {
                        m_stdItems.resize(itemIndex + 1);
                    }
                    m_stdItems[itemIndex] = std::move(item);
                }
            } else {
                LogDatabaseError("LoadStdItemsFromFile",
                               "Invalid item data for: " + item->Name);
            }

        } catch (const std::exception& e) {
            LogDatabaseError("LoadStdItemsFromFile",
                           "Parse error at line " + std::to_string(lineNumber) + ": " + e.what());
        }
    }

    std::cout << "[DatabaseManager] Loaded " << m_stdItems.size() << " items from " << fileName << std::endl;
    return true;
}

bool DatabaseManager::LoadMagicFromFile(const std::string& fileName) {
    std::string filePath = GetDataFilePath(fileName);
    std::ifstream file(filePath);

    if (!file.is_open()) {
        LogDatabaseError("LoadMagicFromFile", "Failed to open file: " + filePath);
        return false;
    }

    std::unique_lock<std::shared_mutex> lock(m_dataMutex);
    m_magics.clear();

    std::string line;
    int lineNumber = 0;

    while (std::getline(file, line)) {
        ++lineNumber;

        if (line.empty() || line[0] == ';' || line[0] == '#') {
            continue;
        }

        // 解析魔法数据行
        std::istringstream iss(line);
        std::string token;
        std::vector<std::string> tokens;

        while (std::getline(iss, token, '\t')) {
            tokens.push_back(token);
        }

        if (tokens.size() < 15) { // 假设至少需要15个字段
            LogDatabaseError("LoadMagicFromFile",
                           "Invalid magic data at line " + std::to_string(lineNumber));
            continue;
        }

        try {
            auto magic = std::make_unique<Magic>();

            // 解析各个字段（根据原项目的Magic结构）
            magic->wMagicId = std::stoi(tokens[0]);
            magic->sMagicName = tokens[1];
            magic->btEffectType = std::stoi(tokens[2]);
            magic->btEffect = std::stoi(tokens[3]);
            magic->wSpell = std::stoi(tokens[4]);
            magic->wPower = std::stoi(tokens[5]);
            magic->wMaxPower = std::stoi(tokens[6]);
            magic->btJob = std::stoi(tokens[7]);

            // 训练等级要求
            if (tokens.size() > 8) magic->TrainLevel[0] = std::stoi(tokens[8]);
            if (tokens.size() > 9) magic->TrainLevel[1] = std::stoi(tokens[9]);
            if (tokens.size() > 10) magic->TrainLevel[2] = std::stoi(tokens[10]);
            if (tokens.size() > 11) magic->TrainLevel[3] = std::stoi(tokens[11]);

            // 最大训练值
            if (tokens.size() > 12) magic->MaxTrain[0] = std::stoi(tokens[12]);
            if (tokens.size() > 13) magic->MaxTrain[1] = std::stoi(tokens[13]);
            if (tokens.size() > 14) magic->MaxTrain[2] = std::stoi(tokens[14]);
            if (tokens.size() > 15) magic->MaxTrain[3] = std::stoi(tokens[15]);

            if (tokens.size() > 16) magic->dwDelayTime = std::stoi(tokens[16]);
            if (tokens.size() > 17) magic->btDefSpell = std::stoi(tokens[17]);
            if (tokens.size() > 18) magic->btDefPower = std::stoi(tokens[18]);
            if (tokens.size() > 19) magic->btDefMaxPower = std::stoi(tokens[19]);
            if (tokens.size() > 20) magic->sDescr = tokens[20];

            magic->btTrainLv = 3; // 默认训练等级

            // 验证魔法数据
            if (ValidateMagic(*magic)) {
                m_magics.push_back(std::move(magic));
            } else {
                LogDatabaseError("LoadMagicFromFile",
                               "Invalid magic data for: " + magic->sMagicName);
            }

        } catch (const std::exception& e) {
            LogDatabaseError("LoadMagicFromFile",
                           "Parse error at line " + std::to_string(lineNumber) + ": " + e.what());
        }
    }

    std::cout << "[DatabaseManager] Loaded " << m_magics.size() << " magics from " << fileName << std::endl;
    return true;
}

bool DatabaseManager::LoadMonsterFromFile(const std::string& fileName) {
    // 简化实现，实际应该根据原项目的Monster数据格式来解析
    std::string filePath = GetDataFilePath(fileName);
    std::ifstream file(filePath);

    if (!file.is_open()) {
        LogDatabaseError("LoadMonsterFromFile", "Failed to open file: " + filePath);
        return false;
    }

    std::unique_lock<std::shared_mutex> lock(m_dataMutex);
    m_monsters.clear();

    // 这里应该实现具体的怪物数据解析
    // 暂时返回true表示成功
    return true;
}

bool DatabaseManager::LoadNPCFromFile(const std::string& fileName) {
    // 简化实现，实际应该根据原项目的NPC数据格式来解析
    std::string filePath = GetDataFilePath(fileName);
    std::ifstream file(filePath);

    if (!file.is_open()) {
        LogDatabaseError("LoadNPCFromFile", "Failed to open file: " + filePath);
        return false;
    }

    std::unique_lock<std::shared_mutex> lock(m_dataMutex);
    m_npcs.clear();

    // 这里应该实现具体的NPC数据解析
    // 暂时返回true表示成功
    return true;
}

bool DatabaseManager::LoadMapFromFile(const std::string& fileName) {
    // 简化实现，实际应该根据原项目的地图数据格式来解析
    std::string filePath = GetDataFilePath(fileName);
    std::ifstream file(filePath);

    if (!file.is_open()) {
        LogDatabaseError("LoadMapFromFile", "Failed to open file: " + filePath);
        return false;
    }

    std::unique_lock<std::shared_mutex> lock(m_dataMutex);
    m_maps.clear();

    // 这里应该实现具体的地图数据解析
    // 暂时返回true表示成功
    return true;
}

bool DatabaseManager::ValidateStdItem(const StdItem& item) const {
    // 基本验证
    if (item.Name.empty()) return false;
    if (item.DuraMax <= 0) return false;
    if (item.Weight < 0) return false;

    return true;
}

bool DatabaseManager::ValidateMagic(const Magic& magic) const {
    // 基本验证
    if (magic.sMagicName.empty()) return false;
    if (magic.wMagicId <= 0) return false;
    if (magic.wSpell < 0) return false;

    return true;
}

bool DatabaseManager::ValidateMonster(const MonsterInfo& monster) const {
    // 基本验证 - 这里需要根据实际的MonsterInfo结构来实现
    return true;
}

// DatabaseTransaction实现
bool DatabaseTransaction::Commit() {
    if (!m_active) return false;

    m_committed = true;
    m_active = false;

    // 这里应该实现实际的事务提交逻辑
    return true;
}

bool DatabaseTransaction::Rollback() {
    if (!m_active) return false;

    m_active = false;

    // 这里应该实现实际的事务回滚逻辑
    return true;
}
