#include "ScriptManager.h"
#include "../Objects/PlayObject.h"
#include "../Common/M2Share.h"
#include <fstream>
#include <sstream>
#include <algorithm>
#include <iostream>
#include <filesystem>

ScriptManager::ScriptManager() 
    : m_nextContextId(1)
    , m_totalScripts(0)
    , m_loadedScripts(0)
    , m_executedActions(0)
    , m_failedActions(0)
    , m_scriptDirectory("scripts/")
    , m_autoReload(true)
    , m_reloadInterval(60000)
    , m_lastReloadCheck(0)
    , m_managerName("ScriptManager")
    , m_initialized(false) {
}

ScriptManager::~ScriptManager() {
    Finalize();
}

bool ScriptManager::Initialize() {
    std::cout << "[ScriptManager] Initializing..." << std::endl;
    
    // 创建脚本目录
    try {
        std::filesystem::create_directories(m_scriptDirectory);
    } catch (const std::exception& e) {
        std::cerr << "[ScriptManager] Failed to create script directory: " << e.what() << std::endl;
        return false;
    }
    
    // 注册内置条件检查器和动作执行器
    RegisterBuiltinConditionCheckers();
    RegisterBuiltinActionExecutors();
    
    // 重置统计信息
    m_totalScripts = 0;
    m_loadedScripts = 0;
    m_executedActions = 0;
    m_failedActions = 0;
    m_nextContextId = 1;
    m_lastReloadCheck = GetCurrentTime();
    
    m_initialized = true;
    
    std::cout << "[ScriptManager] Initialized successfully" << std::endl;
    return true;
}

void ScriptManager::Finalize() {
    if (!m_initialized) return;
    
    std::cout << "[ScriptManager] Finalizing..." << std::endl;
    
    // 清理所有脚本
    {
        std::lock_guard<std::mutex> lock(m_scriptMutex);
        m_scripts.clear();
    }
    
    // 清理所有上下文
    {
        std::lock_guard<std::mutex> lock(m_contextMutex);
        m_contexts.clear();
    }
    
    // 清理变量
    {
        std::lock_guard<std::mutex> lock(m_variableMutex);
        m_globalVariables.clear();
    }
    
    // 清理处理器
    {
        std::lock_guard<std::mutex> lock(m_handlerMutex);
        m_conditionCheckers.clear();
        m_actionExecutors.clear();
    }
    
    m_initialized = false;
    
    std::cout << "[ScriptManager] Finalized" << std::endl;
}

void ScriptManager::Update() {
    if (!m_initialized) return;
    
    // 检查脚本重载
    if (m_autoReload) {
        CheckScriptReload();
    }
}

const std::string& ScriptManager::GetManagerName() const {
    return m_managerName;
}

bool ScriptManager::LoadScript(const std::string& fileName, ScriptType type) {
    std::lock_guard<std::mutex> lock(m_scriptMutex);
    
    std::string filePath = m_scriptDirectory + fileName;
    if (filePath.find(".txt") == std::string::npos) {
        filePath += ".txt";
    }
    
    auto script = std::make_unique<ScriptFile>();
    script->fileName = fileName;
    script->type = type;
    script->lastModified = GetFileModificationTime(filePath);
    
    if (!ParseScriptFile(filePath, *script)) {
        std::cerr << "[ScriptManager] Failed to parse script file: " << fileName << std::endl;
        return false;
    }
    
    script->loaded = true;
    m_scripts[fileName] = std::move(script);
    
    ++m_totalScripts;
    ++m_loadedScripts;
    
    std::cout << "[ScriptManager] Loaded script: " << fileName << std::endl;
    return true;
}

bool ScriptManager::ReloadScript(const std::string& fileName) {
    std::lock_guard<std::mutex> lock(m_scriptMutex);
    
    auto it = m_scripts.find(fileName);
    if (it == m_scripts.end()) {
        return LoadScript(fileName);
    }
    
    std::string filePath = m_scriptDirectory + fileName;
    if (filePath.find(".txt") == std::string::npos) {
        filePath += ".txt";
    }
    
    DWORD newModTime = GetFileModificationTime(filePath);
    if (newModTime <= it->second->lastModified) {
        return true; // 文件未修改
    }
    
    // 重新解析脚本
    auto newScript = std::make_unique<ScriptFile>();
    newScript->fileName = fileName;
    newScript->type = it->second->type;
    newScript->lastModified = newModTime;
    
    if (!ParseScriptFile(filePath, *newScript)) {
        std::cerr << "[ScriptManager] Failed to reload script file: " << fileName << std::endl;
        return false;
    }
    
    newScript->loaded = true;
    it->second = std::move(newScript);
    
    std::cout << "[ScriptManager] Reloaded script: " << fileName << std::endl;
    return true;
}

uint32_t ScriptManager::StartScript(const std::string& scriptName, const std::string& label, 
                                   PlayObject* player, BaseObject* npc) {
    if (!player) {
        std::cerr << "[ScriptManager] Invalid player for script: " << scriptName << std::endl;
        return 0;
    }
    
    const ScriptFile* script = GetScript(scriptName);
    if (!script) {
        std::cerr << "[ScriptManager] Script not found: " << scriptName << std::endl;
        return 0;
    }
    
    const ScriptBlock* block = script->GetBlock(label);
    if (!block) {
        std::cerr << "[ScriptManager] Script block not found: " << scriptName << ":" << label << std::endl;
        return 0;
    }
    
    // 创建执行上下文
    auto context = std::make_unique<ScriptContext>();
    context->player = player;
    context->npc = npc;
    context->currentLabel = label;
    context->finished = false;
    
    uint32_t contextId = GenerateContextId();
    
    {
        std::lock_guard<std::mutex> lock(m_contextMutex);
        m_contexts[contextId] = std::move(context);
    }
    
    // 执行脚本块
    if (!ExecuteScriptBlock(contextId, label)) {
        EndScript(contextId);
        return 0;
    }
    
    std::cout << "[ScriptManager] Started script: " << scriptName << ":" << label 
              << " (context: " << contextId << ")" << std::endl;
    
    return contextId;
}

bool ScriptManager::ExecuteScriptBlock(uint32_t contextId, const std::string& label) {
    std::lock_guard<std::mutex> lock(m_contextMutex);
    
    auto it = m_contexts.find(contextId);
    if (it == m_contexts.end()) {
        return false;
    }
    
    ScriptContext& context = *it->second;
    
    // 获取脚本块
    const ScriptFile* script = GetScript(context.player->GetCharName() + "_script"); // 简化处理
    if (!script) {
        return false;
    }
    
    const ScriptBlock* block = script->GetBlock(label);
    if (!block) {
        return false;
    }
    
    context.currentLabel = label;
    
    // 检查条件
    bool conditionsPassed = CheckConditions(block->conditions, context);
    
    // 执行动作
    if (conditionsPassed) {
        return ExecuteActions(block->actions, context);
    } else {
        return ExecuteActions(block->elseActions, context);
    }
}

bool ScriptManager::CheckConditions(const std::vector<ScriptCondition>& conditions, ScriptContext& context) {
    if (conditions.empty()) {
        return true; // 没有条件默认通过
    }
    
    for (const auto& condition : conditions) {
        if (!CheckCondition(condition, context)) {
            return false;
        }
    }
    
    return true;
}

bool ScriptManager::ExecuteActions(const std::vector<ScriptAction>& actions, ScriptContext& context) {
    bool success = true;
    
    for (const auto& action : actions) {
        if (!ExecuteAction(action, context)) {
            success = false;
            ++m_failedActions;
        } else {
            ++m_executedActions;
        }
    }
    
    return success;
}

bool ScriptManager::CheckCondition(const ScriptCondition& condition, ScriptContext& context) {
    std::lock_guard<std::mutex> lock(m_handlerMutex);
    
    auto it = m_conditionCheckers.find(condition.type);
    if (it != m_conditionCheckers.end()) {
        try {
            return it->second(condition, context);
        } catch (const std::exception& e) {
            std::cerr << "[ScriptManager] Error checking condition: " << e.what() << std::endl;
            return false;
        }
    }
    
    std::cerr << "[ScriptManager] Unknown condition type: " << static_cast<int>(condition.type) << std::endl;
    return false;
}

bool ScriptManager::ExecuteAction(const ScriptAction& action, ScriptContext& context) {
    std::lock_guard<std::mutex> lock(m_handlerMutex);
    
    auto it = m_actionExecutors.find(action.type);
    if (it != m_actionExecutors.end()) {
        try {
            return it->second(action, context);
        } catch (const std::exception& e) {
            std::cerr << "[ScriptManager] Error executing action: " << e.what() << std::endl;
            return false;
        }
    }
    
    std::cerr << "[ScriptManager] Unknown action type: " << static_cast<int>(action.type) << std::endl;
    return false;
}

void ScriptManager::SetGlobalVariable(const std::string& varName, int value) {
    std::lock_guard<std::mutex> lock(m_variableMutex);
    m_globalVariables["global"][varName] = value;
    
    std::cout << "[ScriptManager] Set global variable: " << varName << " = " << value << std::endl;
}

int ScriptManager::GetGlobalVariable(const std::string& varName) const {
    std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(m_variableMutex));
    
    auto it = m_globalVariables.find("global");
    if (it != m_globalVariables.end()) {
        auto varIt = it->second.find(varName);
        if (varIt != it->second.end()) {
            return varIt->second;
        }
    }
    
    return 0; // 默认值
}

const ScriptFile* ScriptManager::GetScript(const std::string& fileName) const {
    std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(m_scriptMutex));
    
    auto it = m_scripts.find(fileName);
    return (it != m_scripts.end()) ? it->second.get() : nullptr;
}

void ScriptManager::RegisterConditionChecker(ScriptConditionType type, 
                                            std::function<bool(const ScriptCondition&, ScriptContext&)> checker) {
    std::lock_guard<std::mutex> lock(m_handlerMutex);
    m_conditionCheckers[type] = std::move(checker);
    
    std::cout << "[ScriptManager] Registered condition checker for type: " << static_cast<int>(type) << std::endl;
}

void ScriptManager::RegisterActionExecutor(ScriptActionType type, 
                                          std::function<bool(const ScriptAction&, ScriptContext&)> executor) {
    std::lock_guard<std::mutex> lock(m_handlerMutex);
    m_actionExecutors[type] = std::move(executor);
    
    std::cout << "[ScriptManager] Registered action executor for type: " << static_cast<int>(type) << std::endl;
}

double ScriptManager::GetActionSuccessRate() const {
    uint64_t total = m_executedActions + m_failedActions;
    return total > 0 ? (double)m_executedActions / total * 100.0 : 0.0;
}

void ScriptManager::RegisterBuiltinConditionCheckers() {
    // 注册内置条件检查器
    RegisterConditionChecker(ScriptConditionType::CHECK_LEVEL, 
        [](const ScriptCondition& condition, ScriptContext& context) -> bool {
            if (!context.player) return false;
            
            int playerLevel = 1; // context.player->GetLevel(); // 需要实现
            int requiredLevel = std::stoi(condition.param1);
            
            if (condition.operator_str == ">") return playerLevel > requiredLevel;
            if (condition.operator_str == "<") return playerLevel < requiredLevel;
            if (condition.operator_str == "=") return playerLevel == requiredLevel;
            if (condition.operator_str == ">=") return playerLevel >= requiredLevel;
            if (condition.operator_str == "<=") return playerLevel <= requiredLevel;
            
            return false;
        });
    
    RegisterConditionChecker(ScriptConditionType::CHECK_GOLD, 
        [](const ScriptCondition& condition, ScriptContext& context) -> bool {
            if (!context.player) return false;
            
            int playerGold = 0; // context.player->GetGold(); // 需要实现
            int requiredGold = std::stoi(condition.param1);
            
            if (condition.operator_str == ">") return playerGold > requiredGold;
            if (condition.operator_str == "<") return playerGold < requiredGold;
            if (condition.operator_str == "=") return playerGold == requiredGold;
            if (condition.operator_str == ">=") return playerGold >= requiredGold;
            if (condition.operator_str == "<=") return playerGold <= requiredGold;
            
            return false;
        });
}

void ScriptManager::RegisterBuiltinActionExecutors() {
    // 注册内置动作执行器
    RegisterActionExecutor(ScriptActionType::SEND_MSG, 
        [](const ScriptAction& action, ScriptContext& context) -> bool {
            if (!context.player) return false;
            
            std::string message = action.param2;
            std::cout << "[ScriptManager] Send message to " << context.player->GetCharName() 
                      << ": " << message << std::endl;
            
            // context.player->SendMessage(message); // 需要实现
            return true;
        });
    
    RegisterActionExecutor(ScriptActionType::GIVE_EXP, 
        [](const ScriptAction& action, ScriptContext& context) -> bool {
            if (!context.player) return false;
            
            int exp = std::stoi(action.param1);
            std::cout << "[ScriptManager] Give " << exp << " exp to " << context.player->GetCharName() << std::endl;
            
            // context.player->AddExp(exp); // 需要实现
            return true;
        });
}

bool ScriptManager::ParseScriptFile(const std::string& filePath, ScriptFile& script) {
    std::vector<std::string> lines;
    if (!LoadTextFile(filePath, lines)) {
        return false;
    }
    
    size_t lineIndex = 0;
    while (lineIndex < lines.size()) {
        const std::string& line = TrimString(lines[lineIndex]);
        
        if (IsComment(line) || line.empty()) {
            ++lineIndex;
            continue;
        }
        
        if (IsLabel(line)) {
            auto block = std::make_unique<ScriptBlock>();
            block->label = ExtractLabel(line);
            
            ++lineIndex;
            if (ParseScriptBlock(lines, lineIndex, *block)) {
                script.blocks[block->label] = std::move(block);
            }
        } else {
            ++lineIndex;
        }
    }
    
    return !script.blocks.empty();
}

uint32_t ScriptManager::GenerateContextId() {
    return m_nextContextId.fetch_add(1);
}

std::string ScriptManager::TrimString(const std::string& str) {
    size_t start = str.find_first_not_of(" \t\r\n");
    if (start == std::string::npos) return "";
    
    size_t end = str.find_last_not_of(" \t\r\n");
    return str.substr(start, end - start + 1);
}

bool ScriptManager::IsComment(const std::string& line) {
    return line.empty() || line[0] == ';' || line.substr(0, 2) == "//";
}

bool ScriptManager::IsLabel(const std::string& line) {
    return line.size() > 2 && line[0] == '[' && line[1] == '@' && line.back() == ']';
}

std::string ScriptManager::ExtractLabel(const std::string& line) {
    if (line.size() > 3 && line[0] == '[' && line[1] == '@' && line.back() == ']') {
        return line.substr(2, line.size() - 3);
    }
    return "";
}

bool ScriptManager::LoadTextFile(const std::string& filePath, std::vector<std::string>& lines) {
    std::ifstream file(filePath);
    if (!file.is_open()) {
        return false;
    }
    
    std::string line;
    while (std::getline(file, line)) {
        lines.push_back(line);
    }
    
    return true;
}

DWORD ScriptManager::GetFileModificationTime(const std::string& filePath) {
    try {
        auto ftime = std::filesystem::last_write_time(filePath);
        auto sctp = std::chrono::time_point_cast<std::chrono::system_clock::duration>(
            ftime - std::filesystem::file_time_type::clock::now() + std::chrono::system_clock::now());
        return static_cast<DWORD>(std::chrono::duration_cast<std::chrono::seconds>(sctp.time_since_epoch()).count());
    } catch (...) {
        return 0;
    }
}

void ScriptManager::CheckScriptReload() {
    DWORD currentTime = GetCurrentTime();
    if (currentTime - m_lastReloadCheck < m_reloadInterval) {
        return;
    }
    
    m_lastReloadCheck = currentTime;
    
    std::lock_guard<std::mutex> lock(m_scriptMutex);
    for (auto& pair : m_scripts) {
        ReloadScript(pair.first);
    }
}
