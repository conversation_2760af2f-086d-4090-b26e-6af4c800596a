#pragma once

#include "IManager.h"
#include <string>
#include <fstream>
#include <mutex>
#include <queue>
#include <thread>
#include <atomic>
#include <condition_variable>
#include <chrono>
#include <memory>
#include <cstdio>

/**
 * @brief 日志级别枚举
 */
enum LogLevel {
    LOG_DEBUG = 0,
    LOG_INFO = 1,
    LOG_WARNING = 2,
    LOG_ERROR = 3,
    LOG_CRITICAL = 4
};

/**
 * @brief 日志条目结构
 */
struct LogEntry {
    LogLevel level;
    std::string message;
    std::string component;
    std::chrono::system_clock::time_point timestamp;
    std::thread::id threadId;
    
    LogEntry(LogLevel lvl, const std::string& msg, const std::string& comp)
        : level(lvl), message(msg), component(comp), 
          timestamp(std::chrono::system_clock::now()),
          threadId(std::this_thread::get_id()) {}
};

/**
 * @brief 日志管理器
 * 负责统一管理所有日志输出
 */
class LogManager : public IManager {
private:
    std::string m_logDirectory;
    std::string m_logFileName;
    std::string m_managerName;
    
    LogLevel m_minLogLevel;
    bool m_enableConsoleOutput;
    bool m_enableFileOutput;
    bool m_enableAsyncLogging;
    
    std::ofstream m_logFile;
    mutable std::mutex m_fileMutex;

    // 异步日志相关
    std::queue<LogEntry> m_logQueue;
    mutable std::mutex m_queueMutex;
    std::condition_variable m_queueCondition;
    std::thread m_logThread;
    std::atomic<bool> m_running;
    
    // 统计信息
    std::atomic<uint64_t> m_totalLogs;
    std::atomic<uint64_t> m_droppedLogs;
    size_t m_maxQueueSize;
    
public:
    LogManager();
    virtual ~LogManager();
    
    // IManager接口实现
    bool Initialize() override;
    void Finalize() override;
    void Update() override;
    const std::string& GetManagerName() const override;
    
    // 日志配置
    void SetLogDirectory(const std::string& directory);
    void SetLogFileName(const std::string& fileName);
    void SetMinLogLevel(LogLevel level);
    void SetConsoleOutput(bool enable);
    void SetFileOutput(bool enable);
    void SetAsyncLogging(bool enable);
    void SetMaxQueueSize(size_t maxSize);
    
    // 日志输出接口
    void Log(LogLevel level, const std::string& message, const std::string& component = "");
    void Debug(const std::string& message, const std::string& component = "");
    void Info(const std::string& message, const std::string& component = "");
    void Warning(const std::string& message, const std::string& component = "");
    void Error(const std::string& message, const std::string& component = "");
    void Critical(const std::string& message, const std::string& component = "");
    
    // 格式化日志输出 (简化版本，避免C++17变参模板)
    void LogFormat(LogLevel level, const std::string& component, const std::string& format);
    void DebugFormat(const std::string& component, const std::string& format);
    void InfoFormat(const std::string& component, const std::string& format);
    void WarningFormat(const std::string& component, const std::string& format);
    void ErrorFormat(const std::string& component, const std::string& format);
    void CriticalFormat(const std::string& component, const std::string& format);
    
    // 统计信息
    uint64_t GetTotalLogs() const { return m_totalLogs; }
    uint64_t GetDroppedLogs() const { return m_droppedLogs; }
    size_t GetQueueSize() const;
    
    // 日志文件管理
    void FlushLogs();
    void RotateLogFile();
    
private:
    void LogThreadFunction();
    void WriteLogEntry(const LogEntry& entry);
    std::string FormatLogEntry(const LogEntry& entry) const;
    std::string LogLevelToString(LogLevel level) const;
    std::string GetTimestamp(const std::chrono::system_clock::time_point& timePoint) const;
    bool OpenLogFile();
    void CloseLogFile();
};


