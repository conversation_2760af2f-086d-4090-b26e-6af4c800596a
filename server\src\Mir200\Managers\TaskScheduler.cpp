#include "TaskScheduler.h"
#include "../Common/M2Share.h"
#include <iostream>
#include <algorithm>
#include <sstream>
#include <chrono>

TaskScheduler::TaskScheduler() 
    : m_taskQueue([](const std::shared_ptr<ScheduledTask>& a, const std::shared_ptr<ScheduledTask>& b) {
        return TaskComparator(a, b);
      })
    , m_running(false)
    , m_nextTaskId(1)
    , m_workerThreadCount(2)
    , m_maxQueueSize(10000)
    , m_defaultTimeout(30000)
    , m_defaultMaxRetries(3)
    , m_managerName("TaskScheduler")
    , m_initialized(false) {
}

TaskScheduler::~TaskScheduler() {
    Finalize();
}

bool TaskScheduler::Initialize() {
    std::cout << "[TaskScheduler] Initializing..." << std::endl;
    
    // 重置统计信息
    {
        std::lock_guard<std::mutex> lock(m_statisticsMutex);
        m_statistics = TaskStatistics();
    }
    
    // 清理任务队列
    {
        std::lock_guard<std::mutex> lock(m_queueMutex);
        while (!m_taskQueue.empty()) {
            m_taskQueue.pop();
        }
    }
    
    // 清理任务存储
    {
        std::lock_guard<std::mutex> lock(m_taskMutex);
        m_tasks.clear();
    }
    
    // 启动工作线程
    m_running = true;
    for (size_t i = 0; i < m_workerThreadCount; ++i) {
        m_workerThreads.emplace_back(&TaskScheduler::WorkerThreadFunction, this);
    }
    
    // 注册系统任务
    RegisterSystemTasks();
    
    m_nextTaskId = 1;
    m_initialized = true;
    
    std::cout << "[TaskScheduler] Initialized with " << m_workerThreadCount << " worker threads" << std::endl;
    return true;
}

void TaskScheduler::Finalize() {
    if (!m_initialized) return;
    
    std::cout << "[TaskScheduler] Finalizing..." << std::endl;
    
    // 停止工作线程
    m_running = false;
    m_queueCondition.notify_all();
    
    for (auto& thread : m_workerThreads) {
        if (thread.joinable()) {
            thread.join();
        }
    }
    m_workerThreads.clear();
    
    // 清理任务
    CancelAllTasks();
    
    m_initialized = false;
    
    std::cout << "[TaskScheduler] Finalized" << std::endl;
}

void TaskScheduler::Update() {
    if (!m_initialized) return;
    
    // 检查需要调度的任务
    std::vector<std::shared_ptr<ScheduledTask>> tasksToSchedule;
    
    {
        std::lock_guard<std::mutex> lock(m_taskMutex);
        DWORD currentTime = GetCurrentTime();
        
        for (auto& pair : m_tasks) {
            auto& task = pair.second;
            
            if (task->status == TaskStatus::PENDING && ShouldExecuteTask(*task)) {
                tasksToSchedule.push_back(task);
            }
        }
    }
    
    // 将需要执行的任务加入队列
    for (auto& task : tasksToSchedule) {
        ScheduleTaskInternal(task);
    }
}

const std::string& TaskScheduler::GetManagerName() const {
    return m_managerName;
}

uint32_t TaskScheduler::ScheduleTask(const std::string& taskName, std::function<bool()> taskFunction,
                                    DWORD delay, TaskPriority priority) {
    auto task = std::make_shared<ScheduledTask>();
    task->taskId = GenerateTaskId();
    task->taskName = taskName;
    task->type = TaskType::SYSTEM_TASK;
    task->priority = priority;
    task->taskFunction = std::move(taskFunction);
    task->createTime = GetCurrentTime();
    task->scheduleTime = task->createTime + delay;
    task->timeout = m_defaultTimeout;
    task->maxRetries = m_defaultMaxRetries;
    
    {
        std::lock_guard<std::mutex> lock(m_taskMutex);
        m_tasks[task->taskId] = task;
    }
    
    {
        std::lock_guard<std::mutex> lock(m_statisticsMutex);
        ++m_statistics.totalTasks;
        ++m_statistics.pendingTasks;
    }
    
    std::cout << "[TaskScheduler] Scheduled task: " << taskName << " (ID: " << task->taskId 
              << ", delay: " << delay << "ms)" << std::endl;
    
    return task->taskId;
}

uint32_t TaskScheduler::ScheduleRepeatingTask(const std::string& taskName, std::function<bool()> taskFunction,
                                             DWORD interval, int repeatCount, TaskPriority priority) {
    auto task = std::make_shared<ScheduledTask>();
    task->taskId = GenerateTaskId();
    task->taskName = taskName;
    task->type = TaskType::SYSTEM_TASK;
    task->priority = priority;
    task->taskFunction = std::move(taskFunction);
    task->createTime = GetCurrentTime();
    task->scheduleTime = task->createTime + interval;
    task->interval = interval;
    task->repeatCount = repeatCount;
    task->timeout = m_defaultTimeout;
    task->maxRetries = m_defaultMaxRetries;
    
    {
        std::lock_guard<std::mutex> lock(m_taskMutex);
        m_tasks[task->taskId] = task;
    }
    
    {
        std::lock_guard<std::mutex> lock(m_statisticsMutex);
        ++m_statistics.totalTasks;
        ++m_statistics.pendingTasks;
    }
    
    std::cout << "[TaskScheduler] Scheduled repeating task: " << taskName << " (ID: " << task->taskId 
              << ", interval: " << interval << "ms, repeat: " << repeatCount << ")" << std::endl;
    
    return task->taskId;
}

bool TaskScheduler::CancelTask(uint32_t taskId) {
    std::lock_guard<std::mutex> lock(m_taskMutex);
    
    auto it = m_tasks.find(taskId);
    if (it == m_tasks.end()) {
        return false;
    }
    
    auto& task = it->second;
    if (task->status == TaskStatus::RUNNING) {
        return false; // 不能取消正在运行的任务
    }
    
    task->status = TaskStatus::CANCELLED;
    task->completeTime = GetCurrentTime();
    
    {
        std::lock_guard<std::mutex> statsLock(m_statisticsMutex);
        ++m_statistics.cancelledTasks;
        if (m_statistics.pendingTasks > 0) {
            --m_statistics.pendingTasks;
        }
    }
    
    std::cout << "[TaskScheduler] Cancelled task: " << task->taskName << " (ID: " << taskId << ")" << std::endl;
    return true;
}

void TaskScheduler::RegisterSystemTasks() {
    // 注册系统维护任务 - 每5分钟执行一次
    ScheduleRepeatingTask("SystemMaintenance", 
        [this]() { return SystemMaintenanceTask(); }, 
        300000, -1, TaskPriority::NORMAL);
    
    // 注册系统清理任务 - 每10分钟执行一次
    ScheduleRepeatingTask("SystemCleanup", 
        [this]() { return SystemCleanupTask(); }, 
        600000, -1, TaskPriority::LOW);
    
    // 注册系统保存任务 - 每2分钟执行一次
    ScheduleRepeatingTask("SystemSave", 
        [this]() { return SystemSaveTask(); }, 
        120000, -1, TaskPriority::HIGH);
    
    // 注册系统监控任务 - 每30秒执行一次
    ScheduleRepeatingTask("SystemMonitor", 
        [this]() { return SystemMonitorTask(); }, 
        30000, -1, TaskPriority::NORMAL);
    
    std::cout << "[TaskScheduler] Registered system tasks" << std::endl;
}

TaskStatistics TaskScheduler::GetStatistics() const {
    std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(m_statisticsMutex));
    return m_statistics;
}

void TaskScheduler::WorkerThreadFunction() {
    while (m_running) {
        std::shared_ptr<ScheduledTask> task;
        
        {
            std::unique_lock<std::mutex> lock(m_queueMutex);
            m_queueCondition.wait(lock, [this] { return !m_taskQueue.empty() || !m_running; });
            
            if (!m_running) break;
            
            if (!m_taskQueue.empty()) {
                task = m_taskQueue.top();
                m_taskQueue.pop();
            }
        }
        
        if (task) {
            ProcessTask(task);
        }
    }
}

void TaskScheduler::ProcessTask(std::shared_ptr<ScheduledTask> task) {
    task->status = TaskStatus::RUNNING;
    task->executeTime = GetCurrentTime();
    
    {
        std::lock_guard<std::mutex> lock(m_statisticsMutex);
        ++m_statistics.runningTasks;
        if (m_statistics.pendingTasks > 0) {
            --m_statistics.pendingTasks;
        }
    }
    
    bool success = ExecuteTask(*task);
    CompleteTask(*task, success);
}

bool TaskScheduler::ExecuteTask(ScheduledTask& task) {
    try {
        if (task.taskFunction) {
            return task.taskFunction();
        }
        return false;
    } catch (const std::exception& e) {
        task.errorMessage = e.what();
        return false;
    }
}

void TaskScheduler::CompleteTask(ScheduledTask& task, bool success) {
    task.completeTime = GetCurrentTime();
    
    if (success) {
        task.status = TaskStatus::COMPLETED;
        ++task.currentCount;
        
        // 检查是否需要重复执行
        if (task.interval > 0 && (task.repeatCount == -1 || task.currentCount < task.repeatCount)) {
            RescheduleRepeatingTask(task);
        }
    } else {
        if (task.retryCount < task.maxRetries) {
            ++task.retryCount;
            task.status = TaskStatus::PENDING;
            task.scheduleTime = GetCurrentTime() + 1000; // 1秒后重试
        } else {
            task.status = TaskStatus::FAILED;
        }
    }
    
    UpdateStatistics(task, success);
    
    // 执行回调
    if (task.callback) {
        try {
            task.callback(success);
        } catch (const std::exception& e) {
            std::cerr << "[TaskScheduler] Error in task callback: " << e.what() << std::endl;
        }
    }
}

void TaskScheduler::ScheduleTaskInternal(std::shared_ptr<ScheduledTask> task) {
    {
        std::lock_guard<std::mutex> lock(m_queueMutex);
        if (m_taskQueue.size() >= m_maxQueueSize) {
            std::cerr << "[TaskScheduler] Task queue full, dropping task: " << task->taskName << std::endl;
            return;
        }
        
        m_taskQueue.push(task);
    }
    
    m_queueCondition.notify_one();
}

bool TaskScheduler::ShouldExecuteTask(const ScheduledTask& task) const {
    DWORD currentTime = GetCurrentTime();
    return task.enabled && currentTime >= task.scheduleTime;
}

void TaskScheduler::RescheduleRepeatingTask(ScheduledTask& task) {
    task.status = TaskStatus::PENDING;
    task.scheduleTime = GetCurrentTime() + task.interval;
    task.retryCount = 0;
    task.errorMessage.clear();
}

uint32_t TaskScheduler::GenerateTaskId() {
    return m_nextTaskId.fetch_add(1);
}

bool TaskScheduler::TaskComparator(const std::shared_ptr<ScheduledTask>& a, const std::shared_ptr<ScheduledTask>& b) {
    // 优先级高的任务优先执行
    if (a->priority != b->priority) {
        return a->priority < b->priority; // 注意：priority_queue是最大堆，所以这里返回相反的比较结果
    }
    
    // 相同优先级按调度时间排序
    return a->scheduleTime > b->scheduleTime;
}

bool TaskScheduler::SystemMaintenanceTask() {
    std::cout << "[TaskScheduler] Executing system maintenance task" << std::endl;
    // 系统维护逻辑
    return true;
}

bool TaskScheduler::SystemCleanupTask() {
    std::cout << "[TaskScheduler] Executing system cleanup task" << std::endl;
    // 系统清理逻辑
    return true;
}

bool TaskScheduler::SystemSaveTask() {
    std::cout << "[TaskScheduler] Executing system save task" << std::endl;
    // 系统保存逻辑
    return true;
}

bool TaskScheduler::SystemMonitorTask() {
    std::cout << "[TaskScheduler] Executing system monitor task" << std::endl;
    // 系统监控逻辑
    return true;
}

void TaskScheduler::UpdateStatistics(const ScheduledTask& task, bool success) {
    std::lock_guard<std::mutex> lock(m_statisticsMutex);
    
    if (m_statistics.runningTasks > 0) {
        --m_statistics.runningTasks;
    }
    
    if (success) {
        ++m_statistics.completedTasks;
    } else {
        ++m_statistics.failedTasks;
    }
    
    // 更新执行时间统计
    if (task.executeTime > 0 && task.completeTime > task.executeTime) {
        DWORD executionTime = task.completeTime - task.executeTime;
        m_statistics.totalExecutionTime += executionTime;
        
        uint64_t totalCompleted = m_statistics.completedTasks + m_statistics.failedTasks;
        if (totalCompleted > 0) {
            m_statistics.averageExecutionTime = m_statistics.totalExecutionTime / totalCompleted;
        }
    }
    
    // 更新成功率
    uint64_t totalFinished = m_statistics.completedTasks + m_statistics.failedTasks;
    if (totalFinished > 0) {
        m_statistics.successRate = (double)m_statistics.completedTasks / totalFinished * 100.0;
    }
}
