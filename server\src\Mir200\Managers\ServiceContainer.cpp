#include "ServiceContainer.h"
#include "GameConfigManager.h"
#include "LogManager.h"
#include "DatabaseManager.h"
#include "ItemManager.h"
#include "MapManager.h"
#include "NPCManager.h"
#include "MonsterManager.h"
#include "MagicManager.h"
#include <iostream>
#include <algorithm>

ServiceContainer::ServiceContainer() : m_initialized(false) {
    m_eventBus = std::make_unique<EventBus>();
}

ServiceContainer::~ServiceContainer() {
    Finalize();
}

bool ServiceContainer::Initialize() {
    if (m_initialized) return true;
    
    std::cout << "[ServiceContainer] Initializing..." << std::endl;
    
    // 启动事件总线
    if (!m_eventBus->Start()) {
        std::cerr << "[ServiceContainer] Failed to start EventBus" << std::endl;
        return false;
    }
    
    // 设置Manager间依赖关系
    SetupManagerDependencies();
    
    // 设置事件订阅
    SetupEventSubscriptions();
    
    // 初始化所有Manager
    if (!InitializeAllManagers()) {
        std::cerr << "[ServiceContainer] Failed to initialize managers" << std::endl;
        return false;
    }
    
    m_initialized = true;
    std::cout << "[ServiceContainer] Initialized successfully" << std::endl;
    return true;
}

void ServiceContainer::Finalize() {
    if (!m_initialized) return;
    
    std::cout << "[ServiceContainer] Finalizing..." << std::endl;
    
    // 清理所有Manager
    FinalizeAllManagers();
    
    // 停止事件总线
    if (m_eventBus) {
        m_eventBus->Stop();
    }
    
    m_initialized = false;
    std::cout << "[ServiceContainer] Finalized" << std::endl;
}

IManager* ServiceContainer::GetManager(const std::string& name) {
    auto it = m_managersByName.find(name);
    return (it != m_managersByName.end()) ? it->second.get() : nullptr;
}

std::vector<std::string> ServiceContainer::GetManagerNames() const {
    std::vector<std::string> names;
    names.reserve(m_managersByName.size());

    for (const auto& pair : m_managersByName) {
        names.push_back(pair.first);
    }

    return names;
}

void ServiceContainer::UpdateAllManagers() {
    for (const auto& pair : m_managersByName) {
        const std::shared_ptr<IManager>& manager = pair.second;
        if (manager) {
            manager->Update();
        }
    }
}

void ServiceContainer::SetupManagerDependencies() {
    std::cout << "[ServiceContainer] Setting up manager dependencies..." << std::endl;

    // ItemManager依赖设置
    auto itemManager = GetManager<ItemManager>();
    if (itemManager) {
        itemManager->SetEventBus(m_eventBus.get());
        // itemManager->SetPlayerProvider(GetService<IPlayerProvider>());
    }

    // MapManager依赖设置
    auto mapManager = GetManager<MapManager>();
    if (mapManager) {
        mapManager->SetEventBus(m_eventBus.get());
    }

    // NPCManager依赖设置
    auto npcManager = GetManager<NPCManager>();
    if (npcManager) {
        npcManager->SetEventBus(m_eventBus.get());
        npcManager->SetScriptManager(GetManager<ScriptManager>());
    }

    // MonsterManager依赖设置
    auto monsterManager = GetManager<MonsterManager>();
    if (monsterManager) {
        monsterManager->SetEventBus(m_eventBus.get());
        // monsterManager->SetPlayerProvider(GetService<IPlayerProvider>());
    }

    // MagicManager依赖设置
    auto magicManager = GetManager<MagicManager>();
    if (magicManager) {
        magicManager->SetEventBus(m_eventBus.get());
        // magicManager->SetPlayerProvider(GetService<IPlayerProvider>());
    }

    std::cout << "[ServiceContainer] Manager dependencies setup completed" << std::endl;
}

void ServiceContainer::SetupEventSubscriptions() {
    // 自动设置各Manager的事件订阅
    for (const auto& pair : m_managersByName) {
        const std::string& name = pair.first;
        const std::shared_ptr<IManager>& manager = pair.second;
        if (auto subscriber = std::dynamic_pointer_cast<IEventSubscriber>(manager)) {
            SetupManagerEventSubscription(subscriber.get(), name);
        }
    }

    std::cout << "[ServiceContainer] Event subscriptions setup completed" << std::endl;
}

bool ServiceContainer::InitializeAllManagers() {
    // 按依赖顺序初始化Manager
    std::vector<std::string> initOrder = {
        "LogManager",           // 日志管理器最先初始化
        "GameConfigManager",    // 配置管理器
        "DatabaseManager",      // 数据库管理器
        "ItemManager",          // 物品管理器
        "MapManager",           // 地图管理器
        "NPCManager",           // NPC管理器
        "MonsterManager",       // 怪物管理器
        "MagicManager"          // 魔法管理器
    };

    // 首先初始化已知顺序的Manager
    for (const std::string& managerName : initOrder) {
        auto manager = GetManager(managerName);
        if (manager) {
            if (!manager->Initialize()) {
                std::cerr << "[ServiceContainer] Failed to initialize manager: " << managerName << std::endl;
                return false;
            }
        }
    }
    
    // 然后初始化其他Manager
    for (const auto& [name, manager] : m_managersByName) {
        if (std::find(initOrder.begin(), initOrder.end(), name) == initOrder.end()) {
            if (!manager->Initialize()) {
                std::cerr << "[ServiceContainer] Failed to initialize manager: " << name << std::endl;
                return false;
            }
        }
    }
    
    return true;
}

void ServiceContainer::FinalizeAllManagers() {
    // 按相反顺序清理Manager
    std::vector<std::string> finalizeOrder = {
        "DatabaseManager",
        "GameConfigManager", 
        "LogManager"
    };
    
    // 首先清理已知顺序的Manager
    for (const std::string& managerName : finalizeOrder) {
        auto manager = GetManager(managerName);
        if (manager) {
            manager->Finalize();
        }
    }
    
    // 然后清理其他Manager
    for (const auto& pair : m_managersByName) {
        const std::string& name = pair.first;
        const std::shared_ptr<IManager>& manager = pair.second;
        if (std::find(finalizeOrder.begin(), finalizeOrder.end(), name) == finalizeOrder.end()) {
            if (manager) {
                manager->Finalize();
            }
        }
    }
    
    // 清理容器
    m_managersByName.clear();
    m_managersByType.clear();
}

void ServiceContainer::SetupManagerEventSubscription(IEventSubscriber* subscriber, const std::string& managerName) {
    if (!subscriber || !m_eventBus) return;
    
    // 这里可以根据Manager类型设置特定的事件订阅
    // 第一阶段暂时不需要复杂的事件订阅
    
    std::cout << "[ServiceContainer] Event subscription setup for: " << managerName << std::endl;
}
