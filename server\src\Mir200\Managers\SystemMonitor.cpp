#include "SystemMonitor.h"
#include "../Common/M2Share.h"
#include <iostream>
#include <sstream>
#include <algorithm>
#include <iomanip>

#ifdef WIN32
#include <windows.h>
#include <psapi.h>
#include <pdh.h>
#pragma comment(lib, "pdh.lib")
#pragma comment(lib, "psapi.lib")
#endif

SystemMonitor::SystemMonitor() 
    : m_updateInterval(5000)
    , m_maxHistorySize(1000)
    , m_enableSystemMonitoring(true)
    , m_enableGameMonitoring(true)
    , m_enableAlerts(true)
    , m_lastUpdateTime(0)
    , m_lastSystemUpdate(0)
    , m_lastGameUpdate(0)
    , m_managerName("SystemMonitor")
    , m_initialized(false) {
}

SystemMonitor::~SystemMonitor() {
    Finalize();
}

bool SystemMonitor::Initialize() {
    std::cout << "[SystemMonitor] Initializing..." << std::endl;
    
    // 重置时间戳
    m_lastUpdateTime = GetCurrentTime();
    m_lastSystemUpdate = m_lastUpdateTime;
    m_lastGameUpdate = m_lastUpdateTime;
    
    // 清理历史数据
    {
        std::lock_guard<std::mutex> lock(m_historyMutex);
        m_systemHistory.clear();
        m_gameHistory.clear();
    }
    
    // 注册内置指标和警报
    RegisterBuiltinMetrics();
    RegisterBuiltinAlerts();
    
    // 初始化系统信息
    if (m_enableSystemMonitoring) {
        UpdateSystemInfo();
    }
    
    // 初始化游戏统计
    if (m_enableGameMonitoring) {
        UpdateGameStats();
    }
    
    m_initialized = true;
    
    std::cout << "[SystemMonitor] Initialized successfully" << std::endl;
    return true;
}

void SystemMonitor::Finalize() {
    if (!m_initialized) return;
    
    std::cout << "[SystemMonitor] Finalizing..." << std::endl;
    
    // 清理指标
    {
        std::lock_guard<std::mutex> lock(m_metricMutex);
        m_metrics.clear();
    }
    
    // 清理警报规则
    {
        std::lock_guard<std::mutex> lock(m_alertMutex);
        m_alertRules.clear();
    }
    
    // 清理历史数据
    {
        std::lock_guard<std::mutex> lock(m_historyMutex);
        m_systemHistory.clear();
        m_gameHistory.clear();
    }
    
    m_initialized = false;
    
    std::cout << "[SystemMonitor] Finalized" << std::endl;
}

void SystemMonitor::Update() {
    if (!m_initialized) return;
    
    DWORD currentTime = GetCurrentTime();
    
    // 检查是否需要更新
    if (currentTime - m_lastUpdateTime < m_updateInterval) {
        return;
    }
    
    m_lastUpdateTime = currentTime;
    
    // 更新系统信息
    if (m_enableSystemMonitoring && currentTime - m_lastSystemUpdate >= m_updateInterval) {
        UpdateSystemInfo();
        m_lastSystemUpdate = currentTime;
    }
    
    // 更新游戏统计
    if (m_enableGameMonitoring && currentTime - m_lastGameUpdate >= m_updateInterval) {
        UpdateGameStats();
        m_lastGameUpdate = currentTime;
    }
    
    // 检查警报
    if (m_enableAlerts) {
        CheckAlerts();
    }
    
    // 清理历史数据
    CleanupHistory();
}

const std::string& SystemMonitor::GetManagerName() const {
    return m_managerName;
}

void SystemMonitor::RegisterMetric(const std::string& name, MetricType type, const std::string& description) {
    std::lock_guard<std::mutex> lock(m_metricMutex);
    
    auto metric = std::make_unique<MonitorMetric>();
    metric->name = name;
    metric->type = type;
    metric->description = description;
    metric->lastUpdateTime = GetCurrentTime();
    
    m_metrics[name] = std::move(metric);
    
    std::cout << "[SystemMonitor] Registered metric: " << name << " (" << GetMetricTypeString(type) << ")" << std::endl;
}

void SystemMonitor::UpdateMetric(const std::string& name, double value) {
    std::lock_guard<std::mutex> lock(m_metricMutex);
    
    auto it = m_metrics.find(name);
    if (it == m_metrics.end()) {
        return;
    }
    
    auto& metric = it->second;
    metric->value = value;
    metric->lastUpdateTime = GetCurrentTime();
    
    // 更新统计信息
    if (metric->count == 0) {
        metric->minValue = value;
        metric->maxValue = value;
        metric->totalValue = value;
    } else {
        metric->minValue = std::min(metric->minValue, value);
        metric->maxValue = std::max(metric->maxValue, value);
        
        if (metric->type == MetricType::COUNTER) {
            metric->totalValue += value;
        } else {
            metric->totalValue += value;
        }
    }
    
    ++metric->count;
}

void SystemMonitor::IncrementCounter(const std::string& name, double increment) {
    std::lock_guard<std::mutex> lock(m_metricMutex);
    
    auto it = m_metrics.find(name);
    if (it == m_metrics.end()) {
        return;
    }
    
    auto& metric = it->second;
    if (metric->type == MetricType::COUNTER) {
        metric->value += increment;
        metric->totalValue += increment;
        metric->lastUpdateTime = GetCurrentTime();
        ++metric->count;
    }
}

const MonitorMetric* SystemMonitor::GetMetric(const std::string& name) const {
    std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(m_metricMutex));
    
    auto it = m_metrics.find(name);
    return (it != m_metrics.end()) ? it->second.get() : nullptr;
}

void SystemMonitor::RegisterAlert(const std::string& name, const std::string& metricName, 
                                 const std::string& condition, double threshold, 
                                 MonitorLevel level, DWORD duration) {
    std::lock_guard<std::mutex> lock(m_alertMutex);
    
    auto alert = std::make_unique<AlertRule>();
    alert->name = name;
    alert->metricName = metricName;
    alert->condition = condition;
    alert->threshold = threshold;
    alert->level = level;
    alert->duration = duration;
    alert->enabled = true;
    
    m_alertRules[name] = std::move(alert);
    
    std::cout << "[SystemMonitor] Registered alert: " << name << " (" << metricName 
              << " " << condition << " " << threshold << ")" << std::endl;
}

const SystemResourceInfo& SystemMonitor::GetSystemInfo() const {
    std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(m_systemInfoMutex));
    return m_systemInfo;
}

void SystemMonitor::UpdateSystemInfo() {
    std::lock_guard<std::mutex> lock(m_systemInfoMutex);
    
    m_systemInfo.timestamp = GetCurrentTime();
    
    // 收集系统信息
    CollectCPUInfo();
    CollectMemoryInfo();
    CollectNetworkInfo();
    CollectDiskInfo();
    CollectProcessInfo();
    
    // 添加到历史记录
    AddSystemHistory(m_systemInfo);
    
    // 更新相关指标
    UpdateMetric("system.cpu.usage", m_systemInfo.cpuUsage);
    UpdateMetric("system.memory.usage", m_systemInfo.memoryUsage);
    UpdateMetric("system.disk.usage", m_systemInfo.diskUsage);
    UpdateMetric("system.network.connections", m_systemInfo.networkConnections);
}

const GameServerStats& SystemMonitor::GetGameStats() const {
    std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(m_gameStatsMutex));
    return m_gameStats;
}

void SystemMonitor::UpdateGameStats() {
    std::lock_guard<std::mutex> lock(m_gameStatsMutex);
    
    m_gameStats.timestamp = GetCurrentTime();
    
    // 这里应该从游戏引擎获取实际统计数据
    // 目前使用模拟数据
    
    // 添加到历史记录
    AddGameHistory(m_gameStats);
    
    // 更新相关指标
    UpdateMetric("game.players.online", m_gameStats.onlinePlayerCount);
    UpdateMetric("game.messages.per_second", m_gameStats.messagesPerSecond);
    UpdateMetric("game.database.avg_query_time", m_gameStats.averageQueryTime);
    UpdateMetric("game.performance.avg_tick_time", m_gameStats.averageTickTime);
}

void SystemMonitor::RecordPlayerLogin() {
    std::lock_guard<std::mutex> lock(m_gameStatsMutex);
    ++m_gameStats.totalLoginCount;
    ++m_gameStats.onlinePlayerCount;
    
    if (m_gameStats.onlinePlayerCount > m_gameStats.maxOnlinePlayerCount) {
        m_gameStats.maxOnlinePlayerCount = m_gameStats.onlinePlayerCount;
    }
    
    IncrementCounter("game.players.login_count");
}

void SystemMonitor::RecordPlayerLogout() {
    std::lock_guard<std::mutex> lock(m_gameStatsMutex);
    ++m_gameStats.totalLogoutCount;
    if (m_gameStats.onlinePlayerCount > 0) {
        --m_gameStats.onlinePlayerCount;
    }
    
    IncrementCounter("game.players.logout_count");
}

void SystemMonitor::RecordMessageReceived() {
    std::lock_guard<std::mutex> lock(m_gameStatsMutex);
    ++m_gameStats.totalMessagesReceived;
    
    IncrementCounter("game.messages.received");
}

void SystemMonitor::RecordMessageSent() {
    std::lock_guard<std::mutex> lock(m_gameStatsMutex);
    ++m_gameStats.totalMessagesSent;
    
    IncrementCounter("game.messages.sent");
}

void SystemMonitor::RecordDatabaseQuery(DWORD duration, bool success) {
    std::lock_guard<std::mutex> lock(m_gameStatsMutex);
    ++m_gameStats.totalDatabaseQueries;
    
    if (!success) {
        ++m_gameStats.databaseQueryErrors;
    }
    
    // 更新平均查询时间
    if (m_gameStats.totalDatabaseQueries > 0) {
        m_gameStats.averageQueryTime = 
            (m_gameStats.averageQueryTime * (m_gameStats.totalDatabaseQueries - 1) + duration) / 
            m_gameStats.totalDatabaseQueries;
    }
    
    IncrementCounter("game.database.queries");
    if (!success) {
        IncrementCounter("game.database.errors");
    }
    
    UpdateMetric("game.database.query_time", duration);
}

void SystemMonitor::RecordError(MonitorLevel level) {
    std::lock_guard<std::mutex> lock(m_gameStatsMutex);
    ++m_gameStats.totalErrors;
    
    if (level == MonitorLevel::CRITICAL) {
        ++m_gameStats.criticalErrors;
        IncrementCounter("game.errors.critical");
    } else if (level == MonitorLevel::WARNING) {
        ++m_gameStats.warningCount;
        IncrementCounter("game.errors.warning");
    }
    
    IncrementCounter("game.errors.total");
}

void SystemMonitor::RecordTick(DWORD duration) {
    std::lock_guard<std::mutex> lock(m_gameStatsMutex);
    ++m_gameStats.totalTicks;
    
    // 更新平均Tick时间
    if (m_gameStats.totalTicks > 0) {
        m_gameStats.averageTickTime = 
            (m_gameStats.averageTickTime * (m_gameStats.totalTicks - 1) + duration) / 
            m_gameStats.totalTicks;
    }
    
    // 更新最大Tick时间
    if (duration > m_gameStats.maxTickTime) {
        m_gameStats.maxTickTime = duration;
    }
    
    UpdateMetric("game.performance.tick_time", duration);
}

void SystemMonitor::RegisterBuiltinMetrics() {
    // 系统指标
    RegisterMetric("system.cpu.usage", MetricType::GAUGE, "CPU使用率(%)");
    RegisterMetric("system.memory.usage", MetricType::GAUGE, "内存使用率(%)");
    RegisterMetric("system.disk.usage", MetricType::GAUGE, "磁盘使用率(%)");
    RegisterMetric("system.network.connections", MetricType::GAUGE, "网络连接数");
    
    // 游戏指标
    RegisterMetric("game.players.online", MetricType::GAUGE, "在线玩家数");
    RegisterMetric("game.players.login_count", MetricType::COUNTER, "登录次数");
    RegisterMetric("game.players.logout_count", MetricType::COUNTER, "登出次数");
    RegisterMetric("game.messages.received", MetricType::COUNTER, "接收消息数");
    RegisterMetric("game.messages.sent", MetricType::COUNTER, "发送消息数");
    RegisterMetric("game.messages.per_second", MetricType::GAUGE, "每秒消息数");
    RegisterMetric("game.database.queries", MetricType::COUNTER, "数据库查询数");
    RegisterMetric("game.database.errors", MetricType::COUNTER, "数据库错误数");
    RegisterMetric("game.database.query_time", MetricType::TIMER, "数据库查询时间");
    RegisterMetric("game.database.avg_query_time", MetricType::GAUGE, "平均查询时间");
    RegisterMetric("game.errors.total", MetricType::COUNTER, "总错误数");
    RegisterMetric("game.errors.critical", MetricType::COUNTER, "严重错误数");
    RegisterMetric("game.errors.warning", MetricType::COUNTER, "警告数");
    RegisterMetric("game.performance.tick_time", MetricType::TIMER, "Tick时间");
    RegisterMetric("game.performance.avg_tick_time", MetricType::GAUGE, "平均Tick时间");
    
    std::cout << "[SystemMonitor] Registered builtin metrics" << std::endl;
}

void SystemMonitor::RegisterBuiltinAlerts() {
    // 系统警报
    RegisterAlert("HighCPUUsage", "system.cpu.usage", ">", 80.0, MonitorLevel::WARNING, 30000);
    RegisterAlert("HighMemoryUsage", "system.memory.usage", ">", 85.0, MonitorLevel::WARNING, 30000);
    RegisterAlert("HighDiskUsage", "system.disk.usage", ">", 90.0, MonitorLevel::WARNING, 60000);
    
    // 游戏警报
    RegisterAlert("HighTickTime", "game.performance.avg_tick_time", ">", 100.0, MonitorLevel::WARNING, 10000);
    RegisterAlert("DatabaseErrors", "game.database.errors", ">", 10.0, MonitorLevel::ERROR_LEVEL, 5000);
    RegisterAlert("CriticalErrors", "game.errors.critical", ">", 0.0, MonitorLevel::CRITICAL, 0);
    
    std::cout << "[SystemMonitor] Registered builtin alerts" << std::endl;
}

void SystemMonitor::CollectCPUInfo() {
    // 简化的CPU信息收集
    m_systemInfo.cpuUsage = 15.5; // 模拟数据
    m_systemInfo.cpuUserTime = 10.2;
    m_systemInfo.cpuSystemTime = 5.3;
}

void SystemMonitor::CollectMemoryInfo() {
    // 简化的内存信息收集
    m_systemInfo.totalMemory = 8ULL * 1024 * 1024 * 1024; // 8GB
    m_systemInfo.usedMemory = 4ULL * 1024 * 1024 * 1024;  // 4GB
    m_systemInfo.freeMemory = m_systemInfo.totalMemory - m_systemInfo.usedMemory;
    m_systemInfo.memoryUsage = (double)m_systemInfo.usedMemory / m_systemInfo.totalMemory * 100.0;
}

void SystemMonitor::CollectNetworkInfo() {
    // 简化的网络信息收集
    m_systemInfo.networkBytesReceived = 1024 * 1024; // 1MB
    m_systemInfo.networkBytesSent = 512 * 1024;      // 512KB
    m_systemInfo.networkConnections = 50;
}

void SystemMonitor::CollectDiskInfo() {
    // 简化的磁盘信息收集
    m_systemInfo.diskTotalSpace = 500ULL * 1024 * 1024 * 1024; // 500GB
    m_systemInfo.diskFreeSpace = 200ULL * 1024 * 1024 * 1024;  // 200GB
    m_systemInfo.diskUsage = (double)(m_systemInfo.diskTotalSpace - m_systemInfo.diskFreeSpace) / 
                            m_systemInfo.diskTotalSpace * 100.0;
}

void SystemMonitor::CollectProcessInfo() {
    // 简化的进程信息收集
    m_systemInfo.processMemory = 256 * 1024 * 1024; // 256MB
    m_systemInfo.threadCount = 20;
    m_systemInfo.handleCount = 500;
}

void SystemMonitor::CheckAlerts() {
    std::lock_guard<std::mutex> lock(m_alertMutex);
    
    DWORD currentTime = GetCurrentTime();
    
    for (auto& pair : m_alertRules) {
        auto& alert = pair.second;
        
        if (!alert->enabled) continue;
        
        const MonitorMetric* metric = GetMetric(alert->metricName);
        if (!metric) continue;
        
        bool conditionMet = EvaluateCondition(alert->condition, metric->value, alert->threshold);
        
        if (conditionMet) {
            if (!alert->triggered || (currentTime - alert->lastTriggerTime) >= alert->duration) {
                TriggerAlert(*alert, metric->value);
            }
        } else {
            alert->triggered = false;
        }
    }
}

void SystemMonitor::TriggerAlert(AlertRule& rule, double currentValue) {
    rule.triggered = true;
    rule.lastTriggerTime = GetCurrentTime();
    
    std::cout << "[SystemMonitor] ALERT [" << GetMonitorLevelString(rule.level) << "] " 
              << rule.name << ": " << rule.metricName << " = " << currentValue 
              << " " << rule.condition << " " << rule.threshold << std::endl;
    
    if (rule.callback) {
        try {
            rule.callback(rule, currentValue);
        } catch (const std::exception& e) {
            std::cerr << "[SystemMonitor] Error in alert callback: " << e.what() << std::endl;
        }
    }
}

bool SystemMonitor::EvaluateCondition(const std::string& condition, double value, double threshold) {
    if (condition == ">") return value > threshold;
    if (condition == "<") return value < threshold;
    if (condition == "=") return value == threshold;
    if (condition == ">=") return value >= threshold;
    if (condition == "<=") return value <= threshold;
    if (condition == "!=") return value != threshold;
    
    return false;
}

void SystemMonitor::AddSystemHistory(const SystemResourceInfo& info) {
    std::lock_guard<std::mutex> lock(m_historyMutex);
    
    m_systemHistory.push_back(info);
    
    if (m_systemHistory.size() > m_maxHistorySize) {
        m_systemHistory.erase(m_systemHistory.begin());
    }
}

void SystemMonitor::AddGameHistory(const GameServerStats& stats) {
    std::lock_guard<std::mutex> lock(m_historyMutex);
    
    m_gameHistory.push_back(stats);
    
    if (m_gameHistory.size() > m_maxHistorySize) {
        m_gameHistory.erase(m_gameHistory.begin());
    }
}

void SystemMonitor::CleanupHistory() {
    std::lock_guard<std::mutex> lock(m_historyMutex);
    
    // 清理超过最大大小的历史记录
    if (m_systemHistory.size() > m_maxHistorySize) {
        m_systemHistory.erase(m_systemHistory.begin(), 
                             m_systemHistory.begin() + (m_systemHistory.size() - m_maxHistorySize));
    }
    
    if (m_gameHistory.size() > m_maxHistorySize) {
        m_gameHistory.erase(m_gameHistory.begin(), 
                           m_gameHistory.begin() + (m_gameHistory.size() - m_maxHistorySize));
    }
}

std::string SystemMonitor::GetMetricTypeString(MetricType type) const {
    switch (type) {
        case MetricType::COUNTER: return "COUNTER";
        case MetricType::GAUGE: return "GAUGE";
        case MetricType::HISTOGRAM: return "HISTOGRAM";
        case MetricType::TIMER: return "TIMER";
        default: return "UNKNOWN";
    }
}

std::string SystemMonitor::GetMonitorLevelString(MonitorLevel level) const {
    switch (level) {
        case MonitorLevel::DEBUG: return "DEBUG";
        case MonitorLevel::INFO: return "INFO";
        case MonitorLevel::WARNING: return "WARNING";
        case MonitorLevel::ERROR_LEVEL: return "ERROR";
        case MonitorLevel::CRITICAL: return "CRITICAL";
        default: return "UNKNOWN";
    }
}
