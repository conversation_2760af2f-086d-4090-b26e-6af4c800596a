// Environment Test Debug - Simple debug test

#include "Environment.h"
#include <iostream>

// Simple mock implementations
namespace g_functions {
    void MainOutMessage(const std::string& msg) {
        std::cout << "[LOG] " << msg << std::endl;
    }
}

int main() {
    std::cout << "Starting debug test..." << std::endl;
    
    try {
        std::cout << "Creating Environment with name only..." << std::endl;
        Environment env1("TestMap1");
        std::cout << "Map name: " << env1.GetMapName() << std::endl;
        std::cout << "Initialized: " << env1.IsInitialized() << std::endl;
        std::cout << "Active: " << env1.IsActive() << std::endl;
        
        std::cout << "Creating Environment with dimensions..." << std::endl;
        Environment env2("TestMap2", 100, 100);
        std::cout << "Map name: " << env2.GetMapName() << std::endl;
        std::cout << "Width: " << env2.GetWidth() << std::endl;
        std::cout << "Height: " << env2.GetHeight() << std::endl;
        
        std::cout << "Initializing environment..." << std::endl;
        bool result = env1.Initialize();
        std::cout << "Initialize result: " << result << std::endl;
        std::cout << "Initialized: " << env1.IsInitialized() << std::endl;
        std::cout << "Active: " << env1.IsActive() << std::endl;
        
        std::cout << "Testing map flags..." << std::endl;
        Point testPos(25, 25);
        std::cout << "IsSafeZone: " << env1.IsSafeZone(testPos) << std::endl;
        
        MapFlags flags;
        flags.is_safe = true;
        env1.SetMapFlags(flags);
        std::cout << "IsSafeZone after setting flag: " << env1.IsSafeZone(testPos) << std::endl;
        
        std::cout << "Testing environment info..." << std::endl;
        std::string info = env1.GetEnvironmentInfo();
        std::cout << "Environment info: " << info << std::endl;
        
        std::cout << "Debug test completed successfully!" << std::endl;
        return 0;
        
    } catch (const std::exception& e) {
        std::cout << "Exception: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cout << "Unknown exception" << std::endl;
        return 1;
    }
}
