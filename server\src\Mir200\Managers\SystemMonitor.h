#pragma once

#include "IManager.h"
#include "../Common/Types.h"
#include <string>
#include <vector>
#include <memory>
#include <mutex>
#include <atomic>
#include <unordered_map>
#include <chrono>
#include <functional>

/**
 * @brief 监控指标类型枚举
 * 基于原项目的系统监控需求
 */
enum class MetricType {
    COUNTER = 1,            // 计数器（累加）
    GAUGE = 2,              // 仪表（当前值）
    HISTOGRAM = 3,          // 直方图（分布）
    TIMER = 4               // 计时器（耗时）
};

/**
 * @brief 监控级别枚举
 */
enum class MonitorLevel {
    DEBUG = 0,              // 调试级别
    INFO = 1,               // 信息级别
    WARNING = 2,            // 警告级别
    ERROR_LEVEL = 3,        // 错误级别
    CRITICAL = 4            // 严重级别
};

/**
 * @brief 系统资源信息
 * 基于原项目的系统监控需求
 */
struct SystemResourceInfo {
    // CPU信息
    double cpuUsage;                    // CPU使用率(%)
    double cpuUserTime;                 // 用户态时间(%)
    double cpuSystemTime;               // 系统态时间(%)
    
    // 内存信息
    uint64_t totalMemory;               // 总内存(字节)
    uint64_t usedMemory;                // 已用内存(字节)
    uint64_t freeMemory;                // 空闲内存(字节)
    double memoryUsage;                 // 内存使用率(%)
    
    // 进程信息
    uint64_t processMemory;             // 进程内存使用(字节)
    uint32_t threadCount;               // 线程数量
    uint32_t handleCount;               // 句柄数量
    
    // 网络信息
    uint64_t networkBytesReceived;      // 网络接收字节数
    uint64_t networkBytesSent;          // 网络发送字节数
    uint32_t networkConnections;        // 网络连接数
    
    // 磁盘信息
    uint64_t diskTotalSpace;            // 磁盘总空间(字节)
    uint64_t diskFreeSpace;             // 磁盘空闲空间(字节)
    double diskUsage;                   // 磁盘使用率(%)
    
    DWORD timestamp;                    // 时间戳
    
    SystemResourceInfo() 
        : cpuUsage(0.0), cpuUserTime(0.0), cpuSystemTime(0.0)
        , totalMemory(0), usedMemory(0), freeMemory(0), memoryUsage(0.0)
        , processMemory(0), threadCount(0), handleCount(0)
        , networkBytesReceived(0), networkBytesSent(0), networkConnections(0)
        , diskTotalSpace(0), diskFreeSpace(0), diskUsage(0.0)
        , timestamp(0) {
    }
};

/**
 * @brief 游戏服务器统计信息
 * 基于原项目的游戏服务器监控需求
 */
struct GameServerStats {
    // 玩家统计
    uint32_t onlinePlayerCount;         // 在线玩家数
    uint32_t maxOnlinePlayerCount;      // 最大在线玩家数
    uint32_t totalLoginCount;           // 总登录次数
    uint32_t totalLogoutCount;          // 总登出次数
    
    // 消息统计
    uint64_t totalMessagesReceived;     // 总接收消息数
    uint64_t totalMessagesSent;         // 总发送消息数
    uint64_t messagesPerSecond;         // 每秒消息数
    
    // 数据库统计
    uint64_t totalDatabaseQueries;      // 总数据库查询数
    uint64_t databaseQueryErrors;       // 数据库查询错误数
    double averageQueryTime;            // 平均查询时间(毫秒)
    
    // 错误统计
    uint64_t totalErrors;               // 总错误数
    uint64_t criticalErrors;            // 严重错误数
    uint64_t warningCount;              // 警告数
    
    // 性能统计
    double averageTickTime;             // 平均Tick时间(毫秒)
    double maxTickTime;                 // 最大Tick时间(毫秒)
    uint64_t totalTicks;                // 总Tick数
    
    DWORD timestamp;                    // 时间戳
    
    GameServerStats() 
        : onlinePlayerCount(0), maxOnlinePlayerCount(0), totalLoginCount(0), totalLogoutCount(0)
        , totalMessagesReceived(0), totalMessagesSent(0), messagesPerSecond(0)
        , totalDatabaseQueries(0), databaseQueryErrors(0), averageQueryTime(0.0)
        , totalErrors(0), criticalErrors(0), warningCount(0)
        , averageTickTime(0.0), maxTickTime(0.0), totalTicks(0)
        , timestamp(0) {
    }
};

/**
 * @brief 监控指标结构
 */
struct MonitorMetric {
    std::string name;                   // 指标名称
    MetricType type;                    // 指标类型
    double value;                       // 当前值
    double minValue;                    // 最小值
    double maxValue;                    // 最大值
    double totalValue;                  // 总值
    uint64_t count;                     // 计数
    DWORD lastUpdateTime;               // 最后更新时间
    std::string description;            // 描述
    
    MonitorMetric() 
        : type(MetricType::GAUGE), value(0.0), minValue(0.0), maxValue(0.0)
        , totalValue(0.0), count(0), lastUpdateTime(0) {
    }
};

/**
 * @brief 警报规则结构
 */
struct AlertRule {
    std::string name;                   // 规则名称
    std::string metricName;             // 监控指标名称
    std::string condition;              // 条件表达式 (>, <, =, >=, <=)
    double threshold;                   // 阈值
    MonitorLevel level;                 // 警报级别
    DWORD duration;                     // 持续时间(毫秒)
    bool enabled;                       // 是否启用
    std::function<void(const AlertRule&, double)> callback; // 警报回调
    
    DWORD lastTriggerTime;              // 最后触发时间
    bool triggered;                     // 是否已触发
    
    AlertRule() 
        : threshold(0.0), level(MonitorLevel::WARNING), duration(0)
        , enabled(true), lastTriggerTime(0), triggered(false) {
    }
};

/**
 * @brief 系统监控管理器
 * 基于原项目的系统监控需求，提供全面的系统和游戏服务器监控
 * 包括资源监控、性能监控、错误监控、警报系统等
 */
class SystemMonitor : public IManager {
private:
    // 监控指标存储
    std::unordered_map<std::string, std::unique_ptr<MonitorMetric>> m_metrics;
    std::mutex m_metricMutex;
    
    // 警报规则存储
    std::unordered_map<std::string, std::unique_ptr<AlertRule>> m_alertRules;
    std::mutex m_alertMutex;
    
    // 系统资源信息
    SystemResourceInfo m_systemInfo;
    std::mutex m_systemInfoMutex;
    
    // 游戏服务器统计
    GameServerStats m_gameStats;
    std::mutex m_gameStatsMutex;
    
    // 历史数据存储
    std::vector<SystemResourceInfo> m_systemHistory;
    std::vector<GameServerStats> m_gameHistory;
    std::mutex m_historyMutex;
    
    // 配置参数
    DWORD m_updateInterval;             // 更新间隔(毫秒)
    size_t m_maxHistorySize;            // 最大历史记录数
    bool m_enableSystemMonitoring;      // 是否启用系统监控
    bool m_enableGameMonitoring;        // 是否启用游戏监控
    bool m_enableAlerts;                // 是否启用警报
    
    // 时间记录
    DWORD m_lastUpdateTime;
    DWORD m_lastSystemUpdate;
    DWORD m_lastGameUpdate;
    
    // 管理器状态
    std::string m_managerName;
    bool m_initialized;

public:
    SystemMonitor();
    virtual ~SystemMonitor();
    
    // IManager接口实现
    bool Initialize() override;
    void Finalize() override;
    void Update() override;
    const std::string& GetManagerName() const override;
    
    // 指标管理
    void RegisterMetric(const std::string& name, MetricType type, const std::string& description = "");
    void UpdateMetric(const std::string& name, double value);
    void IncrementCounter(const std::string& name, double increment = 1.0);
    void RecordTimer(const std::string& name, DWORD duration);
    
    // 指标查询
    const MonitorMetric* GetMetric(const std::string& name) const;
    std::vector<const MonitorMetric*> GetAllMetrics() const;
    std::vector<const MonitorMetric*> GetMetricsByType(MetricType type) const;
    
    // 警报管理
    void RegisterAlert(const std::string& name, const std::string& metricName, 
                      const std::string& condition, double threshold, 
                      MonitorLevel level = MonitorLevel::WARNING, DWORD duration = 0);
    void EnableAlert(const std::string& name, bool enabled);
    void SetAlertCallback(const std::string& name, std::function<void(const AlertRule&, double)> callback);
    
    // 系统监控
    const SystemResourceInfo& GetSystemInfo() const;
    void UpdateSystemInfo();
    std::vector<SystemResourceInfo> GetSystemHistory(size_t count = 0) const;
    
    // 游戏监控
    const GameServerStats& GetGameStats() const;
    void UpdateGameStats();
    std::vector<GameServerStats> GetGameHistory(size_t count = 0) const;
    
    // 游戏统计更新方法
    void RecordPlayerLogin();
    void RecordPlayerLogout();
    void RecordMessageReceived();
    void RecordMessageSent();
    void RecordDatabaseQuery(DWORD duration, bool success = true);
    void RecordError(MonitorLevel level = MonitorLevel::ERROR_LEVEL);
    void RecordTick(DWORD duration);
    
    // 配置管理
    void SetUpdateInterval(DWORD interval) { m_updateInterval = interval; }
    void SetMaxHistorySize(size_t maxSize) { m_maxHistorySize = maxSize; }
    void EnableSystemMonitoring(bool enabled) { m_enableSystemMonitoring = enabled; }
    void EnableGameMonitoring(bool enabled) { m_enableGameMonitoring = enabled; }
    void EnableAlerts(bool enabled) { m_enableAlerts = enabled; }
    
    // 报告生成
    std::string GenerateSystemReport() const;
    std::string GenerateGameReport() const;
    std::string GenerateMetricsReport() const;
    std::string GenerateAlertsReport() const;
    
    // 调试和监控
    void DumpSystemInfo() const;
    void DumpGameStats() const;
    void DumpMetrics() const;
    void DumpAlerts() const;

private:
    // 系统信息收集
    void CollectCPUInfo();
    void CollectMemoryInfo();
    void CollectNetworkInfo();
    void CollectDiskInfo();
    void CollectProcessInfo();
    
    // 警报处理
    void CheckAlerts();
    void TriggerAlert(AlertRule& rule, double currentValue);
    bool EvaluateCondition(const std::string& condition, double value, double threshold);
    
    // 历史数据管理
    void AddSystemHistory(const SystemResourceInfo& info);
    void AddGameHistory(const GameServerStats& stats);
    void CleanupHistory();
    
    // 内置指标注册
    void RegisterBuiltinMetrics();
    void RegisterBuiltinAlerts();
    
    // 工具方法
    std::string GetMetricTypeString(MetricType type) const;
    std::string GetMonitorLevelString(MonitorLevel level) const;
    std::string FormatBytes(uint64_t bytes) const;
    std::string FormatPercentage(double percentage) const;
};
