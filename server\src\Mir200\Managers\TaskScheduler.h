#pragma once

#include "IManager.h"
#include "../Common/Types.h"
#include <string>
#include <vector>
#include <memory>
#include <mutex>
#include <thread>
#include <atomic>
#include <queue>
#include <functional>
#include <chrono>
#include <condition_variable>
#include <unordered_map>

/**
 * @brief 任务类型枚举
 * 基于原项目Timer相关功能的任务类型
 */
enum class TaskType {
    SYSTEM_TASK = 1,        // 系统任务
    MAINTENANCE_TASK = 2,   // 维护任务
    CLEANUP_TASK = 3,       // 清理任务
    SAVE_TASK = 4,          // 保存任务
    MONITOR_TASK = 5,       // 监控任务
    EVENT_TASK = 6,         // 事件任务
    BACKUP_TASK = 7,        // 备份任务
    NOTIFICATION_TASK = 8   // 通知任务
};

/**
 * @brief 任务优先级枚举
 */
enum class TaskPriority {
    LOW = 0,               // 低优先级
    NORMAL = 1,            // 普通优先级
    HIGH = 2,              // 高优先级
    CRITICAL = 3           // 关键优先级
};

/**
 * @brief 任务状态枚举
 */
enum class TaskStatus {
    PENDING = 0,           // 等待执行
    RUNNING = 1,           // 正在执行
    COMPLETED = 2,         // 已完成
    FAILED = 3,            // 执行失败
    CANCELLED = 4,         // 已取消
    PAUSED = 5             // 已暂停
};

/**
 * @brief 调度任务结构
 * 基于原项目Timer功能的任务结构
 */
struct ScheduledTask {
    uint32_t taskId;                    // 任务ID
    std::string taskName;               // 任务名称
    TaskType type;                      // 任务类型
    TaskPriority priority;              // 优先级
    TaskStatus status;                  // 状态
    
    std::function<bool()> taskFunction; // 任务函数
    std::function<void(bool)> callback; // 完成回调
    
    DWORD createTime;                   // 创建时间
    DWORD scheduleTime;                 // 调度时间
    DWORD executeTime;                  // 执行时间
    DWORD completeTime;                 // 完成时间
    
    DWORD interval;                     // 执行间隔(毫秒)
    int repeatCount;                    // 重复次数(-1表示无限重复)
    int currentCount;                   // 当前执行次数
    
    DWORD timeout;                      // 超时时间(毫秒)
    int retryCount;                     // 重试次数
    int maxRetries;                     // 最大重试次数
    
    bool enabled;                       // 是否启用
    bool persistent;                    // 是否持久化
    std::string errorMessage;           // 错误信息
    
    ScheduledTask() 
        : taskId(0), type(TaskType::SYSTEM_TASK), priority(TaskPriority::NORMAL), status(TaskStatus::PENDING)
        , createTime(0), scheduleTime(0), executeTime(0), completeTime(0)
        , interval(0), repeatCount(1), currentCount(0)
        , timeout(30000), retryCount(0), maxRetries(3)
        , enabled(true), persistent(false) {
    }
};

/**
 * @brief 任务统计信息
 */
struct TaskStatistics {
    uint64_t totalTasks;                // 总任务数
    uint64_t completedTasks;            // 完成任务数
    uint64_t failedTasks;               // 失败任务数
    uint64_t cancelledTasks;            // 取消任务数
    uint64_t runningTasks;              // 运行中任务数
    uint64_t pendingTasks;              // 等待任务数
    uint64_t totalExecutionTime;        // 总执行时间
    uint64_t averageExecutionTime;      // 平均执行时间
    double successRate;                 // 成功率
    
    TaskStatistics() 
        : totalTasks(0), completedTasks(0), failedTasks(0), cancelledTasks(0)
        , runningTasks(0), pendingTasks(0), totalExecutionTime(0)
        , averageExecutionTime(0), successRate(0.0) {
    }
};

/**
 * @brief 任务调度器
 * 基于原项目Timer功能，提供定时任务管理
 * 支持一次性任务、周期性任务、延迟任务等
 */
class TaskScheduler : public IManager {
private:
    // 任务队列（按优先级和时间排序）
    std::priority_queue<std::shared_ptr<ScheduledTask>, 
                       std::vector<std::shared_ptr<ScheduledTask>>,
                       std::function<bool(const std::shared_ptr<ScheduledTask>&, const std::shared_ptr<ScheduledTask>&)>> m_taskQueue;
    std::mutex m_queueMutex;
    
    // 任务存储
    std::unordered_map<uint32_t, std::shared_ptr<ScheduledTask>> m_tasks;
    std::mutex m_taskMutex;
    
    // 工作线程
    std::vector<std::thread> m_workerThreads;
    std::atomic<bool> m_running;
    std::condition_variable m_queueCondition;
    
    // 任务ID生成器
    std::atomic<uint32_t> m_nextTaskId;
    
    // 统计信息
    TaskStatistics m_statistics;
    std::mutex m_statisticsMutex;
    
    // 配置参数
    size_t m_workerThreadCount;
    size_t m_maxQueueSize;
    DWORD m_defaultTimeout;
    int m_defaultMaxRetries;
    
    // 管理器状态
    std::string m_managerName;
    bool m_initialized;

public:
    TaskScheduler();
    virtual ~TaskScheduler();
    
    // IManager接口实现
    bool Initialize() override;
    void Finalize() override;
    void Update() override;
    const std::string& GetManagerName() const override;
    
    // 任务调度
    uint32_t ScheduleTask(const std::string& taskName, std::function<bool()> taskFunction,
                         DWORD delay = 0, TaskPriority priority = TaskPriority::NORMAL);
    uint32_t ScheduleRepeatingTask(const std::string& taskName, std::function<bool()> taskFunction,
                                  DWORD interval, int repeatCount = -1, 
                                  TaskPriority priority = TaskPriority::NORMAL);
    uint32_t ScheduleDelayedTask(const std::string& taskName, std::function<bool()> taskFunction,
                                DWORD delay, TaskPriority priority = TaskPriority::NORMAL);
    
    // 任务控制
    bool CancelTask(uint32_t taskId);
    bool PauseTask(uint32_t taskId);
    bool ResumeTask(uint32_t taskId);
    bool RetryTask(uint32_t taskId);
    
    // 任务查询
    ScheduledTask* GetTask(uint32_t taskId);
    std::vector<ScheduledTask*> GetTasksByType(TaskType type);
    std::vector<ScheduledTask*> GetTasksByStatus(TaskStatus status);
    std::vector<ScheduledTask*> GetAllTasks();
    
    // 批量操作
    void CancelAllTasks();
    void CancelTasksByType(TaskType type);
    void PauseAllTasks();
    void ResumeAllTasks();
    
    // 系统任务注册
    void RegisterSystemTasks();
    uint32_t ScheduleMaintenanceTask(const std::string& taskName, std::function<bool()> taskFunction, DWORD interval);
    uint32_t ScheduleCleanupTask(const std::string& taskName, std::function<bool()> taskFunction, DWORD interval);
    uint32_t ScheduleSaveTask(const std::string& taskName, std::function<bool()> taskFunction, DWORD interval);
    
    // 配置管理
    void SetWorkerThreadCount(size_t count);
    void SetMaxQueueSize(size_t maxSize) { m_maxQueueSize = maxSize; }
    void SetDefaultTimeout(DWORD timeout) { m_defaultTimeout = timeout; }
    void SetDefaultMaxRetries(int maxRetries) { m_defaultMaxRetries = maxRetries; }
    
    // 统计信息
    TaskStatistics GetStatistics() const;
    size_t GetQueueSize() const;
    size_t GetActiveTaskCount() const;
    std::string GetTaskInfo(uint32_t taskId) const;
    
    // 调试和监控
    void DumpTaskStatistics() const;
    void DumpQueueStatus() const;
    std::vector<std::string> GetTaskList() const;

private:
    // 工作线程函数
    void WorkerThreadFunction();
    
    // 任务处理
    void ProcessTask(std::shared_ptr<ScheduledTask> task);
    bool ExecuteTask(ScheduledTask& task);
    void CompleteTask(ScheduledTask& task, bool success);
    void HandleTaskFailure(ScheduledTask& task);
    
    // 任务调度
    void ScheduleTaskInternal(std::shared_ptr<ScheduledTask> task);
    bool ShouldExecuteTask(const ScheduledTask& task) const;
    void RescheduleRepeatingTask(ScheduledTask& task);
    
    // 统计更新
    void UpdateStatistics(const ScheduledTask& task, bool success);
    void RecalculateStatistics();
    
    // 工具方法
    uint32_t GenerateTaskId();
    std::string GetTaskTypeString(TaskType type) const;
    std::string GetTaskPriorityString(TaskPriority priority) const;
    std::string GetTaskStatusString(TaskStatus status) const;
    
    // 队列比较函数
    static bool TaskComparator(const std::shared_ptr<ScheduledTask>& a, const std::shared_ptr<ScheduledTask>& b);
    
    // 系统任务实现
    bool SystemMaintenanceTask();
    bool SystemCleanupTask();
    bool SystemSaveTask();
    bool SystemMonitorTask();
};
