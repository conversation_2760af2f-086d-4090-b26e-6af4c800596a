@echo off
echo ========================================
echo Building Manager Architecture Phase 1
echo ========================================

REM 设置编译目录
set BUILD_DIR=build_manager_phase1
set SOURCE_DIR=%~dp0

REM 清理旧的编译目录
if exist "%BUILD_DIR%" (
    echo Cleaning old build directory...
    rmdir /s /q "%BUILD_DIR%"
)

REM 创建编译目录
mkdir "%BUILD_DIR%"
cd "%BUILD_DIR%"

echo.
echo Configuring CMake...
cmake -G "Visual Studio 16 2019" -A x64 ^
    -DCMAKE_BUILD_TYPE=Release ^
    -S "%SOURCE_DIR%" ^
    -B . ^
    -f "%SOURCE_DIR%\CMakeLists_Manager_Phase1.txt"

if %ERRORLEVEL% neq 0 (
    echo CMake configuration failed!
    pause
    exit /b 1
)

echo.
echo Building project...
cmake --build . --config Release

if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo ========================================
echo Build completed successfully!
echo ========================================

echo.
echo Running Manager Phase 1 test...
cd bin
if exist "test_manager_phase1.exe" (
    test_manager_phase1.exe
) else (
    echo Test executable not found!
    pause
    exit /b 1
)

echo.
echo ========================================
echo Manager Phase 1 test completed!
echo ========================================

pause
