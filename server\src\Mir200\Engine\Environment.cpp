#include "Environment.h"
#include "Common/M2Share.h"
#include "Objects/BaseObject.h"
#include "Objects/PlayObject.h"
#include <fstream>
#include <algorithm>
#include <cmath>

// Constructor with map name only
Environment::Environment(const std::string& map_name)
    : m_map_name(map_name), m_sub_map_name(map_name) {
    InitializeMapFlags();
}

// Constructor with map name and dimensions
Environment::Environment(const std::string& map_name, int width, int height)
    : m_map_name(map_name), m_sub_map_name(map_name) {
    InitializeMapFlags();
    Initialize(width, height);
}

Environment::~Environment() {
    Finalize();
}

bool Environment::Initialize() {
    TRY_BEGIN
        std::unique_lock<std::shared_mutex> lock(m_mutex);

        g_functions::MainOutMessage("Initializing Environment for map: " + m_map_name);

        // Initialize map flags to default values
        InitializeMapFlags();

        // Initialize door and quest lists
        m_door_list.clear();
        m_quest_list.clear();

        // Reset counters
        m_monster_count = 0;
        m_human_count = 0;

        m_active = true;
        m_initialized = true;

        g_functions::MainOutMessage("Environment initialized successfully for map: " + m_map_name);
        return true;

    TRY_END

    return false;
}

void Environment::Finalize() {
    TRY_BEGIN
        if (!m_initialized) return;

        std::unique_lock<std::shared_mutex> lock(m_mutex);

        g_functions::MainOutMessage("Finalizing Environment for map: " + m_map_name);

        // Save environment data before finalizing
        SaveEnvironmentData();

        // Cleanup map cells
        CleanupMapCells();

        // Clear object lists
        m_door_list.clear();
        m_quest_list.clear();

        // Clear restricted items lists
        m_un_allow_std_items_list.clear();
        m_un_allow_std_items_idx_list.clear();

        m_active = false;
        m_initialized = false;

        g_functions::MainOutMessage("Environment finalized for map: " + m_map_name);

    TRY_END
}

void Environment::ProcessEnvironment() {
    TRY_BEGIN
        if (!m_active || !m_initialized) return;

        // Process map events and effects
        ProcessMapEvents();

    TRY_END
}

void Environment::SaveEnvironmentData() {
    TRY_BEGIN
        std::shared_lock<std::shared_mutex> lock(m_mutex);

        g_functions::MainOutMessage("Saving environment data for map: " + m_map_name);

        // Environment data is typically saved as part of server shutdown
        // or periodic backup operations

        g_functions::MainOutMessage("Environment data saved for map: " + m_map_name);

    TRY_END
}

// Private initialization method for map dimensions
void Environment::Initialize(int width, int height) {
    if (width > 1 && height > 1) {
        // Cleanup existing map data if any
        if (!m_map_cell_array.empty()) {
            CleanupMapCells();
        }

        m_width = width;
        m_height = height;

        // Initialize map cell array
        m_map_cell_array.resize(m_width * m_height);

        // Initialize all cells to default state
        for (auto& cell : m_map_cell_array) {
            cell.flag = 0;  // Walkable by default
            cell.obj_list.clear();
        }
    }
}

// Initialize map flags to default values
void Environment::InitializeMapFlags() {
    m_map_flags = MapFlags{};  // Initialize all to false/0
}

// Get map cell information (non-const version)
bool Environment::GetMapCellInfo(int x, int y, MapCellInfo*& cell_info) {
    if (x >= 0 && x < m_width && y >= 0 && y < m_height) {
        cell_info = &m_map_cell_array[x * m_height + y];
        return true;
    }
    cell_info = nullptr;
    return false;
}

// Get map cell information (const version)
bool Environment::GetMapCellInfo(int x, int y, const MapCellInfo*& cell_info) const {
    if (x >= 0 && x < m_width && y >= 0 && y < m_height) {
        cell_info = &m_map_cell_array[x * m_height + y];
        return true;
    }
    cell_info = nullptr;
    return false;
}

// Load map data from file (matching original LoadMapData)
bool Environment::LoadMapData(const std::string& map_file) {
    TRY_BEGIN
        std::unique_lock<std::shared_mutex> lock(m_mutex);

        g_functions::MainOutMessage("Loading map data from: " + map_file);

        std::ifstream file(map_file, std::ios::binary);
        if (!file.is_open()) {
            g_functions::MainOutMessage("Failed to open map file: " + map_file);
            return false;
        }

        // Read map header
        MapHeader header;
        file.read(reinterpret_cast<char*>(&header.width), sizeof(header.width));
        file.read(reinterpret_cast<char*>(&header.height), sizeof(header.height));

        // Read title (16 bytes)
        char title_buffer[17] = {0};
        file.read(title_buffer, 16);
        header.title = std::string(title_buffer);

        file.read(reinterpret_cast<char*>(&header.update_date), sizeof(header.update_date));
        file.read(header.reserved.data(), header.reserved.size());

        // Initialize map with loaded dimensions
        Initialize(header.width, header.height);

        // Calculate map data size
        size_t map_size = m_width * m_height * sizeof(MapUnitInfo);
        std::vector<MapUnitInfo> map_buffer(m_width * m_height);

        // Read map data
        file.read(reinterpret_cast<char*>(map_buffer.data()), map_size);
        file.close();

        // Process map data and set cell flags
        for (int w = 0; w < m_width; ++w) {
            int base_index = w * m_height;
            for (int h = 0; h < m_height; ++h) {
                int index = base_index + h;
                const auto& map_unit = map_buffer[index];
                MapCellInfo* cell_info = nullptr;

                if (GetMapCellInfo(w, h, cell_info)) {
                    // Check for movement blocking flags
                    if ((map_unit.bk_img & 0x8000) != 0) {
                        cell_info->flag = 1;  // Blocked
                    }
                    if ((map_unit.fr_img & 0x8000) != 0) {
                        cell_info->flag = 2;  // Fly blocked
                    }

                    // Process door information
                    if ((map_unit.door_index & 0x80) != 0) {
                        int door_point = map_unit.door_index & 0x7F;
                        if (door_point > 0) {
                            auto door = std::make_shared<DoorInfo>();
                            door->x = w;
                            door->y = h;
                            door->index = door_point;

                            // Check for existing door status in nearby doors
                            door->status = nullptr;
                            for (const auto& existing_door : m_door_list) {
                                if (abs(existing_door->x - door->x) <= 10 &&
                                    abs(existing_door->y - door->y) <= 10 &&
                                    existing_door->index == door_point) {
                                    door->status = existing_door->status;
                                    door->status->ref_count++;
                                    break;
                                }
                            }

                            // Create new door status if not found
                            if (!door->status) {
                                door->status = std::make_shared<DoorInfo::DoorStatus>();
                                door->status->opened = false;
                                door->status->flag = false;
                                door->status->value = 0;
                                door->status->open_tick = 0;
                                door->status->ref_count = 1;
                            }

                            m_door_list.push_back(door);
                        }
                    }
                }
            }
        }

        g_functions::MainOutMessage("Map data loaded successfully: " + m_map_name +
                                  " (" + std::to_string(m_width) + "x" + std::to_string(m_height) + ")");
        return true;

    TRY_END

    return false;
}

// Cleanup map cells
void Environment::CleanupMapCells() {
    for (int x = 0; x < m_width; ++x) {
        for (int y = 0; y < m_height; ++y) {
            MapCellInfo* cell_info = nullptr;
            if (GetMapCellInfo(x, y, cell_info) && !cell_info->obj_list.empty()) {
                // Clean up objects in cell
                for (auto obj_ptr : cell_info->obj_list) {
                    if (obj_ptr) {
                        OSObject* os_obj = static_cast<OSObject*>(obj_ptr);
                        // Note: Object cleanup depends on object type
                        // This should be handled by the object management system
                        delete os_obj;
                    }
                }
                cell_info->obj_list.clear();
            }
        }
    }
    m_map_cell_array.clear();
}

// Set map flags
void Environment::SetMapFlags(const MapFlags& flags) {
    std::unique_lock<std::shared_mutex> lock(m_mutex);
    m_map_flags = flags;

    // Update restricted items list if needed
    if (m_map_flags.un_allow_std_items && !m_map_flags.un_allow_std_items_text.empty()) {
        UpdateUnAllowItemsList();
    }
}

// Update restricted items list from text
void Environment::UpdateUnAllowItemsList() {
    m_un_allow_std_items_list.clear();
    m_un_allow_std_items_idx_list.clear();

    if (m_map_flags.un_allow_std_items_text.empty()) {
        return;
    }

    // Parse the restricted items text (separated by | \ /)
    std::string text = m_map_flags.un_allow_std_items_text;
    std::string delimiters = "|\\/ ";
    size_t start = 0;
    size_t end = 0;

    while ((end = text.find_first_of(delimiters, start)) != std::string::npos) {
        if (end != start) {
            std::string item_name = text.substr(start, end - start);
            // Trim whitespace
            item_name.erase(0, item_name.find_first_not_of(" \t"));
            item_name.erase(item_name.find_last_not_of(" \t") + 1);

            if (!item_name.empty()) {
                m_un_allow_std_items_list.push_back(item_name);

                // Get item index from UserEngine if available
                // This would require integration with UserEngine
                // For now, we'll store -1 as placeholder
                m_un_allow_std_items_idx_list.push_back(-1);
            }
        }
        start = end + 1;
    }

    // Handle last item
    if (start < text.length()) {
        std::string item_name = text.substr(start);
        item_name.erase(0, item_name.find_first_not_of(" \t"));
        item_name.erase(item_name.find_last_not_of(" \t") + 1);

        if (!item_name.empty()) {
            m_un_allow_std_items_list.push_back(item_name);
            m_un_allow_std_items_idx_list.push_back(-1);
        }
    }
}

// Map zone checking methods (all zones apply to entire map)
bool Environment::IsSafeZone(const Point& pos) const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);
    return m_map_flags.is_safe;
}

bool Environment::IsFightZone(const Point& pos) const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);
    return m_map_flags.is_fight_zone;
}

bool Environment::IsFight3Zone(const Point& pos) const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);
    return m_map_flags.is_fight3_zone;
}

bool Environment::IsDarknessZone(const Point& pos) const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);
    return m_map_flags.is_dark;
}

bool Environment::IsDayLightZone(const Point& pos) const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);
    return m_map_flags.is_day;
}

bool Environment::IsQuizZone(const Point& pos) const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);
    return m_map_flags.is_quiz;
}

bool Environment::IsNoReconnectZone(const Point& pos) const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);
    return m_map_flags.no_reconnect;
}

bool Environment::IsNeedHoleZone(const Point& pos) const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);
    return m_map_flags.need_hole;
}

bool Environment::IsNoRecallZone(const Point& pos) const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);
    return m_map_flags.no_recall;
}

bool Environment::IsNoGuildRecallZone(const Point& pos) const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);
    return m_map_flags.no_guild_recall;
}

bool Environment::IsNoDearRecallZone(const Point& pos) const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);
    return m_map_flags.no_dear_recall;
}

bool Environment::IsNoMasterRecallZone(const Point& pos) const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);
    return m_map_flags.no_master_recall;
}

bool Environment::IsNoRandomMoveZone(const Point& pos) const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);
    return m_map_flags.no_random_move;
}

bool Environment::IsNoDrugZone(const Point& pos) const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);
    return m_map_flags.no_drug;
}

bool Environment::IsMineZone(const Point& pos) const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);
    return m_map_flags.is_mine;
}

bool Environment::IsNoPositionMoveZone(const Point& pos) const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);
    return m_map_flags.no_position_move;
}

bool Environment::IsRunHumanZone(const Point& pos) const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);
    return m_map_flags.run_human;
}

bool Environment::IsRunMonsterZone(const Point& pos) const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);
    return m_map_flags.run_monster;
}

bool Environment::IsNoFireMagicZone(const Point& pos) const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);
    return m_map_flags.no_fire_magic;
}

// Item restriction checking (matching original AllowStdItems)
bool Environment::AllowStdItems(const std::string& item_name) const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);

    if (!m_map_flags.un_allow_std_items || m_un_allow_std_items_list.empty()) {
        return true;
    }

    for (const auto& restricted_item : m_un_allow_std_items_list) {
        if (restricted_item == item_name) {
            return false;
        }
    }

    return true;
}

bool Environment::AllowStdItems(int item_idx) const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);

    if (!m_map_flags.un_allow_std_items || m_un_allow_std_items_idx_list.empty()) {
        return true;
    }

    for (int restricted_idx : m_un_allow_std_items_idx_list) {
        if (restricted_idx == item_idx) {
            return false;
        }
    }

    return true;
}

// Utility methods
bool Environment::IsInMapRange(int x, int y) const {
    return x >= 0 && x < m_width && y >= 0 && y < m_height;
}

bool Environment::IsInMapRange(const Point& pos) const {
    return IsInMapRange(pos.x, pos.y);
}

// Map walking checks (matching original CanWalk methods)
bool Environment::CanWalk(int x, int y, bool flag) const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);

    const MapCellInfo* cell_info = nullptr;
    if (!GetMapCellInfo(x, y, cell_info) || cell_info->flag != 0) {
        return false;
    }

    if (!flag && !cell_info->obj_list.empty()) {
        // Check for blocking objects
        for (const auto& obj_ptr : cell_info->obj_list) {
            if (obj_ptr) {
                OSObject* os_obj = static_cast<OSObject*>(obj_ptr);
                if (os_obj->type == ObjectType::OS_MOVINGOBJECT) {
                    BaseObject* base_obj = static_cast<BaseObject*>(os_obj->cell_obj);
                    if (base_obj && !base_obj->IsGhost() && base_obj->IsVisible() &&
                        !base_obj->IsDeath() && !base_obj->IsObserveMode()) {
                        return false;
                    }
                }
            }
        }
    }

    return true;
}

bool Environment::CanWalkOfItem(int x, int y, bool flag, bool item) const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);

    const MapCellInfo* cell_info = nullptr;
    if (!GetMapCellInfo(x, y, cell_info) || cell_info->flag != 0) {
        return false;
    }

    if (!cell_info->obj_list.empty()) {
        for (const auto& obj_ptr : cell_info->obj_list) {
            if (obj_ptr) {
                OSObject* os_obj = static_cast<OSObject*>(obj_ptr);

                // Check for blocking moving objects
                if (!flag && os_obj->type == ObjectType::OS_MOVINGOBJECT) {
                    BaseObject* base_obj = static_cast<BaseObject*>(os_obj->cell_obj);
                    if (base_obj && !base_obj->IsGhost() && base_obj->IsVisible() &&
                        !base_obj->IsDeath() && !base_obj->IsObserveMode()) {
                        return false;
                    }
                }

                // Check for items if item flag is false
                if (!item && os_obj->type == ObjectType::OS_ITEMOBJECT) {
                    return false;
                }
            }
        }
    }

    return true;
}

bool Environment::CanWalkEx(int x, int y, bool flag) const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);

    const MapCellInfo* cell_info = nullptr;
    if (!GetMapCellInfo(x, y, cell_info) || cell_info->flag != 0) {
        return false;
    }

    if (!flag && !cell_info->obj_list.empty()) {
        for (const auto& obj_ptr : cell_info->obj_list) {
            if (obj_ptr) {
                OSObject* os_obj = static_cast<OSObject*>(obj_ptr);
                if (os_obj->type == ObjectType::OS_MOVINGOBJECT) {
                    BaseObject* base_obj = static_cast<BaseObject*>(os_obj->cell_obj);
                    if (base_obj && !base_obj->IsGhost() && base_obj->IsVisible() &&
                        !base_obj->IsDeath() && !base_obj->IsObserveMode()) {

                        // Check run permissions based on object type
                        if (base_obj->IsPlayer()) {
                            if (!m_map_flags.run_human) {
                                return false;
                            }
                        } else if (base_obj->IsNPC()) {
                            // NPCs can usually be run through
                            continue;
                        } else if (base_obj->IsGuard()) {
                            // Guards can usually be run through
                            continue;
                        } else {
                            // Monsters
                            if (!m_map_flags.run_monster) {
                                return false;
                            }
                        }
                    }
                }
            }
        }
    }

    return true;
}

// Object count management
void Environment::AddObjectCount(BaseObject* obj) {
    if (!obj) return;

    std::unique_lock<std::shared_mutex> lock(m_mutex);

    if (obj->IsPlayer()) {
        m_human_count++;
    } else if (obj->IsMonster()) {
        m_monster_count++;
    }
}

void Environment::DelObjectCount(BaseObject* obj) {
    if (!obj) return;

    std::unique_lock<std::shared_mutex> lock(m_mutex);

    if (obj->IsPlayer()) {
        if (m_human_count > 0) {
            m_human_count--;
        }
    } else if (obj->IsMonster()) {
        if (m_monster_count > 0) {
            m_monster_count--;
        }
    }
}

// Get environment information (matching original GetEnvirInfo)
std::string Environment::GetEnvironmentInfo() const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);

    std::string info = "Map: " + m_map_name + "(" + m_map_desc + ")";
    info += " Size: " + std::to_string(m_width) + "x" + std::to_string(m_height);
    info += " Players: " + std::to_string(m_human_count);
    info += " Monsters: " + std::to_string(m_monster_count);

    // Add flag information
    if (m_map_flags.is_safe) info += " [SAFE]";
    if (m_map_flags.is_fight_zone) info += " [FIGHT]";
    if (m_map_flags.is_fight3_zone) info += " [GUILD_WAR]";
    if (m_map_flags.is_dark) info += " [DARK]";
    if (m_map_flags.is_day) info += " [DAY]";
    if (m_map_flags.is_quiz) info += " [QUIZ]";
    if (m_map_flags.no_reconnect) info += " [NO_RECONNECT]";
    if (m_map_flags.need_hole) info += " [NEED_HOLE]";
    if (m_map_flags.no_recall) info += " [NO_RECALL]";
    if (m_map_flags.no_guild_recall) info += " [NO_GUILD_RECALL]";
    if (m_map_flags.no_dear_recall) info += " [NO_DEAR_RECALL]";
    if (m_map_flags.no_master_recall) info += " [NO_MASTER_RECALL]";
    if (m_map_flags.no_random_move) info += " [NO_RANDOM]";
    if (m_map_flags.no_drug) info += " [NO_DRUG]";
    if (m_map_flags.is_mine) info += " [MINE]";
    if (m_map_flags.no_position_move) info += " [NO_POSITION_MOVE]";
    if (m_map_flags.run_human) info += " [RUN_HUMAN]";
    if (m_map_flags.run_monster) info += " [RUN_MONSTER]";
    if (m_map_flags.no_fire_magic) info += " [NO_FIRE_MAGIC]";
    if (m_map_flags.un_allow_std_items) info += " [RESTRICT_ITEMS]";

    // Add numeric effects
    if (m_map_flags.has_music) {
        info += " Music:" + std::to_string(m_map_flags.music_id);
    }
    if (m_map_flags.exp_rate) {
        info += " ExpRate:" + std::to_string(m_map_flags.exp_rate_value) + "%";
    }
    if (m_map_flags.pk_win_level) {
        info += " PKWinLevel:" + std::to_string(m_map_flags.pk_win_level_value);
    }
    if (m_map_flags.pk_lost_level) {
        info += " PKLostLevel:" + std::to_string(m_map_flags.pk_lost_level_value);
    }
    if (m_map_flags.inc_hp) {
        info += " IncHP:" + std::to_string(m_map_flags.inc_hp_point) +
                "/" + std::to_string(m_map_flags.inc_hp_time) + "ms";
    }
    if (m_map_flags.dec_hp) {
        info += " DecHP:" + std::to_string(m_map_flags.dec_hp_point) +
                "/" + std::to_string(m_map_flags.dec_hp_time) + "ms";
    }
    if (m_map_flags.inc_game_gold) {
        info += " IncGold:" + std::to_string(m_map_flags.inc_game_gold_value) +
                "/" + std::to_string(m_map_flags.inc_game_gold_time) + "ms";
    }
    if (m_map_flags.dec_game_gold) {
        info += " DecGold:" + std::to_string(m_map_flags.dec_game_gold_value) +
                "/" + std::to_string(m_map_flags.dec_game_gold_time) + "ms";
    }
    if (m_map_flags.inc_game_point) {
        info += " IncPoint:" + std::to_string(m_map_flags.inc_game_point_value) +
                "/" + std::to_string(m_map_flags.inc_game_point_time) + "ms";
    }
    if (m_map_flags.dec_game_point) {
        info += " DecPoint:" + std::to_string(m_map_flags.dec_game_point_value) +
                "/" + std::to_string(m_map_flags.dec_game_point_time) + "ms";
    }

    return info;
}

// Process map events (main processing method)
void Environment::ProcessMapEvents() {
    if (!m_active || !m_initialized) return;

    // Process various map effects
    ProcessMapEffects();
    ProcessAutoHP();
    ProcessAutoGameGold();
    ProcessAutoGamePoint();
    ProcessPKEffects();
}

// Process map effects (lighting, music, etc.)
void Environment::ProcessMapEffects() {
    // Map effects are typically handled by client-side rendering
    // Server mainly tracks the state for new players entering the map
}

// Process automatic HP changes
void Environment::ProcessAutoHP() {
    // This would typically iterate through all players in the map
    // and apply HP changes based on timing intervals
    // Implementation depends on player management system
}

// Process automatic game gold changes
void Environment::ProcessAutoGameGold() {
    // Similar to ProcessAutoHP but for game gold
}

// Process automatic game point changes
void Environment::ProcessAutoGamePoint() {
    // Similar to ProcessAutoHP but for game points
}

// Process PK effects
void Environment::ProcessPKEffects() {
    // PK effects are typically handled during combat resolution
    // This method can be used for periodic PK-related processing
}
