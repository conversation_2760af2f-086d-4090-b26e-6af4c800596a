#!/bin/bash

echo "========================================"
echo "Building Manager Architecture Phase 1"
echo "========================================"

# 设置编译目录
BUILD_DIR="build_manager_phase1"
SOURCE_DIR="$(pwd)"

# 清理旧的编译目录
if [ -d "$BUILD_DIR" ]; then
    echo "Cleaning old build directory..."
    rm -rf "$BUILD_DIR"
fi

# 创建编译目录
mkdir "$BUILD_DIR"
cd "$BUILD_DIR"

echo ""
echo "Configuring CMake..."

# 创建临时CMakeLists.txt
cat > CMakeLists.txt << 'EOF'
cmake_minimum_required(VERSION 3.16)
project(Mir200_Manager_Phase1)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置编译选项
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -std=c++17")
set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -g -O0")
set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O3")

# 包含目录
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/..)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../Common)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../Managers)

# Manager架构源文件
set(MANAGER_SOURCES
    ../Managers/GameConfigManager.cpp
    ../Managers/LogManager.cpp
    ../Managers/DatabaseManager.cpp
    ../Managers/EventBus.cpp
    ../Managers/ServiceContainer.cpp
    ../Common/M2Share.cpp
)

# 创建第一阶段测试程序
add_executable(test_manager_phase1 
    ../test_manager_phase1.cpp
    ${MANAGER_SOURCES}
)

# 链接线程库
find_package(Threads REQUIRED)
target_link_libraries(test_manager_phase1 Threads::Threads)

# 设置输出目录
set_target_properties(test_manager_phase1 PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
)

# 创建配置和数据目录
file(MAKE_DIRECTORY "${CMAKE_BINARY_DIR}/bin/config")
file(MAKE_DIRECTORY "${CMAKE_BINARY_DIR}/bin/data")
file(MAKE_DIRECTORY "${CMAKE_BINARY_DIR}/bin/logs")
EOF

# 配置CMake
cmake -DCMAKE_BUILD_TYPE=Release .

if [ $? -ne 0 ]; then
    echo "CMake configuration failed!"
    exit 1
fi

echo ""
echo "Building project..."
make -j$(nproc)

if [ $? -ne 0 ]; then
    echo "Build failed!"
    exit 1
fi

echo ""
echo "========================================"
echo "Build completed successfully!"
echo "========================================"

echo ""
echo "Running Manager Phase 1 test..."
cd bin
if [ -f "test_manager_phase1" ]; then
    ./test_manager_phase1
else
    echo "Test executable not found!"
    exit 1
fi

echo ""
echo "========================================"
echo "Manager Phase 1 test completed!"
echo "========================================"
