#pragma once

#include "IManager.h"
#include "EventBus.h"
#include <memory>
#include <unordered_map>
#include <typeindex>
#include <vector>
#include <string>

/**
 * @brief 服务容器
 * 负责管理所有Manager的生命周期和依赖注入
 */
class ServiceContainer {
private:
    std::unordered_map<std::string, std::shared_ptr<IManager>> m_managersByName;
    std::unordered_map<std::type_index, std::shared_ptr<IManager>> m_managersByType;
    std::unique_ptr<EventBus> m_eventBus;
    bool m_initialized;

public:
    ServiceContainer();
    ~ServiceContainer();
    
    // 生命周期管理
    bool Initialize();
    void Finalize();
    bool IsInitialized() const { return m_initialized; }
    
    // Manager注册
    template<typename T>
    void RegisterManager(std::shared_ptr<T> manager) {
        static_assert(std::is_base_of_v<IManager, T>, "T must inherit from IManager");
        
        // 按名称注册
        m_managersByName[manager->GetManagerName()] = manager;
        
        // 按类型注册
        m_managersByType[std::type_index(typeid(T))] = manager;
        
        std::cout << "[ServiceContainer] Manager registered: " << manager->GetManagerName() << std::endl;
    }
    
    // 按类型获取Manager
    template<typename T>
    T* GetManager() {
        auto it = m_managersByType.find(std::type_index(typeid(T)));
        if (it != m_managersByType.end()) {
            return std::static_pointer_cast<T>(it->second).get();
        }
        return nullptr;
    }
    
    // 按名称获取Manager
    IManager* GetManager(const std::string& name);
    
    // 获取服务接口
    template<typename Interface>
    Interface* GetService() {
        for (const auto& [name, manager] : m_managersByName) {
            if (auto service = std::dynamic_pointer_cast<Interface>(manager)) {
                return service.get();
            }
        }
        return nullptr;
    }
    
    // 获取事件总线
    EventBus* GetEventBus() { return m_eventBus.get(); }
    
    // 获取所有Manager
    std::vector<std::string> GetManagerNames() const;
    
    // 更新所有Manager
    void UpdateAllManagers();

private:
    void SetupManagerDependencies();
    void SetupEventSubscriptions();
    bool InitializeAllManagers();
    void FinalizeAllManagers();
    void SetupManagerEventSubscription(IEventSubscriber* subscriber, const std::string& managerName);
};
