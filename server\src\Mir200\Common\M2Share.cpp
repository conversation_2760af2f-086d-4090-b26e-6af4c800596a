#ifdef _WIN32
#include <windows.h>
#endif
#include "M2Share.h"
#include <iostream>
#include <ctime>
#include <random>

// Global configuration variables
namespace g_config {
    bool server_ready = false;
    bool show_exception = true;
    bool show_pre_fix = true;
    std::string server_name = "Mir200 Server";
    std::string gate_addr = "127.0.0.1";
    int gate_port = 7000;
    int max_user = 1000;
    bool test_server = false;
    bool service_mode = false;
    std::string version_date = "20140909";
    DWORD server_file_crc = 0;

    // Message processing configuration (following original project)
    bool bo_spell_send_update_msg = true;
    bool bo_action_send_action_msg = true;
}

// Global objects
namespace g_objects {
    std::shared_ptr<UserEngine> user_engine = nullptr;
    std::shared_ptr<LocalDatabase> local_database = nullptr;
    std::vector<std::shared_ptr<Environment>> env_list;
    std::unordered_map<std::string, std::shared_ptr<Guild>> guild_list;
    std::shared_ptr<Castle> castle = nullptr;
}

// Random number generator
static std::random_device rd;
static std::mt19937 gen(rd());

// Global functions implementation
namespace g_functions {
    
    DWORD GetCurrentTime() {
        return static_cast<DWORD>(GetTickCount());
    }
    
    std::string GetCurrentTimeStr() {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        std::stringstream ss;
        ss << std::put_time(std::localtime(&time_t), "%H:%M:%S");
        return ss.str();
    }
    
    std::string GetDateTimeStr() {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        std::stringstream ss;
        ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
        return ss.str();
    }
    
    int Random(int max) {
        if (max <= 0) return 0;
        std::uniform_int_distribution<> dis(0, max - 1);
        return dis(gen);
    }
    
    int Random(int min, int max) {
        if (min >= max) return min;
        std::uniform_int_distribution<> dis(min, max);
        return dis(gen);
    }
    
    bool RandomBool() {
        return Random(2) == 1;
    }
    
    std::string GetValidStr3(const std::string& str, std::string& dest, const std::string& div) {
        size_t pos = str.find(div);
        if (pos != std::string::npos) {
            dest = str.substr(0, pos);
            return str.substr(pos + div.length());
        }
        dest = str;
        return "";
    }
    
    std::string GetValidStrCap(const std::string& str, std::string& dest, const std::string& div) {
        std::string result = GetValidStr3(str, dest, div);
        std::transform(dest.begin(), dest.end(), dest.begin(), ::toupper);
        return result;
    }
    
    std::string ArrestStringEx(const std::string& str, const std::string& search_str, 
                              const std::string& end_str, std::string& result) {
        size_t start_pos = str.find(search_str);
        if (start_pos == std::string::npos) {
            result = "";
            return str;
        }
        
        start_pos += search_str.length();
        size_t end_pos = str.find(end_str, start_pos);
        if (end_pos == std::string::npos) {
            result = str.substr(start_pos);
            return "";
        }
        
        result = str.substr(start_pos, end_pos - start_pos);
        return str.substr(end_pos + end_str.length());
    }
    
    bool FileExists(const std::string& filename) {
        std::ifstream file(filename);
        return file.good();
    }
    
    bool DirectoryExists(const std::string& dirname) {
        DWORD attrs = GetFileAttributesA(dirname.c_str());
        return (attrs != INVALID_FILE_ATTRIBUTES) && (attrs & FILE_ATTRIBUTE_DIRECTORY);
    }
    
    std::string ExtractFilePath(const std::string& filename) {
        size_t pos = filename.find_last_of("\\/");
        if (pos != std::string::npos) {
            return filename.substr(0, pos + 1);
        }
        return "";
    }
    
    std::string ExtractFileName(const std::string& filename) {
        size_t pos = filename.find_last_of("\\/");
        if (pos != std::string::npos) {
            return filename.substr(pos + 1);
        }
        return filename;
    }
    
    std::string ChangeFileExt(const std::string& filename, const std::string& ext) {
        size_t pos = filename.find_last_of('.');
        if (pos != std::string::npos) {
            return filename.substr(0, pos) + ext;
        }
        return filename + ext;
    }
    
    DWORD CalcFileCRC(const std::string& filename) {
        std::ifstream file(filename, std::ios::binary);
        if (!file) return 0;
        
        DWORD crc = 0;
        char buffer[4096];
        while (file.read(buffer, sizeof(buffer)) || file.gcount() > 0) {
            for (std::streamsize i = 0; i < file.gcount(); ++i) {
                crc = (crc << 1) ^ static_cast<BYTE>(buffer[i]);
            }
        }
        return crc;
    }
    
    int GetDistance(int x1, int y1, int x2, int y2) {
        int dx = abs(x1 - x2);
        int dy = abs(y1 - y2);
        return (dx > dy) ? dx : dy;
    }
    
    BYTE GetDirection(int x1, int y1, int x2, int y2) {
        int dx = x2 - x1;
        int dy = y2 - y1;
        
        if (dx == 0 && dy == 0) return DR_UP;
        
        if (abs(dx) > abs(dy)) {
            return (dx > 0) ? DR_RIGHT : DR_LEFT;
        } else {
            return (dy > 0) ? DR_DOWN : DR_UP;
        }
    }
    
    Point GetFrontPosition(int x, int y, BYTE dir) {
        Point result(x, y);
        switch (dir) {
            case DR_UP: result.y--; break;
            case DR_UPRIGHT: result.x++; result.y--; break;
            case DR_RIGHT: result.x++; break;
            case DR_DOWNRIGHT: result.x++; result.y++; break;
            case DR_DOWN: result.y++; break;
            case DR_DOWNLEFT: result.x--; result.y++; break;
            case DR_LEFT: result.x--; break;
            case DR_UPLEFT: result.x--; result.y--; break;
        }
        return result;
    }
    
    Point GetBackPosition(int x, int y, BYTE dir) {
        BYTE back_dir = (dir + 4) % 8;
        return GetFrontPosition(x, y, back_dir);
    }
    
    DWORD GetLevelExp(BYTE level) {
        if (level <= 1) return 0;
        if (level >= 65) return 0xFFFFFFFF;
        
        // Original formula from Delphi version
        DWORD base_exp = 100;
        for (BYTE i = 2; i <= level; ++i) {
            base_exp = static_cast<DWORD>(base_exp * 1.2);
        }
        return base_exp;
    }
    
    BYTE GetLevelByExp(DWORD exp) {
        for (BYTE level = 1; level < 65; ++level) {
            if (GetLevelExp(level + 1) > exp) {
                return level;
            }
        }
        return 65;
    }
    
    DWORD GetUpgradeExp(BYTE level) {
        return GetLevelExp(level + 1) - GetLevelExp(level);
    }
    
    bool IsAccessory(int std_mode) {
        return (std_mode >= 19 && std_mode <= 24) || std_mode == 26;
    }
    
    bool IsWeapon(int std_mode) {
        return std_mode >= 5 && std_mode <= 18;
    }
    
    bool IsArmor(int std_mode) {
        return std_mode >= 10 && std_mode <= 14;
    }
    
    std::string GetItemGradeStr(const TUserItem& item) {
        // Simplified implementation - can be expanded based on item properties
        (void)item; // Suppress unused parameter warning
        return "Normal";
    }
    
    bool IsWarriorSkill(WORD magic_id) {
        return (magic_id >= SKILL_ONESWORD && magic_id <= SKILL_MOOTEBO) ||
               (magic_id >= SKILL_40 && magic_id <= SKILL_49);
    }
    
    bool IsWizardSkill(WORD magic_id) {
        return (magic_id >= SKILL_FIREBALL && magic_id <= SKILL_LIGHTFLOWER) ||
               (magic_id >= SKILL_50 && magic_id <= SKILL_59);
    }
    
    bool IsTaoistSkill(WORD magic_id) {
        return (magic_id >= SKILL_SHOWHP && magic_id <= SKILL_GROUPDEDING) ||
               (magic_id >= SKILL_60 && magic_id <= SKILL_72);
    }
    
    std::string GetMagicName(WORD magic_id) {
        // This would be loaded from database in real implementation
        switch (magic_id) {
            case SKILL_FIREBALL: return "FireBall";
            case SKILL_HEALLING: return "Healing";
            case SKILL_ONESWORD: return "BasicSword";
            case SKILL_ERGUM: return "Thrust";
            case SKILL_BANWOL: return "HalfMoon";
            case SKILL_FIRESWORD: return "FireSword";
            default: return "Unknown";
        }
    }
    
    std::string GetGoldStr(int gold) {
        std::stringstream ss;
        ss << gold << " Gold";
        return ss.str();
    }
    
    std::string GetUserName(const std::string& account, const std::string& char_name) {
        return char_name + "(" + account + ")";
    }
    
    std::string EncodeMessage(const DefaultMessage& msg) {
        // Simple encoding - in real implementation this would be more complex
        std::stringstream ss;
        ss << msg.Ident << "," << msg.Recog << "," << msg.Param << ","
           << msg.tag << "," << msg.Series;
        return ss.str();
    }
    
    DefaultMessage DecodeMessage(const std::string& data) {
        DefaultMessage msg;
        std::stringstream ss(data);
        std::string item;

        if (std::getline(ss, item, ',')) msg.Ident = static_cast<WORD>(std::stoi(item));
        if (std::getline(ss, item, ',')) msg.Recog = static_cast<int>(std::stoi(item));
        if (std::getline(ss, item, ',')) msg.Param = static_cast<WORD>(std::stoi(item));
        if (std::getline(ss, item, ',')) msg.tag = static_cast<WORD>(std::stoi(item));
        if (std::getline(ss, item, ',')) msg.Series = static_cast<WORD>(std::stoi(item));

        return msg;
    }
    
    std::string EncodeString(const std::string& str) {
        // Simple encoding - can be enhanced for security
        return str;
    }
    
    std::string DecodeString(const std::string& data) {
        // Simple decoding - can be enhanced for security
        return data;
    }
    
    void MainOutMessage(const std::string& msg) {
        std::string time_str = GetCurrentTimeStr();
        std::cout << "[" << time_str << "] " << msg << std::endl;
        
        // Also write to log file if needed
        static std::ofstream log_file("logs/server.log", std::ios::app);
        if (log_file.is_open()) {
            log_file << "[" << time_str << "] " << msg << std::endl;
            log_file.flush();
        }
    }
    
    void MainOutMessageFmt(const char* format, ...) {
        char buffer[1024];
        va_list args;
        va_start(args, format);
        vsnprintf(buffer, sizeof(buffer), format, args);
        va_end(args);
        MainOutMessage(std::string(buffer));
    }
}

bool InitializeM2Share() {
    TRY_BEGIN
        // Initialize random seed
        srand(static_cast<unsigned int>(time(nullptr)));
        
        // Create log directory if it doesn't exist
        if (!g_functions::DirectoryExists("logs")) {
            CreateDirectoryA("logs", nullptr);
        }
        
        g_functions::MainOutMessage("M2Share initialized successfully");
        return true;
    TRY_END
    
    return false;
}

void FinalizeM2Share() {
    TRY_BEGIN
        // Clear global objects
        g_objects::user_engine.reset();
        g_objects::local_database.reset();
        g_objects::env_list.clear();
        g_objects::guild_list.clear();
        g_objects::castle.reset();
        
        g_functions::MainOutMessage("M2Share finalized");
    TRY_END
}

bool LoadServerConfig(const std::string& config_file) {
    TRY_BEGIN
        // Simple INI-style configuration loading
        std::ifstream file(config_file);
        if (!file.is_open()) {
            g_functions::MainOutMessage("Warning: Config file not found, using defaults");
            return true; // Use defaults
        }
        
        std::string line;
        while (std::getline(file, line)) {
            if (line.empty() || line[0] == ';' || line[0] == '#') continue;
            
            size_t pos = line.find('=');
            if (pos == std::string::npos) continue;
            
            std::string key = line.substr(0, pos);
            std::string value = line.substr(pos + 1);
            
            // Trim whitespace
            key.erase(0, key.find_first_not_of(" \t"));
            key.erase(key.find_last_not_of(" \t") + 1);
            value.erase(0, value.find_first_not_of(" \t"));
            value.erase(value.find_last_not_of(" \t") + 1);
            
            // Parse configuration values
            if (key == "ServerName") g_config::server_name = value;
            else if (key == "GateAddr") g_config::gate_addr = value;
            else if (key == "GatePort") g_config::gate_port = std::stoi(value);
            else if (key == "MaxUser") g_config::max_user = std::stoi(value);
            else if (key == "TestServer") g_config::test_server = (value == "1" || value == "true");
            else if (key == "ServiceMode") g_config::service_mode = (value == "1" || value == "true");
            else if (key == "ShowException") g_config::show_exception = (value == "1" || value == "true");
        }
        
        g_functions::MainOutMessage("Server configuration loaded from " + config_file);
        return true;
    TRY_END
    
    return false;
}

bool SaveServerConfig(const std::string& config_file) {
    TRY_BEGIN
        std::ofstream file(config_file);
        if (!file.is_open()) {
            g_functions::MainOutMessage("Error: Cannot save config file " + config_file);
            return false;
        }
        
        file << "; Mir200 Server Configuration\n";
        file << "; Generated on " << g_functions::GetDateTimeStr() << "\n\n";
        
        file << "ServerName=" << g_config::server_name << "\n";
        file << "GateAddr=" << g_config::gate_addr << "\n";
        file << "GatePort=" << g_config::gate_port << "\n";
        file << "MaxUser=" << g_config::max_user << "\n";
        file << "TestServer=" << (g_config::test_server ? "1" : "0") << "\n";
        file << "ServiceMode=" << (g_config::service_mode ? "1" : "0") << "\n";
        file << "ShowException=" << (g_config::show_exception ? "1" : "0") << "\n";
        
        g_functions::MainOutMessage("Server configuration saved to " + config_file);
        return true;
    TRY_END
    
    return false;
}
