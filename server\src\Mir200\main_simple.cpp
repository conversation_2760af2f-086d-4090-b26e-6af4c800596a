#include "M2Server.h"
#include "Common/SimpleTypes.h"
#include <iostream>
#include <string>
#include <vector>
#include <csignal>

// Global server instance
std::unique_ptr<M2Server> g_server;

void SignalHandler(int signal) {
    std::cout << "Received signal " << signal << ", shutting down..." << std::endl;
    
    if (g_server) {
        g_server->Stop();
    }
    
    exit(0);
}

#ifdef _WIN32
BOOL WINAPI ConsoleCtrlHandler(DWORD ctrl_type) {
    switch (ctrl_type) {
        case CTRL_C_EVENT:
        case CTRL_BREAK_EVENT:
        case CTRL_CLOSE_EVENT:
        case CTRL_LOGOFF_EVENT:
        case CTRL_SHUTDOWN_EVENT:
            std::cout << "Console control event received, shutting down..." << std::endl;
            if (g_server) {
                g_server->Stop();
            }
            return TRUE;
        default:
            return FALSE;
    }
}
#endif

void PrintBanner() {
    std::cout << "========================================" << std::endl;
    std::cout << "  Mir200 Server - C++ Refactored" << std::endl;
    std::cout << "  Version: 1.0.0" << std::endl;
    std::cout << "  Program: Mir200 Server" << std::endl;
    std::cout << "  Website: https://github.com/mir-legend" << std::endl;
    std::cout << "========================================" << std::endl;
}

// Simple command line argument structure
struct CommandLineArgs {
    std::string config_file = "config/M2Server.ini";
    bool show_help = false;
    bool show_version = false;
    bool test_mode = false;
    bool daemon_mode = false;
    bool no_console = false;
};

CommandLineArgs ParseCommandLine(int argc, char* argv[]) {
    CommandLineArgs args;
    
    for (int i = 1; i < argc; ++i) {
        std::string arg = argv[i];
        
        if (arg == "-h" || arg == "--help") {
            args.show_help = true;
        } else if (arg == "-v" || arg == "--version") {
            args.show_version = true;
        } else if (arg == "-t" || arg == "--test") {
            args.test_mode = true;
        } else if (arg == "-d" || arg == "--daemon") {
            args.daemon_mode = true;
        } else if (arg == "--no-console") {
            args.no_console = true;
        } else if (arg == "-c" || arg == "--config") {
            if (i + 1 < argc) {
                args.config_file = argv[++i];
            }
        }
    }
    
    return args;
}

void PrintHelp() {
    std::cout << "Usage: Mir200 [options]" << std::endl;
    std::cout << "Options:" << std::endl;
    std::cout << "  -h, --help        Show this help message" << std::endl;
    std::cout << "  -v, --version     Show version information" << std::endl;
    std::cout << "  -c, --config FILE Use specified configuration file" << std::endl;
    std::cout << "  -t, --test        Test mode (validate config and exit)" << std::endl;
    std::cout << "  -d, --daemon      Run as daemon (background)" << std::endl;
    std::cout << "  --no-console      Disable console output" << std::endl;
}

int main(int argc, char* argv[]) {
    try {
        // Parse command line arguments
        CommandLineArgs args = ParseCommandLine(argc, argv);
        
        // Handle help and version
        if (args.show_help) {
            PrintHelp();
            return 0;
        }
        
        if (args.show_version) {
            std::cout << "Mir200 Server Version 1.0.0" << std::endl;
            std::cout << "Build Date: " << __DATE__ << " " << __TIME__ << std::endl;
            return 0;
        }
        
        // Print banner
        if (!args.no_console) {
            PrintBanner();
        }
        
        // Setup signal handlers
        signal(SIGINT, SignalHandler);
        signal(SIGTERM, SignalHandler);
        
#ifdef _WIN32
        SetConsoleCtrlHandler(ConsoleCtrlHandler, TRUE);
#endif
        
        // Create server instance
        g_server = std::make_unique<M2Server>();
        
        if (!g_server->Initialize(args.config_file)) {
            std::cout << "Failed to initialize server" << std::endl;
            return 1;
        }
        
        // Test mode - just validate configuration and exit
        if (args.test_mode) {
            std::cout << "Test mode: Configuration validated successfully" << std::endl;
            g_server->Finalize();
            return 0;
        }
        
        // Start the server
        std::cout << "Starting Mir200 Server..." << std::endl;
        
        if (!g_server->Start()) {
            std::cout << "Failed to start server" << std::endl;
            return 1;
        }
        
        std::cout << "Server started successfully" << std::endl;
        std::cout << "Server Name: Mir200 Test Server" << std::endl;
        std::cout << "Gate Address: 127.0.0.1:7000" << std::endl;
        std::cout << "Max Users: 1000" << std::endl;
        std::cout << "Test Server: Yes" << std::endl;
        
        if (!args.daemon_mode && !args.no_console) {
            std::cout << "Press Ctrl+C to stop the server" << std::endl;
        }
        
        // Run the server
        g_server->Run();
        
        // Cleanup
        std::cout << "Shutting down server..." << std::endl;
        g_server->Stop();
        g_server->Finalize();
        g_server.reset();
        
        std::cout << "Server shutdown complete" << std::endl;
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "Fatal error: " << e.what() << std::endl;
        
        if (g_server) {
            g_server->EmergencyStop();
            g_server.reset();
        }
        
        return 1;
    } catch (...) {
        std::cerr << "Unknown fatal error occurred" << std::endl;
        
        if (g_server) {
            g_server->EmergencyStop();
            g_server.reset();
        }
        
        return 1;
    }
}
