#pragma once

#include "IManager.h"
#include "EventData.h"
#include "../Common/Types.h"
#include <string>
#include <vector>
#include <unordered_map>
#include <mutex>
#include <shared_mutex>
#include <memory>

// 前向声明
class EventBus;
class PlayObject;

/**
 * @brief 物品掉落配置
 */
struct ItemDropConfig {
    int itemIndex;          // 物品索引
    int dropRate;           // 掉落概率 (1-10000)
    int minCount;           // 最小数量
    int maxCount;           // 最大数量
    int minLevel;           // 最小等级要求
    int maxLevel;           // 最大等级要求
    
    ItemDropConfig() : itemIndex(0), dropRate(0), minCount(1), maxCount(1), minLevel(0), maxLevel(999) {}
};

/**
 * @brief 物品强化配置
 */
struct ItemUpgradeConfig {
    int itemType;           // 物品类型
    int maxUpgradeLevel;    // 最大强化等级
    int successRate;        // 成功率
    int materialIndex;      // 强化材料索引
    int materialCount;      // 材料数量
    int goldCost;           // 金币消耗
    
    ItemUpgradeConfig() : itemType(0), maxUpgradeLevel(10), successRate(5000), 
                         materialIndex(0), materialCount(1), goldCost(1000) {}
};

/**
 * @brief 物品管理器
 * 负责管理所有物品相关功能
 * 对应原项目的物品系统功能
 */
class ItemManager : public IManager, public IItemProvider, public IEventSubscriber {
private:
    std::string m_managerName;
    bool m_initialized;
    
    // 依赖注入
    IPlayerProvider* m_playerProvider;
    EventBus* m_eventBus;
    
    // 物品掉落配置
    std::unordered_map<std::string, std::vector<ItemDropConfig>> m_monsterDrops;
    std::unordered_map<int, ItemUpgradeConfig> m_upgradeConfigs;
    
    // 线程安全
    mutable std::shared_mutex m_dropMutex;
    mutable std::shared_mutex m_upgradeMutex;
    
    // 统计信息
    std::atomic<uint64_t> m_totalItemsCreated;
    std::atomic<uint64_t> m_totalDropsGenerated;
    std::atomic<uint64_t> m_totalUpgradesAttempted;

public:
    ItemManager();
    virtual ~ItemManager();
    
    // IManager接口实现
    bool Initialize() override;
    void Finalize() override;
    void Update() override;
    const std::string& GetManagerName() const override;
    
    // 依赖注入
    void SetPlayerProvider(IPlayerProvider* provider);
    void SetEventBus(EventBus* eventBus);
    
    // IItemProvider接口实现
    const StdItem* GetStdItem(int itemIndex) const override;
    TUserItem CreateItem(int itemIndex) const override;
    bool ValidateItem(const TUserItem& item) const override;
    std::vector<TUserItem> GenerateMonsterDrops(const std::string& monsterName, int level) override;
    
    // IEventSubscriber接口实现
    void OnEvent(const std::string& eventType, const EventData& data) override;
    
    // 物品管理功能
    bool LoadItemDatabase();
    bool LoadDropConfigs(const std::string& configFile);
    bool LoadUpgradeConfigs(const std::string& configFile);
    
    // 物品创建和验证
    TUserItem CreateRandomItem(int itemType, int level) const;
    TUserItem CreateItemWithProperties(int itemIndex, int dura, int duraMax) const;
    bool IsItemExpired(const TUserItem& item) const;
    bool IsItemStackable(const TUserItem& item) const;
    
    // 物品强化系统
    bool UpgradeItem(PlayObject* player, TUserItem& item, const TUserItem& material);
    bool CanUpgradeItem(const TUserItem& item) const;
    int GetUpgradeSuccessRate(const TUserItem& item) const;
    
    // 物品掉落系统
    void AddMonsterDrop(const std::string& monsterName, const ItemDropConfig& config);
    void RemoveMonsterDrop(const std::string& monsterName, int itemIndex);
    std::vector<TUserItem> GenerateDropsForMonster(const std::string& monsterName, int level, int playerLevel) const;
    
    // 物品修理系统
    bool RepairItem(PlayObject* player, TUserItem& item, int repairType);
    int GetRepairCost(const TUserItem& item, int repairType) const;
    bool CanRepairItem(const TUserItem& item) const;
    
    // 统计信息
    uint64_t GetTotalItemsCreated() const { return m_totalItemsCreated; }
    uint64_t GetTotalDropsGenerated() const { return m_totalDropsGenerated; }
    uint64_t GetTotalUpgradesAttempted() const { return m_totalUpgradesAttempted; }
    
    // 配置管理
    void SetDropRate(const std::string& monsterName, int itemIndex, int rate);
    int GetDropRate(const std::string& monsterName, int itemIndex) const;

private:
    // 内部辅助方法
    bool LoadDropConfigFromFile(const std::string& fileName);
    bool LoadUpgradeConfigFromFile(const std::string& fileName);
    bool ParseDropConfigLine(const std::string& line, std::string& monsterName, ItemDropConfig& config);
    bool ParseUpgradeConfigLine(const std::string& line, ItemUpgradeConfig& config);
    
    // 物品属性计算
    int CalculateItemDurability(int itemIndex, int level) const;
    int CalculateItemValue(const TUserItem& item) const;
    bool RollDropChance(int dropRate) const;
    
    // 事件处理
    void HandlePlayerLogin(const struct PlayerLoginEventData& data);
    void HandleMonsterKilled(const struct MonsterKilledEventData& data);
    void HandleItemUsed(const struct ItemUsedEventData& data);
    
    // 日志记录
    void LogItemOperation(const std::string& operation, const std::string& details) const;
    void LogItemError(const std::string& operation, const std::string& error) const;
};
