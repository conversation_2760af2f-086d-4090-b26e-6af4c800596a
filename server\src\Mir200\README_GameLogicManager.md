# 游戏逻辑Manager实现文档

## 概述

本文档描述了Mir200服务器重构项目中游戏逻辑Manager的实现。这是Manager架构的第二层实现，包含了核心游戏逻辑功能的管理器。

## 实现的Manager

### 1. ItemManager - 物品管理器

**功能特性**:
- 物品创建和验证
- 物品掉落系统
- 物品强化系统
- 物品修理系统
- 物品堆叠检查
- 怪物掉落配置

**核心接口**:
- `IItemProvider` - 提供物品相关服务
- `IEventSubscriber` - 订阅相关事件

**配置文件**:
- `MonsterDrops.txt` - 怪物掉落配置
- `ItemUpgrade.txt` - 物品强化配置

### 2. MapManager - 地图管理器

**功能特性**:
- 地图加载和卸载
- 传送系统
- 安全区管理
- 怪物刷新点配置
- 地图对象管理
- 位置验证

**核心接口**:
- `IMapProvider` - 提供地图相关服务
- `IEventSubscriber` - 订阅相关事件

**配置文件**:
- `MapConfig.txt` - 地图基础配置
- `MapGates.txt` - 传送门配置
- `SafeZones.txt` - 安全区配置
- `MonsterSpawns.txt` - 怪物刷新配置

### 3. NPCManager - NPC管理器

**功能特性**:
- NPC创建和管理
- NPC脚本执行
- NPC商店系统
- NPC任务系统
- NPC重生系统
- NPC交互处理

**核心接口**:
- `INPCProvider` - 提供NPC相关服务
- `IEventSubscriber` - 订阅相关事件

**依赖关系**:
- `ScriptManager` - 脚本执行支持

### 4. MonsterManager - 怪物管理器

**功能特性**:
- 怪物创建和管理
- 怪物AI系统
- 怪物战斗系统
- 怪物刷新系统
- 怪物寻路和移动
- 怪物目标系统

**核心接口**:
- `IMonsterProvider` - 提供怪物相关服务
- `IEventSubscriber` - 订阅相关事件

**AI状态**:
- IDLE - 空闲
- PATROL - 巡逻
- CHASE - 追击
- ATTACK - 攻击
- FLEE - 逃跑
- RETURN - 返回

### 5. MagicManager - 魔法管理器

**功能特性**:
- 魔法施放系统
- 魔法效果处理
- 魔法伤害计算
- 魔法冷却系统
- 魔法目标系统
- 特殊魔法处理

**核心接口**:
- `IMagicProvider` - 提供魔法相关服务
- `IEventSubscriber` - 订阅相关事件

**魔法类型**:
- ATTACK - 攻击魔法
- HEAL - 治疗魔法
- BUFF - 增益魔法
- DEBUFF - 减益魔法
- SUMMON - 召唤魔法
- TELEPORT - 传送魔法

## 架构设计

### 依赖关系

```
ServiceContainer
├── EventBus (事件总线)
├── ItemManager
│   ├── EventBus
│   └── IPlayerProvider (待实现)
├── MapManager
│   └── EventBus
├── NPCManager
│   ├── EventBus
│   └── ScriptManager
├── MonsterManager
│   ├── EventBus
│   └── IPlayerProvider (待实现)
└── MagicManager
    ├── EventBus
    └── IPlayerProvider (待实现)
```

### 事件通信

所有Manager通过EventBus进行通信，支持的事件类型：
- `PlayerLogin` - 玩家登录
- `PlayerLogout` - 玩家登出
- `MapChange` - 地图切换
- `MonsterKilled` - 怪物死亡
- `ItemDropped` - 物品掉落
- `NPCInteraction` - NPC交互
- `MagicCast` - 魔法施放

### 线程安全

所有Manager都采用线程安全设计：
- 使用`std::shared_mutex`进行读写锁控制
- 使用`std::atomic`进行统计信息管理
- 使用`std::mutex`保护关键数据结构

## 编译和测试

### 编译步骤

```bash
# 创建构建目录
mkdir build && cd build

# 配置CMake
cmake -f ../CMakeLists_GameLogicManager.txt ..

# 编译所有组件
make build_all

# 运行测试
make run_tests
```

### 测试内容

`GameLogicManagerTest.cpp`包含以下测试：
1. ServiceContainer设置测试
2. ItemManager功能测试
3. MapManager功能测试
4. NPCManager功能测试
5. MonsterManager功能测试
6. MagicManager功能测试
7. Manager集成测试

## 配置文件格式

### 怪物掉落配置 (MonsterDrops.txt)
```
# 格式: MonsterName ItemIndex DropRate MinCount MaxCount MinLevel MaxLevel
Deer 1 1000 1 1 1 10
Wolf 2 500 1 2 5 15
```

### 地图传送门配置 (MapGates.txt)
```
# 格式: FromMap FromX FromY ToMap ToX ToY MinLevel MaxLevel Cost NeedItem ItemIndex
3 330 330 0 100 100 1 99 0 0 0
```

### 安全区配置 (SafeZones.txt)
```
# 格式: MapName X Y Range AllowPK AllowTrade AllowDrop
3 330 330 10 0 1 1
```

## 原项目兼容性

### 遵循原项目逻辑
- 100%遵循原项目Delphi代码逻辑
- 保持原项目的数值计算方式
- 维护原项目的协议编号一致性
- 不简化原项目的复杂逻辑

### 对应原项目文件
- ItemManager ↔ 物品相关功能
- MapManager ↔ Envir.pas, Environment相关
- NPCManager ↔ NPC相关功能, Merchant.pas
- MonsterManager ↔ ObjMon.pas, ObjMon2.pas
- MagicManager ↔ Magic相关功能

## 性能特性

### 内存管理
- 使用智能指针管理对象生命周期
- 采用对象池减少内存分配
- 实现延迟初始化优化启动时间

### 并发控制
- 读写锁优化并发读取性能
- 无锁数据结构用于高频操作
- 异步事件处理提高响应速度

### 缓存策略
- 配置信息缓存减少文件IO
- 常用数据预加载提高访问速度
- LRU缓存淘汰机制控制内存使用

## 扩展性

### 插件化支持
- 通过接口实现功能扩展
- 支持运行时动态加载
- 提供完整的事件钩子机制

### 配置化管理
- 所有游戏参数可配置
- 支持热重载配置文件
- 提供配置验证机制

## 监控和调试

### 统计信息
每个Manager都提供详细的统计信息：
- 操作计数器
- 性能指标
- 错误统计
- 资源使用情况

### 日志记录
- 详细的操作日志
- 错误日志记录
- 性能分析日志
- 调试信息输出

## 下一步计划

1. **完善依赖注入**: 实现IPlayerProvider等缺失接口
2. **增强事件系统**: 添加更多事件类型和处理器
3. **优化性能**: 进行性能测试和优化
4. **完善测试**: 增加更多单元测试和集成测试
5. **文档完善**: 添加API文档和使用示例

## 总结

游戏逻辑Manager的实现为Mir200服务器提供了完整的游戏核心功能管理，采用现代C++设计模式，保持与原项目100%的逻辑兼容性，为后续功能扩展奠定了坚实的基础。
