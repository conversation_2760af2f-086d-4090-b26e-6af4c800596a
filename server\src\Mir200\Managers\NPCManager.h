#pragma once

#include "IManager.h"
#include "EventData.h"
#include "../Common/Types.h"
#include <string>
#include <vector>
#include <unordered_map>
#include <mutex>
#include <shared_mutex>
#include <memory>

// 前向声明
class EventBus;
class NPC;
class PlayObject;
class ScriptManager;

/**
 * @brief NPC商店物品配置
 */
struct NPCShopItem {
    int itemIndex;          // 物品索引
    int price;              // 价格
    int stock;              // 库存(-1表示无限)
    int minLevel;           // 最小等级要求
    int maxLevel;           // 最大等级要求
    bool needItem;          // 是否需要特定物品
    int needItemIndex;      // 需要的物品索引
    int needItemCount;      // 需要的物品数量
    
    NPCShopItem() : itemIndex(0), price(0), stock(-1), minLevel(0), maxLevel(999), 
                   needItem(false), needItemIndex(0), needItemCount(0) {}
};

/**
 * @brief NPC任务配置
 */
struct NPCQuest {
    int questId;            // 任务ID
    std::string questName;  // 任务名称
    std::string description; // 任务描述
    int minLevel;           // 最小等级要求
    int maxLevel;           // 最大等级要求
    bool repeatable;        // 是否可重复
    int cooldown;           // 冷却时间(秒)
    std::vector<int> preQuests; // 前置任务
    
    NPCQuest() : questId(0), minLevel(0), maxLevel(999), repeatable(false), cooldown(0) {}
};

/**
 * @brief NPC配置
 */
struct NPCConfig {
    std::string npcName;    // NPC名称
    std::string scriptFile; // 脚本文件
    int npcType;            // NPC类型 (0=普通, 1=商人, 2=守卫, 3=任务)
    std::string mapName;    // 所在地图
    int x, y;               // 坐标
    int face;               // 朝向
    int appearance;         // 外观
    bool respawn;           // 是否重生
    int respawnTime;        // 重生时间(秒)
    
    std::vector<NPCShopItem> shopItems; // 商店物品
    std::vector<NPCQuest> quests;       // 任务列表
    
    NPCConfig() : npcType(0), x(0), y(0), face(0), appearance(0), 
                 respawn(true), respawnTime(60) {}
};

/**
 * @brief NPC管理器
 * 负责管理所有NPC相关功能
 * 对应原项目的NPC系统功能
 */
class NPCManager : public IManager, public INPCProvider, public IEventSubscriber {
private:
    std::string m_managerName;
    bool m_initialized;
    
    // 依赖注入
    EventBus* m_eventBus;
    ScriptManager* m_scriptManager;
    
    // NPC数据
    std::unordered_map<std::string, NPCConfig> m_npcConfigs;
    std::unordered_map<std::string, std::unique_ptr<NPC>> m_npcs;
    std::unordered_map<std::string, std::vector<std::string>> m_mapNPCs; // 地图->NPC列表
    
    // 线程安全
    mutable std::shared_mutex m_configMutex;
    mutable std::shared_mutex m_npcMutex;
    mutable std::shared_mutex m_mapMutex;
    
    // 统计信息
    std::atomic<uint64_t> m_totalNPCsCreated;
    std::atomic<uint64_t> m_totalInteractions;
    std::atomic<uint64_t> m_totalScriptExecutions;

public:
    NPCManager();
    virtual ~NPCManager();
    
    // IManager接口实现
    bool Initialize() override;
    void Finalize() override;
    void Update() override;
    const std::string& GetManagerName() const override;
    
    // 依赖注入
    void SetEventBus(EventBus* eventBus);
    void SetScriptManager(ScriptManager* scriptManager);
    
    // INPCProvider接口实现
    const NPCInfo* GetNPCInfo(const std::string& npcName) const override;
    NPC* CreateNPC(const std::string& npcName, const std::string& mapName, int x, int y) override;
    bool ExecuteNPCScript(const std::string& npcName, PlayObject* player, const std::string& label) override;
    
    // IEventSubscriber接口实现
    void OnEvent(const std::string& eventType, const EventData& data) override;
    
    // NPC管理功能
    bool LoadNPCData();
    bool LoadNPCConfigs(const std::string& configFile);
    bool LoadNPCScripts(const std::string& scriptDir);
    
    // NPC操作
    bool SpawnNPC(const std::string& npcName);
    bool RemoveNPC(const std::string& npcName);
    NPC* FindNPC(const std::string& npcName);
    std::vector<NPC*> GetNPCsInMap(const std::string& mapName);
    
    // NPC交互
    bool InteractWithNPC(PlayObject* player, const std::string& npcName);
    bool OpenNPCShop(PlayObject* player, const std::string& npcName);
    bool BuyFromNPC(PlayObject* player, const std::string& npcName, int itemIndex, int count);
    bool SellToNPC(PlayObject* player, const std::string& npcName, const TUserItem& item);
    
    // NPC任务系统
    std::vector<NPCQuest> GetAvailableQuests(PlayObject* player, const std::string& npcName);
    bool AcceptQuest(PlayObject* player, const std::string& npcName, int questId);
    bool CompleteQuest(PlayObject* player, const std::string& npcName, int questId);
    bool CanAcceptQuest(PlayObject* player, const NPCQuest& quest);
    
    // NPC配置管理
    void AddNPCConfig(const NPCConfig& config);
    void RemoveNPCConfig(const std::string& npcName);
    NPCConfig* GetNPCConfig(const std::string& npcName);
    void UpdateNPCConfig(const std::string& npcName, const NPCConfig& config);
    
    // NPC商店管理
    void AddShopItem(const std::string& npcName, const NPCShopItem& item);
    void RemoveShopItem(const std::string& npcName, int itemIndex);
    void UpdateShopItem(const std::string& npcName, int itemIndex, const NPCShopItem& item);
    std::vector<NPCShopItem> GetShopItems(const std::string& npcName);
    
    // NPC重生系统
    void ProcessRespawns();
    bool ShouldRespawnNPC(const std::string& npcName);
    void RespawnNPC(const std::string& npcName);
    
    // 统计信息
    uint64_t GetTotalNPCsCreated() const { return m_totalNPCsCreated; }
    uint64_t GetTotalInteractions() const { return m_totalInteractions; }
    uint64_t GetTotalScriptExecutions() const { return m_totalScriptExecutions; }
    
    // 地图相关
    void RegisterNPCToMap(const std::string& mapName, const std::string& npcName);
    void UnregisterNPCFromMap(const std::string& mapName, const std::string& npcName);
    std::vector<std::string> GetMapNPCNames(const std::string& mapName) const;

private:
    // 内部辅助方法
    bool LoadNPCConfigFromFile(const std::string& fileName);
    bool LoadNPCScriptFromFile(const std::string& npcName, const std::string& scriptFile);
    bool ParseNPCConfigLine(const std::string& line, NPCConfig& config);
    bool ParseShopItemLine(const std::string& line, NPCShopItem& item);
    bool ParseQuestLine(const std::string& line, NPCQuest& quest);
    
    // NPC创建和销毁
    std::unique_ptr<NPC> CreateNPCInstance(const NPCConfig& config);
    bool ValidateNPCConfig(const NPCConfig& config) const;
    bool ValidateNPCPosition(const std::string& mapName, int x, int y) const;
    
    // 脚本处理
    bool LoadNPCScript(const std::string& npcName);
    bool ExecuteNPCFunction(const std::string& npcName, PlayObject* player, 
                           const std::string& function, const std::vector<std::string>& params);
    
    // 商店逻辑
    bool ValidatePurchase(PlayObject* player, const NPCShopItem& item, int count);
    bool ValidateSale(PlayObject* player, const TUserItem& item, const std::string& npcName);
    int CalculateSellPrice(const TUserItem& item, const std::string& npcName);
    
    // 任务逻辑
    bool CheckQuestPrerequisites(PlayObject* player, const NPCQuest& quest);
    bool CheckQuestCooldown(PlayObject* player, int questId);
    void SetQuestCooldown(PlayObject* player, int questId);
    
    // 事件处理
    void HandlePlayerLogin(const struct PlayerLoginEventData& data);
    void HandlePlayerMapChange(const struct MapChangeEventData& data);
    void HandleNPCInteraction(const struct NPCInteractionEventData& data);
    
    // 日志记录
    void LogNPCOperation(const std::string& operation, const std::string& details) const;
    void LogNPCError(const std::string& operation, const std::string& error) const;
};
