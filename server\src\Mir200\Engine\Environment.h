#pragma once

// Mir200 Environment - Game environment management
// Based on delphi/EM2Engine/Envir.pas - Following original project structure
// Complete implementation matching original TEnvirnoment class

#include "Common/M2Share.h"
#include <string>
#include <vector>
#include <memory>
#include <mutex>
#include <shared_mutex>
#include <unordered_map>
#include <list>

// Forward declarations
class BaseObject;
class PlayObject;
class Event;
class TMerchant;

// Map unit information structure (matching TMapUnitInfo)
struct MapUnitInfo {
    WORD bk_img = 0;        // Background image ($8000 = no move flag)
    WORD mid_img = 0;       // Middle image
    WORD fr_img = 0;        // Foreground image
    BYTE door_index = 0;    // Door index ($80 = door)
    BYTE door_offset = 0;   // Door offset
    BYTE ani_frame = 0;     // Animation frame ($80 = draw alpha)
    BYTE ani_tick = 0;      // Animation tick
    BYTE area = 0;          // Area type
    BYTE light = 0;         // Light level (0..1..4)
};

// Map cell information structure (matching TMapCellinfo)
struct MapCellInfo {
    BYTE flag = 0;          // Cell flag (0=walkable, 1=blocked, 2=fly blocked)
    std::list<void*> obj_list;  // Object list in this cell
};

// Map header structure (matching TMapHeader)
struct MapHeader {
    WORD width = 0;
    WORD height = 0;
    std::string title;
    DWORD update_date = 0;
    std::array<char, 23> reserved = {};
};

// Object types in map cells (matching original constants)
enum class ObjectType : BYTE {
    OS_MOVINGOBJECT = 1,    // Moving objects (players, monsters)
    OS_ITEMOBJECT = 2,      // Items on ground
    OS_EVENTOBJECT = 3,     // Events
    OS_DOOR = 4,            // Doors
    OS_GATEOBJECT = 5       // Gates/teleports
};

// OS Object structure for map cells
struct OSObject {
    ObjectType type = ObjectType::OS_MOVINGOBJECT;
    void* cell_obj = nullptr;
    DWORD add_time = 0;
};

// Door information structure
struct DoorInfo {
    int x = 0;
    int y = 0;
    int index = 0;
    struct DoorStatus {
        bool opened = false;
        bool flag = false;
        int value = 0;
        DWORD open_tick = 0;
        int ref_count = 1;
    };
    std::shared_ptr<DoorStatus> status;
};

// Gate object structure
struct GateObj {
    bool flag = false;
    class Environment* dest_envir = nullptr;
    int dest_x = 0;
    int dest_y = 0;
};

// Forward declaration - MapQuestInfo is defined in LocalDatabase.h
struct MapQuestInfo;

// Map flags structure (matching all original TEnvirnoment boolean fields)
struct MapFlags {
    // Basic zone types
    bool is_safe = false;               // m_boSAFE - Safe zone
    bool is_fight_zone = false;         // m_boFightZone - Fight zone
    bool is_fight3_zone = false;        // m_boFight3Zone - Guild war zone
    bool is_dark = false;               // m_boDARK - Dark environment
    bool is_day = false;                // m_boDAY - Day environment
    bool is_quiz = false;               // m_boQUIZ - Quiz zone

    // Movement restrictions
    bool no_reconnect = false;          // m_boNORECONNECT - No reconnect
    bool need_hole = false;             // m_boNEEDHOLE - Need hole to enter
    bool no_recall = false;             // m_boNORECALL - No recall
    bool no_guild_recall = false;       // m_boNOGUILDRECALL - No guild recall
    bool no_dear_recall = false;        // m_boNODEARRECALL - No dear recall
    bool no_master_recall = false;      // m_boNOMASTERRECALL - No master recall
    bool no_random_move = false;        // m_boNORANDOMMOVE - No random move
    bool no_position_move = false;      // m_boNOPOSITIONMOVE - No position move

    // Item and magic restrictions
    bool no_drug = false;               // m_boNODRUG - No drug use
    bool no_fire_magic = false;         // m_boUnAllowFireMagic - No fire magic
    bool un_allow_std_items = false;    // m_boUnAllowStdItems - Restrict items

    // Special zones
    bool is_mine = false;               // m_boMINE - Mine zone

    // Movement permissions
    bool run_human = false;             // m_boRUNHUMAN - Allow human run
    bool run_monster = false;           // m_boRUNMON - Allow monster run

    // Auto effects
    bool inc_hp = false;                // m_boINCHP - Auto increase HP
    bool dec_hp = false;                // m_boDECHP - Auto decrease HP
    bool inc_game_gold = false;         // m_boIncGameGold - Auto increase gold
    bool dec_game_gold = false;         // m_boDecGameGold - Auto decrease gold
    bool inc_game_point = false;        // m_boINCGAMEPOINT - Auto increase points
    bool dec_game_point = false;        // m_boDECGAMEPOINT - Auto decrease points

    // Special effects
    bool has_music = false;             // m_boMUSIC - Has background music
    bool exp_rate = false;              // m_boEXPRATE - Experience rate modifier

    // PK effects
    bool pk_win_level = false;          // m_boPKWINLEVEL - PK win gain level
    bool pk_win_exp = false;            // m_boPKWINEXP - PK win gain exp
    bool pk_lost_level = false;         // m_boPKLOSTLEVEL - PK lose level
    bool pk_lost_exp = false;           // m_boPKLOSTEXP - PK lose exp

    // Numeric parameters
    int pk_win_level_value = 0;         // m_nPKWINLEVEL
    int pk_lost_level_value = 0;        // m_nPKLOSTLEVEL
    int pk_win_exp_value = 0;           // m_nPKWINEXP
    int pk_lost_exp_value = 0;          // m_nPKLOSTEXP

    int dec_hp_time = 0;                // m_nDECHPTIME
    int dec_hp_point = 0;               // m_nDECHPPOINT
    int inc_hp_time = 0;                // m_nINCHPTIME
    int inc_hp_point = 0;               // m_nINCHPPOINT

    int dec_game_gold_time = 0;         // m_nDECGAMEGOLDTIME
    int dec_game_gold_value = 0;        // m_nDecGameGold
    int inc_game_gold_time = 0;         // m_nINCGAMEGOLDTIME
    int inc_game_gold_value = 0;        // m_nIncGameGold

    int dec_game_point_time = 0;        // m_nDECGAMEPOINTTIME
    int dec_game_point_value = 0;       // m_nDECGAMEPOINT
    int inc_game_point_time = 0;        // m_nINCGAMEPOINTTIME
    int inc_game_point_value = 0;       // m_nINCGAMEPOINT

    int music_id = 0;                   // m_nMUSICID
    int exp_rate_value = 100;           // m_nEXPRATE (percentage)

    // String parameters
    std::string no_reconnect_map;       // sNoReconnectMap
    std::string un_allow_std_items_text; // Restricted items list
};

// Environment class - Complete game environment management
// Based on original TEnvirnoment class from Envir.pas
class Environment {
private:
    // Basic map information (matching original fields)
    std::string m_map_name;             // sMapName
    std::string m_map_desc;             // sMapDesc
    std::string m_main_map_name;        // sMainMapName
    std::string m_sub_map_name;         // sSubMapName
    bool m_is_main_map = false;         // m_boMainMap

    // Map dimensions and data
    int m_width = 0;                    // m_nWidth
    int m_height = 0;                   // m_nHeight
    std::vector<MapCellInfo> m_map_cell_array; // MapCellArray

    // Server and map configuration
    int m_server_index = 0;             // nServerIndex
    int m_request_level = 0;            // nRequestLevel
    int m_min_map = 0;                  // nMinMap

    // Map flags and properties
    MapFlags m_map_flags;               // All boolean and numeric flags

    // Object lists
    std::vector<std::shared_ptr<DoorInfo>> m_door_list;     // m_DoorList
    std::vector<std::shared_ptr<MapQuestInfo>> m_quest_list; // m_QuestList

    // Object counts
    int m_monster_count = 0;            // m_nMonCount
    int m_human_count = 0;              // m_nHumCount

    // Quest NPC
    TMerchant* m_quest_npc = nullptr;   // QuestNPC

    // Special flags for quest system
    int m_need_set_on_flag = 0;         // nNEEDSETONFlag
    int m_need_on_off = 0;              // nNeedONOFF

    // Restricted items management
    std::vector<std::string> m_un_allow_std_items_list;     // Item names
    std::vector<int> m_un_allow_std_items_idx_list;         // Item indices

    // Thread safety
    mutable std::shared_mutex m_mutex;

    // Internal state
    bool m_active = false;
    bool m_initialized = false;
    bool m_temp_flag = false;           // bo2C - temporary flag for operations

    // Private methods
    void Initialize(int width, int height);
    bool GetMapCellInfo(int x, int y, MapCellInfo*& cell_info);
    bool GetMapCellInfo(int x, int y, const MapCellInfo*& cell_info) const;
    void UpdateUnAllowItemsList();

public:
    // Constructor and destructor
    Environment(const std::string& map_name);
    Environment(const std::string& map_name, int width, int height);
    ~Environment();

    // Core lifecycle
    bool Initialize();
    void Finalize();

    // Map data loading
    bool LoadMapData(const std::string& map_file);

    // Basic information access
    const std::string& GetMapName() const { return m_map_name; }
    const std::string& GetMapDesc() const { return m_map_desc; }
    const std::string& GetMainMapName() const { return m_main_map_name; }
    const std::string& GetSubMapName() const { return m_sub_map_name; }
    int GetServerIndex() const { return m_server_index; }
    int GetRequestLevel() const { return m_request_level; }
    int GetMinMap() const { return m_min_map; }
    bool IsMainMap() const { return m_is_main_map; }
    int GetWidth() const { return m_width; }
    int GetHeight() const { return m_height; }

    // Basic information modification
    void SetMapDesc(const std::string& desc) { m_map_desc = desc; }
    void SetMainMapName(const std::string& name) { m_main_map_name = name; }
    void SetSubMapName(const std::string& name) { m_sub_map_name = name; }
    void SetServerIndex(int index) { m_server_index = index; }
    void SetRequestLevel(int level) { m_request_level = level; }
    void SetMinMap(int min_map) { m_min_map = min_map; }
    void SetMainMap(bool is_main) { m_is_main_map = is_main; }

    // Map flags management
    void SetMapFlags(const MapFlags& flags);
    const MapFlags& GetMapFlags() const { return m_map_flags; }

    // Object counts
    int GetMonsterCount() const { return m_monster_count; }
    int GetHumanCount() const { return m_human_count; }
    void AddObjectCount(BaseObject* obj);
    void DelObjectCount(BaseObject* obj);

    // State management
    bool IsActive() const { return m_active; }
    bool IsInitialized() const { return m_initialized; }

    // Map cell operations (matching original methods)
    void* AddToMap(int x, int y, ObjectType type, void* obj);
    int DeleteFromMap(int x, int y, ObjectType type, void* obj);
    int MoveToMovingObject(int curr_x, int curr_y, void* obj, int new_x, int new_y, bool flag);

    // Map walking and movement checks
    bool CanWalk(int x, int y, bool flag) const;
    bool CanWalkOfItem(int x, int y, bool flag, bool item) const;
    bool CanWalkEx(int x, int y, bool flag) const;
    bool CanSafeWalk(int x, int y) const;
    bool CanFly(int sx, int sy, int dx, int dy) const;

    // Map object queries
    void* GetItem(int x, int y);
    void* GetItemEx(int x, int y, int& count);
    void* GetMovingObject(int x, int y, bool flag);
    void* GetEvent(int x, int y);
    DoorInfo* GetDoor(int x, int y);

    // Map object management
    int GetXYObjCount(int x, int y) const;
    bool GetXYHuman(int x, int y) const;
    bool IsValidObject(int x, int y, int range, void* obj) const;
    int GetRangeBaseObject(int x, int y, int range, bool flag, std::vector<BaseObject*>& obj_list);
    int GetBaseObjects(int x, int y, bool flag, std::vector<BaseObject*>& obj_list);

    // Position and direction utilities
    bool GetNextPosition(int sx, int sy, int dir, int flag, int& new_x, int& new_y) const;

    // Map flags and cell operations
    void SetMapXYFlag(int x, int y, bool flag);
    void VerifyMapTime(int x, int y, BaseObject* obj);

    // Door management
    void AddDoorToMap();
    bool ArroundDoorOpened(int x, int y) const;

    // Quest system
    bool CreateQuest(int flag, int value, const std::string& monster,
                    const std::string& item, const std::string& npc, bool grouped);
    TMerchant* GetQuestNPC(BaseObject* obj, const std::string& char_name,
                          const std::string& str, bool flag);
    bool IsCheapStuff() const;

    // Mine events
    void* AddToMapMineEvent(int x, int y, int type, Event* event);

    // Environment processing
    void ProcessEnvironment();
    void ProcessMapEvents();
    void ProcessMapEffects();
    void ProcessAutoHP();
    void ProcessAutoGameGold();
    void ProcessAutoGamePoint();
    void ProcessPKEffects();

    // Data management
    void SaveEnvironmentData();

    // Environment information
    std::string GetEnvironmentInfo() const;

    // Map zone checking methods (based on map flags)
    bool IsSafeZone(const Point& pos) const;
    bool IsFightZone(const Point& pos) const;
    bool IsFight3Zone(const Point& pos) const;
    bool IsDarknessZone(const Point& pos) const;
    bool IsDayLightZone(const Point& pos) const;
    bool IsQuizZone(const Point& pos) const;
    bool IsNoReconnectZone(const Point& pos) const;
    bool IsNeedHoleZone(const Point& pos) const;
    bool IsNoRecallZone(const Point& pos) const;
    bool IsNoGuildRecallZone(const Point& pos) const;
    bool IsNoDearRecallZone(const Point& pos) const;
    bool IsNoMasterRecallZone(const Point& pos) const;
    bool IsNoRandomMoveZone(const Point& pos) const;
    bool IsNoDrugZone(const Point& pos) const;
    bool IsMineZone(const Point& pos) const;
    bool IsNoPositionMoveZone(const Point& pos) const;
    bool IsRunHumanZone(const Point& pos) const;
    bool IsRunMonsterZone(const Point& pos) const;
    bool IsNoFireMagicZone(const Point& pos) const;

    // Item restriction checking (matching original AllowStdItems)
    bool AllowStdItems(const std::string& item_name) const;
    bool AllowStdItems(int item_idx) const;

    // Utility methods
    bool IsInMapRange(int x, int y) const;
    bool IsInMapRange(const Point& pos) const;

private:
    // Internal helper methods
    bool CheckMapEventCondition(int x, int y) const;
    void CleanupMapCells();
    void InitializeMapFlags();
};
