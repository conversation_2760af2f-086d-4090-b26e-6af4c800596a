#include "LogManager.h"
#include <iostream>
#include <filesystem>
#include <iomanip>
#include <sstream>

LogManager::LogManager() 
    : m_manager<PERSON>ame("LogManager"),
      m_logDirectory("logs/"),
      m_logFileName("mir200.log"),
      m_minLogLevel(LOG_INFO),
      m_enableConsoleOutput(true),
      m_enableFileOutput(true),
      m_enableAsyncLogging(true),
      m_running(false),
      m_totalLogs(0),
      m_droppedLogs(0),
      m_maxQueueSize(10000) {
}

LogManager::~LogManager() {
    Finalize();
}

bool LogManager::Initialize() {
    std::cout << "[LogManager] Initializing..." << std::endl;
    
    // 创建日志目录
    std::filesystem::create_directories(m_logDirectory);
    
    // 打开日志文件
    if (m_enableFileOutput && !OpenLogFile()) {
        std::cerr << "[LogManager] Warning: Failed to open log file" << std::endl;
    }
    
    // 启动异步日志线程
    if (m_enableAsyncLogging) {
        m_running = true;
        m_logThread = std::thread(&LogManager::LogThreadFunction, this);
    }
    
    Info("LogManager initialized successfully", "LogManager");
    return true;
}

void LogManager::Finalize() {
    Info("LogManager finalizing...", "LogManager");
    
    // 停止异步日志线程
    if (m_enableAsyncLogging && m_running) {
        m_running = false;
        m_queueCondition.notify_all();
        
        if (m_logThread.joinable()) {
            m_logThread.join();
        }
    }
    
    // 刷新并关闭日志文件
    FlushLogs();
    CloseLogFile();
    
    std::cout << "[LogManager] Finalized" << std::endl;
}

void LogManager::Update() {
    // 日志管理器通常不需要每帧更新
    // 可以在这里检查日志文件大小并进行轮转
    static int updateCounter = 0;
    if (++updateCounter % 1000 == 0) { // 每1000次更新检查一次
        // 检查日志文件大小，如果超过限制则轮转
        if (m_logFile.is_open()) {
            auto pos = m_logFile.tellp();
            if (pos > 100 * 1024 * 1024) { // 100MB
                RotateLogFile();
            }
        }
    }
}

const std::string& LogManager::GetManagerName() const {
    return m_managerName;
}

void LogManager::SetLogDirectory(const std::string& directory) {
    m_logDirectory = directory;
    if (!m_logDirectory.empty() && m_logDirectory.back() != '/') {
        m_logDirectory += '/';
    }
}

void LogManager::SetLogFileName(const std::string& fileName) {
    m_logFileName = fileName;
}

void LogManager::SetMinLogLevel(LogLevel level) {
    m_minLogLevel = level;
}

void LogManager::SetConsoleOutput(bool enable) {
    m_enableConsoleOutput = enable;
}

void LogManager::SetFileOutput(bool enable) {
    m_enableFileOutput = enable;
}

void LogManager::SetAsyncLogging(bool enable) {
    m_enableAsyncLogging = enable;
}

void LogManager::SetMaxQueueSize(size_t maxSize) {
    m_maxQueueSize = maxSize;
}

void LogManager::Log(LogLevel level, const std::string& message, const std::string& component) {
    if (level < m_minLogLevel) {
        return;
    }
    
    ++m_totalLogs;
    
    LogEntry entry(level, message, component);
    
    if (m_enableAsyncLogging && m_running) {
        // 异步日志
        std::unique_lock<std::mutex> lock(m_queueMutex);
        if (m_logQueue.size() >= m_maxQueueSize) {
            ++m_droppedLogs;
            return;
        }
        
        m_logQueue.push(entry);
        m_queueCondition.notify_one();
    } else {
        // 同步日志
        WriteLogEntry(entry);
    }
}

void LogManager::Debug(const std::string& message, const std::string& component) {
    Log(LOG_DEBUG, message, component);
}

void LogManager::Info(const std::string& message, const std::string& component) {
    Log(LOG_INFO, message, component);
}

void LogManager::Warning(const std::string& message, const std::string& component) {
    Log(LOG_WARNING, message, component);
}

void LogManager::Error(const std::string& message, const std::string& component) {
    Log(LOG_ERROR, message, component);
}

void LogManager::Critical(const std::string& message, const std::string& component) {
    Log(LOG_CRITICAL, message, component);
}

void LogManager::LogFormat(LogLevel level, const std::string& component, const std::string& format) {
    Log(level, format, component);
}

void LogManager::DebugFormat(const std::string& component, const std::string& format) {
    LogFormat(LOG_DEBUG, component, format);
}

void LogManager::InfoFormat(const std::string& component, const std::string& format) {
    LogFormat(LOG_INFO, component, format);
}

void LogManager::WarningFormat(const std::string& component, const std::string& format) {
    LogFormat(LOG_WARNING, component, format);
}

void LogManager::ErrorFormat(const std::string& component, const std::string& format) {
    LogFormat(LOG_ERROR, component, format);
}

void LogManager::CriticalFormat(const std::string& component, const std::string& format) {
    LogFormat(LOG_CRITICAL, component, format);
}

size_t LogManager::GetQueueSize() const {
    std::lock_guard<std::mutex> lock(m_queueMutex);
    return m_logQueue.size();
}

void LogManager::FlushLogs() {
    if (m_enableAsyncLogging) {
        // 等待队列清空
        while (GetQueueSize() > 0) {
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
    }
    
    std::lock_guard<std::mutex> lock(m_fileMutex);
    if (m_logFile.is_open()) {
        m_logFile.flush();
    }
}

void LogManager::RotateLogFile() {
    std::lock_guard<std::mutex> lock(m_fileMutex);
    
    if (m_logFile.is_open()) {
        m_logFile.close();
    }
    
    // 重命名当前日志文件
    std::string oldFilePath = m_logDirectory + m_logFileName;
    std::string newFilePath = m_logDirectory + m_logFileName + ".old";
    std::filesystem::rename(oldFilePath, newFilePath);
    
    // 重新打开日志文件
    OpenLogFile();
}

void LogManager::LogThreadFunction() {
    while (m_running) {
        std::unique_lock<std::mutex> lock(m_queueMutex);
        
        m_queueCondition.wait(lock, [this] {
            return !m_logQueue.empty() || !m_running;
        });
        
        while (!m_logQueue.empty()) {
            LogEntry entry = m_logQueue.front();
            m_logQueue.pop();
            lock.unlock();
            
            WriteLogEntry(entry);
            
            lock.lock();
        }
    }
}

void LogManager::WriteLogEntry(const LogEntry& entry) {
    std::string formattedEntry = FormatLogEntry(entry);

    // 控制台输出
    if (m_enableConsoleOutput) {
        if (entry.level >= LogLevel::ERROR) {
            std::cerr << formattedEntry << std::endl;
        } else {
            std::cout << formattedEntry << std::endl;
        }
    }

    // 文件输出
    if (m_enableFileOutput) {
        std::lock_guard<std::mutex> lock(m_fileMutex);
        if (m_logFile.is_open()) {
            m_logFile << formattedEntry << std::endl;
        }
    }
}

std::string LogManager::FormatLogEntry(const LogEntry& entry) const {
    std::ostringstream oss;

    // 时间戳
    oss << "[" << GetTimestamp(entry.timestamp) << "] ";

    // 日志级别
    oss << "[" << LogLevelToString(entry.level) << "] ";

    // 组件名称
    if (!entry.component.empty()) {
        oss << "[" << entry.component << "] ";
    }

    // 线程ID
    oss << "[Thread:" << entry.threadId << "] ";

    // 消息内容
    oss << entry.message;

    return oss.str();
}

std::string LogManager::LogLevelToString(LogLevel level) const {
    switch (level) {
        case LOG_DEBUG: return "DEBUG";
        case LOG_INFO: return "INFO";
        case LOG_WARNING: return "WARN";
        case LOG_ERROR: return "ERROR";
        case LOG_CRITICAL: return "CRIT";
        default: return "UNKNOWN";
    }
}

std::string LogManager::GetTimestamp(const std::chrono::system_clock::time_point& timePoint) const {
    auto time_t = std::chrono::system_clock::to_time_t(timePoint);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        timePoint.time_since_epoch()) % 1000;

    std::ostringstream oss;
    oss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    oss << "." << std::setfill('0') << std::setw(3) << ms.count();

    return oss.str();
}

bool LogManager::OpenLogFile() {
    std::string fullPath = m_logDirectory + m_logFileName;
    m_logFile.open(fullPath, std::ios::app);
    return m_logFile.is_open();
}

void LogManager::CloseLogFile() {
    if (m_logFile.is_open()) {
        m_logFile.close();
    }
}
