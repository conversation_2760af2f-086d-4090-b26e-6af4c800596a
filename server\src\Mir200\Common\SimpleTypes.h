#pragma once

// Simple types for basic compilation
#ifdef _WIN32
#include <windows.h>
#else
using BYTE = uint8_t;
using WORD = uint16_t;
using DWORD = uint32_t;
#endif

#include <cstdint>
#include <string>
#include <memory>
#include <cstring>

// Basic point structure
struct Point {
    int x;
    int y;
    
    Point() : x(0), y(0) {}
    Point(int x_, int y_) : x(x_), y(y_) {}
};

// Simple ability structure
struct SimpleAbility {
    WORD level;
    WORD hp;
    WORD mp;
    WORD dc;
    WORD mc;
    WORD sc;
    WORD ac;
    WORD mac;
    
    SimpleAbility() : level(1), hp(0), mp(0), dc(0), mc(0), sc(0), ac(0), mac(0) {}
};

// Simple user item
struct SimpleUserItem {
    int make_index;
    WORD w_index;
    WORD dura;
    WORD dura_max;
    BYTE bt_value[14];
    
    SimpleUserItem() : make_index(0), w_index(0), dura(0), dura_max(0) {
        std::memset(bt_value, 0, sizeof(bt_value));
    }
};

// Simple message
struct SimpleMessage {
    int recog;
    WORD ident;
    WORD param;
    WORD tag;
    WORD series;
    
    SimpleMessage() : recog(0), ident(0), param(0), tag(0), series(0) {}
};

// Forward declarations
class BaseObject;
class PlayObject;
class Environment;
class UserEngine;
class LocalDatabase;

// Stub classes for compilation
class Environment {
public:
    std::string GetMapName() const { return ""; }
    bool Initialize() { return true; }
    void ProcessEnvironment() {}
    bool IsActive() const { return true; }
    void Finalize() {}
    void SaveEnvironmentData() {}
};

class Guild {
public:
    bool Initialize() { return true; }
    void ProcessGuild() {}
    void SaveGuildData() {}
    void Finalize() {}
};

class Castle {
public:
    void ProcessCastle() {}
    void SaveCastleData() {}
    void Finalize() {}
};

// Utility functions
inline DWORD GetCurrentTime() {
#ifdef _WIN32
    return static_cast<DWORD>(GetTickCount());
#else
    return static_cast<DWORD>(std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::steady_clock::now().time_since_epoch()).count());
#endif
}

inline int Random(int max) {
    return rand() % max;
}
