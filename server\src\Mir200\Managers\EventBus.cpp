#include "EventBus.h"
#include <iostream>

EventBus::EventBus() 
    : m_running(false), 
      m_totalEvents(0), 
      m_processedEvents(0),
      m_maxQueueSize(10000) {
}

EventBus::~EventBus() {
    Stop();
}

bool EventBus::Start() {
    if (m_running) return true;
    
    m_running = true;
    m_asyncThread = std::thread(&EventBus::AsyncEventLoop, this);
    
    std::cout << "[EventBus] Started" << std::endl;
    return true;
}

void EventBus::Stop() {
    if (!m_running) return;
    
    m_running = false;
    m_asyncCondition.notify_all();
    
    if (m_asyncThread.joinable()) {
        m_asyncThread.join();
    }
    
    std::cout << "[EventBus] Stopped" << std::endl;
}

void EventBus::Subscribe(const std::string& eventType, EventHandler handler) {
    std::lock_guard<std::mutex> lock(m_syncMutex);
    m_syncHandlers[eventType].push_back(std::move(handler));
    
    std::cout << "[EventBus] Subscription added for event: " << eventType << std::endl;
}

void EventBus::Unsubscribe(const std::string& eventType) {
    std::lock_guard<std::mutex> lock(m_syncMutex);
    m_syncHandlers.erase(eventType);
    
    std::cout << "[EventBus] Unsubscribed from event: " << eventType << std::endl;
}

void EventBus::Publish(const std::string& eventType, const EventData& data) {
    ++m_totalEvents;
    PublishToHandlers(eventType, data);
    ++m_processedEvents;
}

void EventBus::PublishAsync(const std::string& eventType, std::unique_ptr<EventData> data) {
    ++m_totalEvents;
    
    {
        std::lock_guard<std::mutex> lock(m_asyncMutex);
        if (m_asyncQueue.size() >= m_maxQueueSize) {
            std::cerr << "[EventBus] Warning: Event queue full, dropping event: " << eventType << std::endl;
            return;
        }
        
        m_asyncQueue.emplace(eventType, std::move(data));
    }
    
    m_asyncCondition.notify_one();
}

void EventBus::ProcessAsyncEvents() {
    // 手动处理异步事件（如果不使用异步线程）
    std::unique_lock<std::mutex> lock(m_asyncMutex);
    
    while (!m_asyncQueue.empty()) {
        auto [eventType, eventData] = std::move(m_asyncQueue.front());
        m_asyncQueue.pop();
        lock.unlock();
        
        try {
            PublishToHandlers(eventType, *eventData);
            ++m_processedEvents;
        } catch (const std::exception& e) {
            std::cerr << "[EventBus] Error processing event " << eventType << ": " << e.what() << std::endl;
        }
        
        lock.lock();
    }
}

size_t EventBus::GetQueueSize() const {
    std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(m_asyncMutex));
    return m_asyncQueue.size();
}

void EventBus::AsyncEventLoop() {
    while (m_running) {
        std::unique_lock<std::mutex> lock(m_asyncMutex);
        
        m_asyncCondition.wait(lock, [this] {
            return !m_asyncQueue.empty() || !m_running;
        });
        
        while (!m_asyncQueue.empty()) {
            auto eventPair = std::move(m_asyncQueue.front());
            std::string eventType = eventPair.first;
            std::unique_ptr<EventData> eventData = std::move(eventPair.second);
            m_asyncQueue.pop();
            lock.unlock();

            try {
                PublishToHandlers(eventType, *eventData);
                ++m_processedEvents;
            } catch (const std::exception& e) {
                std::cerr << "[EventBus] Error processing async event " << eventType << ": " << e.what() << std::endl;
            }

            lock.lock();
        }
    }
}



void EventBus::PublishToHandlers(const std::string& eventType, const EventData& data) {
    std::lock_guard<std::mutex> lock(m_syncMutex);

    auto it = m_syncHandlers.find(eventType);
    if (it != m_syncHandlers.end()) {
        for (const auto& handler : it->second) {
            try {
                handler(data);
            } catch (const std::exception& e) {
                std::cerr << "[EventBus] Event handler error for " << eventType << ": " << e.what() << std::endl;
            }
        }
    }
}
