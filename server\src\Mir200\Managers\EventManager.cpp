#include "EventManager.h"
#include "../Common/M2Share.h"
#include <algorithm>
#include <iostream>
#include <sstream>

EventManager::EventManager() 
    : m_nextEventId(1)
    , m_totalEvents(0)
    , m_processedEvents(0)
    , m_activeEvents(0)
    , m_managerName("EventManager")
    , m_initialized(false) {
}

EventManager::~EventManager() {
    Finalize();
}

bool EventManager::Initialize() {
    std::cout << "[EventManager] Initializing..." << std::endl;
    
    std::lock_guard<std::mutex> lock(m_eventMutex);
    
    // 清理现有事件
    m_events.clear();
    m_eventMap.clear();
    
    // 重置统计信息
    m_totalEvents = 0;
    m_processedEvents = 0;
    m_activeEvents = 0;
    m_nextEventId = 1;
    
    m_initialized = true;
    
    std::cout << "[EventManager] Initialized successfully" << std::endl;
    return true;
}

void EventManager::Finalize() {
    if (!m_initialized) return;
    
    std::cout << "[EventManager] Finalizing..." << std::endl;
    
    // 清理所有事件
    ClearAllEvents();
    
    // 清理事件处理器
    {
        std::lock_guard<std::mutex> lock(m_handlerMutex);
        m_eventHandlers.clear();
    }
    
    m_initialized = false;
    
    std::cout << "[EventManager] Finalized" << std::endl;
}

void EventManager::Update() {
    if (!m_initialized) return;
    
    ProcessEvents();
    CleanupExpiredEvents();
}

const std::string& EventManager::GetManagerName() const {
    return m_managerName;
}

uint32_t EventManager::CreateEvent(GameEventType type, const Point& position, void* envir,
                                  DWORD duration, DWORD interval, int repeatCount) {
    std::lock_guard<std::mutex> lock(m_eventMutex);
    
    auto event = std::make_unique<GameEvent>();
    event->eventId = GenerateEventId();
    event->type = type;
    event->position = position;
    event->envir = envir;
    event->startTime = GetCurrentTime();
    event->duration = duration;
    event->interval = interval;
    event->repeatCount = repeatCount;
    event->currentCount = 0;
    event->active = false;
    event->persistent = false;
    
    uint32_t eventId = event->eventId;
    
    // 添加到容器
    m_eventMap[eventId] = std::move(event);
    
    ++m_totalEvents;
    
    std::cout << "[EventManager] Created event " << eventId << " of type " << static_cast<int>(type) << std::endl;
    
    return eventId;
}

uint32_t EventManager::CreateTimerEvent(DWORD delay, std::function<void()> callback, 
                                       int repeatCount, DWORD interval) {
    std::lock_guard<std::mutex> lock(m_eventMutex);
    
    auto event = std::make_unique<GameEvent>();
    event->eventId = GenerateEventId();
    event->type = GameEventType::TIMER_EVENT;
    event->startTime = GetCurrentTime() + delay;
    event->duration = 0;
    event->interval = interval;
    event->repeatCount = repeatCount;
    event->currentCount = 0;
    event->active = true;
    event->persistent = false;
    event->callback = std::move(callback);
    
    uint32_t eventId = event->eventId;
    
    // 添加到容器
    m_eventMap[eventId] = std::move(event);
    
    ++m_totalEvents;
    ++m_activeEvents;
    
    std::cout << "[EventManager] Created timer event " << eventId << " with delay " << delay << "ms" << std::endl;
    
    return eventId;
}

uint32_t EventManager::CreateMapEvent(void* envir, const Point& position,
                                     const std::string& eventName, const std::string& eventData) {
    std::lock_guard<std::mutex> lock(m_eventMutex);
    
    auto event = std::make_unique<GameEvent>();
    event->eventId = GenerateEventId();
    event->type = GameEventType::MAP_EVENT;
    event->position = position;
    event->envir = envir;
    event->startTime = GetCurrentTime();
    event->duration = 0;
    event->interval = 0;
    event->repeatCount = -1; // 永久事件
    event->currentCount = 0;
    event->active = true;
    event->persistent = true;
    event->eventName = eventName;
    event->eventData = eventData;
    
    uint32_t eventId = event->eventId;
    
    // 添加到容器
    m_eventMap[eventId] = std::move(event);
    
    ++m_totalEvents;
    ++m_activeEvents;
    
    std::cout << "[EventManager] Created map event " << eventId << " '" << eventName << "' at (" 
              << position.x << "," << position.y << ")" << std::endl;
    
    return eventId;
}

bool EventManager::StartEvent(uint32_t eventId) {
    std::lock_guard<std::mutex> lock(m_eventMutex);
    
    auto it = m_eventMap.find(eventId);
    if (it == m_eventMap.end()) {
        return false;
    }
    
    auto& event = it->second;
    if (!event->active) {
        event->active = true;
        event->startTime = GetCurrentTime();
        ++m_activeEvents;
        
        std::cout << "[EventManager] Started event " << eventId << std::endl;
    }
    
    return true;
}

bool EventManager::StopEvent(uint32_t eventId) {
    std::lock_guard<std::mutex> lock(m_eventMutex);
    
    auto it = m_eventMap.find(eventId);
    if (it == m_eventMap.end()) {
        return false;
    }
    
    auto& event = it->second;
    if (event->active) {
        event->active = false;
        --m_activeEvents;
        
        std::cout << "[EventManager] Stopped event " << eventId << std::endl;
    }
    
    return true;
}

bool EventManager::RemoveEvent(uint32_t eventId) {
    std::lock_guard<std::mutex> lock(m_eventMutex);
    
    auto it = m_eventMap.find(eventId);
    if (it == m_eventMap.end()) {
        return false;
    }
    
    if (it->second->active) {
        --m_activeEvents;
    }
    
    m_eventMap.erase(it);
    
    std::cout << "[EventManager] Removed event " << eventId << std::endl;
    return true;
}

GameEvent* EventManager::GetEvent(uint32_t eventId) {
    std::lock_guard<std::mutex> lock(m_eventMutex);
    
    auto it = m_eventMap.find(eventId);
    return (it != m_eventMap.end()) ? it->second.get() : nullptr;
}

void EventManager::RegisterEventHandler(GameEventType type, std::function<void(const GameEvent&)> handler) {
    std::lock_guard<std::mutex> lock(m_handlerMutex);
    m_eventHandlers[type].push_back(std::move(handler));
    
    std::cout << "[EventManager] Registered handler for event type " << static_cast<int>(type) << std::endl;
}

void EventManager::ClearAllEvents() {
    std::lock_guard<std::mutex> lock(m_eventMutex);
    
    size_t eventCount = m_eventMap.size();
    m_eventMap.clear();
    m_activeEvents = 0;
    
    std::cout << "[EventManager] Cleared " << eventCount << " events" << std::endl;
}

size_t EventManager::GetEventCount() const {
    std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(m_eventMutex));
    return m_eventMap.size();
}

void EventManager::ProcessEvents() {
    std::lock_guard<std::mutex> lock(m_eventMutex);
    
    DWORD currentTime = GetCurrentTime();
    
    for (auto& pair : m_eventMap) {
        auto& event = pair.second;
        
        if (!event->active) continue;
        
        if (ShouldEventTrigger(*event)) {
            ProcessEvent(*event);
        }
        
        UpdateEventState(*event);
    }
}

void EventManager::ProcessEvent(GameEvent& event) {
    // 执行回调函数
    if (event.callback) {
        try {
            event.callback();
        } catch (const std::exception& e) {
            std::cerr << "[EventManager] Error executing event " << event.eventId << " callback: " << e.what() << std::endl;
        }
    }
    
    // 触发事件处理器
    TriggerEventHandlers(event);
    
    // 更新计数
    ++event.currentCount;
    ++m_processedEvents;
}

void EventManager::TriggerEventHandlers(const GameEvent& event) {
    std::lock_guard<std::mutex> lock(m_handlerMutex);
    
    auto it = m_eventHandlers.find(event.type);
    if (it != m_eventHandlers.end()) {
        for (const auto& handler : it->second) {
            try {
                handler(event);
            } catch (const std::exception& e) {
                std::cerr << "[EventManager] Error in event handler for type " 
                          << static_cast<int>(event.type) << ": " << e.what() << std::endl;
            }
        }
    }
}

bool EventManager::ShouldEventTrigger(const GameEvent& event) const {
    DWORD currentTime = GetCurrentTime();
    
    // 检查是否到达开始时间
    if (currentTime < event.startTime) {
        return false;
    }
    
    // 检查重复次数
    if (event.repeatCount > 0 && event.currentCount >= event.repeatCount) {
        return false;
    }
    
    // 检查间隔时间
    if (event.interval > 0 && event.currentCount > 0) {
        DWORD expectedNextTime = event.startTime + (event.currentCount * event.interval);
        if (currentTime < expectedNextTime) {
            return false;
        }
    }
    
    return true;
}

void EventManager::UpdateEventState(GameEvent& event) {
    // 检查事件是否应该停止
    if (event.repeatCount > 0 && event.currentCount >= event.repeatCount) {
        if (event.active) {
            event.active = false;
            --m_activeEvents;
        }
    }
}

bool EventManager::IsEventExpired(const GameEvent& event) const {
    // 持久化事件不会过期
    if (event.persistent) {
        return false;
    }
    
    // 检查是否完成所有重复
    if (event.repeatCount > 0 && event.currentCount >= event.repeatCount) {
        return true;
    }
    
    // 检查持续时间
    if (event.duration > 0) {
        DWORD currentTime = GetCurrentTime();
        return (currentTime >= event.startTime + event.duration);
    }
    
    return false;
}

void EventManager::CleanupExpiredEvents() {
    std::lock_guard<std::mutex> lock(m_eventMutex);
    
    auto it = m_eventMap.begin();
    while (it != m_eventMap.end()) {
        if (IsEventExpired(*it->second)) {
            if (it->second->active) {
                --m_activeEvents;
            }
            it = m_eventMap.erase(it);
        } else {
            ++it;
        }
    }
}

uint32_t EventManager::GenerateEventId() {
    return m_nextEventId.fetch_add(1);
}

void EventManager::ValidateEvent(const GameEvent& event) const {
    // 基本验证逻辑
    if (event.eventId == 0) {
        throw std::invalid_argument("Invalid event ID");
    }
    
    if (event.repeatCount < -1) {
        throw std::invalid_argument("Invalid repeat count");
    }
}
