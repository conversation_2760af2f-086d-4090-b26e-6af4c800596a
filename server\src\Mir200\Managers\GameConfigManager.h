#pragma once

#include "IManager.h"
#include "../Common/Types.h"
#include <string>
#include <unordered_map>
#include <mutex>
#include <fstream>
#include <iostream>

/**
 * @brief 服务器配置结构
 */
struct ServerConfig {
    std::string serverName;
    std::string version;
    std::string versionDate;
    int maxUserCount;
    int testUserLimit;
    int startPermission;
    bool testServer;
    bool serviceMode;
    bool ventureMode;
    bool nonPKMode;
    
    ServerConfig() : maxUserCount(1000), testUserLimit(10), startPermission(0),
                    testServer(false), serviceMode(false), ventureMode(false), nonPKMode(false) {}
};

/**
 * @brief 游戏规则配置结构
 */
struct GameRuleConfig {
    // 速度相关
    int hitIntervalTime;
    int magicHitIntervalTime;
    int runIntervalTime;
    int walkIntervalTime;
    int turnIntervalTime;
    int digUpIntervalTime;
    int itemSpeedTime;
    
    // 消息限制
    int maxHitMsgCount;
    int maxSpellMsgCount;
    int maxRunMsgCount;
    int maxWalkMsgCount;
    int maxTurnMsgCount;
    int maxDigUpMsgCount;
    
    // PK相关
    int decPkPointTime;
    int decPkPointCount;
    int pkFlagTime;
    int killHumanAddPKPoint;
    int pkProtectLevel;
    int redPKProtectLevel;
    bool pkLevelProtect;
    
    // 经验相关
    int killMonExpMultiple;
    bool highLevelKillMonFixExp;
    bool fixExp;
    int baseExp;
    int addExp;
    bool highLevelGroupFixExp;
    int limitExpLevel;
    int limitExpValue;
    
    GameRuleConfig() : hitIntervalTime(900), magicHitIntervalTime(800), runIntervalTime(600),
                      walkIntervalTime(600), turnIntervalTime(300), digUpIntervalTime(300),
                      itemSpeedTime(60), maxHitMsgCount(2), maxSpellMsgCount(2), maxRunMsgCount(4),
                      maxWalkMsgCount(4), maxTurnMsgCount(10), maxDigUpMsgCount(2),
                      decPkPointTime(300), decPkPointCount(1), pkFlagTime(300000),
                      killHumanAddPKPoint(100), pkProtectLevel(0), redPKProtectLevel(0),
                      pkLevelProtect(false), killMonExpMultiple(10), highLevelKillMonFixExp(false),
                      fixExp(false), baseExp(100), addExp(0), highLevelGroupFixExp(false),
                      limitExpLevel(0), limitExpValue(0) {}
};

/**
 * @brief 倍率配置结构
 */
struct RateConfig {
    int monsterPowerRate;
    int itemsPowerRate;
    int itemsACPowerRate;
    int superRepairPriceRate;
    int repairItemDecDura;
    
    RateConfig() : monsterPowerRate(10), itemsPowerRate(10), itemsACPowerRate(10),
                  superRepairPriceRate(3), repairItemDecDura(5) {}
};

/**
 * @brief 地图配置结构
 */
struct MapConfig {
    int safeZoneSize;
    int startPointSize;
    std::string homeMap;
    int homeX;
    int homeY;
    std::string redHomeMap;
    int redHomeX;
    int redHomeY;
    std::string redDieHomeMap;
    int redDieHomeX;
    int redDieHomeY;
    
    MapConfig() : safeZoneSize(10), startPointSize(2), homeMap("3"),
                 homeX(330), homeY(330), redHomeMap("3"), redHomeX(330), redHomeY(330),
                 redDieHomeMap("3"), redDieHomeX(330), redDieHomeY(330) {}
};

/**
 * @brief 游戏配置管理器
 * 负责统一管理所有游戏配置
 */
class GameConfigManager : public IManager {
private:
    ServerConfig m_serverConfig;
    GameRuleConfig m_gameRuleConfig;
    RateConfig m_rateConfig;
    MapConfig m_mapConfig;
    
    std::unordered_map<std::string, std::string> m_customConfigs;
    mutable std::mutex m_configMutex;
    
    std::string m_configPath;
    std::string m_managerName;
    bool m_initialized;

public:
    GameConfigManager();
    virtual ~GameConfigManager();
    
    // IManager接口实现
    bool Initialize() override;
    void Finalize() override;
    void Update() override;
    const std::string& GetManagerName() const override;
    
    // 配置文件操作
    bool LoadConfig(const std::string& configPath);
    bool SaveConfig(const std::string& configPath = "");
    void ReloadConfig();
    
    // 配置获取接口
    const ServerConfig& GetServerConfig() const;
    const GameRuleConfig& GetGameRuleConfig() const;
    const RateConfig& GetRateConfig() const;
    const MapConfig& GetMapConfig() const;
    
    // 配置设置接口
    void SetServerConfig(const ServerConfig& config);
    void SetGameRuleConfig(const GameRuleConfig& config);
    void SetRateConfig(const RateConfig& config);
    void SetMapConfig(const MapConfig& config);
    
    // 自定义配置
    std::string GetCustomConfig(const std::string& key, const std::string& defaultValue = "") const;
    void SetCustomConfig(const std::string& key, const std::string& value);
    
    // 配置验证
    bool ValidateConfig() const;
    bool IsInitialized() const;

private:
    bool LoadServerConfig(const std::string& configFile);
    bool LoadGameRuleConfig(const std::string& configFile);
    bool LoadRateConfig(const std::string& configFile);
    bool LoadMapConfig(const std::string& configFile);
    
    bool SaveServerConfig(const std::string& configFile) const;
    bool SaveGameRuleConfig(const std::string& configFile) const;
    bool SaveRateConfig(const std::string& configFile) const;
    bool SaveMapConfig(const std::string& configFile) const;
    
    void SetDefaultConfigs();
    std::string GetConfigFilePath(const std::string& configName) const;
};
