#pragma once

#include "IManager.h"
#include "EventData.h"
#include "../Common/Types.h"
#include <vector>
#include <memory>
#include <mutex>
#include <unordered_map>
#include <functional>
#include <atomic>

// Forward declarations
class BaseObject;

/**
 * @brief 游戏事件类型枚举
 * 基于原项目Event.pas中的事件类型
 */
enum class GameEventType {
    NONE = 0,
    TIMER_EVENT = 1,        // 定时器事件
    MONSTER_SPAWN = 2,      // 怪物刷新事件
    ITEM_SPAWN = 3,         // 物品刷新事件
    MAP_EVENT = 4,          // 地图事件
    QUEST_EVENT = 5,        // 任务事件
    SYSTEM_EVENT = 6,       // 系统事件
    PLAYER_EVENT = 7,       // 玩家事件
    GUILD_EVENT = 8,        // 行会事件
    CASTLE_EVENT = 9,       // 城堡事件
    MINE_EVENT = 10         // 挖矿事件
};

/**
 * @brief 游戏事件结构
 * 基于原项目Event.pas中的TEvent结构
 */
struct GameEvent {
    uint32_t eventId;           // 事件ID
    GameEventType type;         // 事件类型
    Point position;             // 事件位置
    void* envir;                // 所属环境（使用void*避免循环依赖）
    DWORD startTime;           // 开始时间
    DWORD duration;            // 持续时间
    DWORD interval;            // 间隔时间
    int repeatCount;           // 重复次数
    int currentCount;          // 当前次数
    bool active;               // 是否激活
    bool persistent;           // 是否持久化
    std::string eventName;     // 事件名称
    std::string eventData;     // 事件数据
    std::function<void()> callback;  // 回调函数
    
    GameEvent() 
        : eventId(0), type(GameEventType::NONE), envir(nullptr)
        , startTime(0), duration(0), interval(0)
        , repeatCount(1), currentCount(0)
        , active(false), persistent(false) {
        position.x = 0;
        position.y = 0;
    }
};

/**
 * @brief 事件管理器
 * 基于原项目Event.pas的功能，管理游戏内所有事件
 * 负责事件的创建、调度、执行和清理
 */
class EventManager : public IManager {
private:
    // 事件存储
    std::vector<std::unique_ptr<GameEvent>> m_events;
    std::unordered_map<uint32_t, std::unique_ptr<GameEvent>> m_eventMap;
    std::mutex m_eventMutex;
    
    // 事件ID生成器
    std::atomic<uint32_t> m_nextEventId;
    
    // 统计信息
    std::atomic<uint64_t> m_totalEvents;
    std::atomic<uint64_t> m_processedEvents;
    std::atomic<uint64_t> m_activeEvents;
    
    // 管理器状态
    std::string m_managerName;
    bool m_initialized;
    
    // 事件处理器映射
    std::unordered_map<GameEventType, std::vector<std::function<void(const GameEvent&)>>> m_eventHandlers;
    std::mutex m_handlerMutex;

public:
    EventManager();
    virtual ~EventManager();
    
    // IManager接口实现
    bool Initialize() override;
    void Finalize() override;
    void Update() override;
    const std::string& GetManagerName() const override;
    
    // 事件创建和管理
    uint32_t CreateEvent(GameEventType type, const Point& position, void* envir,
                        DWORD duration = 0, DWORD interval = 0, int repeatCount = 1);
    uint32_t CreateTimerEvent(DWORD delay, std::function<void()> callback,
                             int repeatCount = 1, DWORD interval = 0);
    uint32_t CreateMapEvent(void* envir, const Point& position,
                           const std::string& eventName, const std::string& eventData);
    
    // 事件控制
    bool StartEvent(uint32_t eventId);
    bool StopEvent(uint32_t eventId);
    bool PauseEvent(uint32_t eventId);
    bool ResumeEvent(uint32_t eventId);
    bool RemoveEvent(uint32_t eventId);
    
    // 事件查询
    GameEvent* GetEvent(uint32_t eventId);
    std::vector<GameEvent*> GetEventsByType(GameEventType type);
    std::vector<GameEvent*> GetEventsByEnvironment(void* envir);
    std::vector<GameEvent*> GetEventsByPosition(const Point& position, int range = 0);
    
    // 事件处理器注册
    void RegisterEventHandler(GameEventType type, std::function<void(const GameEvent&)> handler);
    void UnregisterEventHandlers(GameEventType type);
    
    // 批量操作
    void RemoveEventsByEnvironment(void* envir);
    void RemoveEventsByType(GameEventType type);
    void PauseAllEvents();
    void ResumeAllEvents();
    void ClearAllEvents();
    
    // 统计信息
    uint64_t GetTotalEvents() const { return m_totalEvents; }
    uint64_t GetProcessedEvents() const { return m_processedEvents; }
    uint64_t GetActiveEvents() const { return m_activeEvents; }
    size_t GetEventCount() const;
    
    // 调试和监控
    std::string GetEventInfo(uint32_t eventId) const;
    std::vector<std::string> GetAllEventInfo() const;
    void DumpEventStatistics() const;

private:
    // 内部事件处理
    void ProcessEvents();
    void ProcessEvent(GameEvent& event);
    void TriggerEventHandlers(const GameEvent& event);
    
    // 事件生命周期管理
    bool IsEventExpired(const GameEvent& event) const;
    bool ShouldEventTrigger(const GameEvent& event) const;
    void UpdateEventState(GameEvent& event);
    
    // 工具方法
    uint32_t GenerateEventId();
    void CleanupExpiredEvents();
    void ValidateEvent(const GameEvent& event) const;
};
