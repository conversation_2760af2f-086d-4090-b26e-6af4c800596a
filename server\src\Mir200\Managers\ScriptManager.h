#pragma once

#include "IManager.h"
#include "../Common/Types.h"
#include <string>
#include <vector>
#include <memory>
#include <mutex>
#include <unordered_map>
#include <functional>
#include <atomic>

// Forward declarations
class PlayObject;
class BaseObject;

/**
 * @brief 脚本类型枚举
 * 基于原项目QuestNPC.pas中的脚本类型
 */
enum class ScriptType {
    NPC_SCRIPT = 1,         // NPC对话脚本
    QUEST_SCRIPT = 2,       // 任务脚本
    SYSTEM_SCRIPT = 3,      // 系统脚本
    EVENT_SCRIPT = 4,       // 事件脚本
    MERCHANT_SCRIPT = 5     // 商人脚本
};

/**
 * @brief 脚本条件类型
 * 基于原项目ScriptEngine中的条件检查
 */
enum class ScriptConditionType {
    CHECK_LEVEL = 1,        // 检查等级
    CHECK_JOB = 2,          // 检查职业
    CHECK_GOLD = 3,         // 检查金币
    CHECK_ITEM = 4,         // 检查物品
    CHECK_VAR = 5,          // 检查变量
    CHECK_FLAG = 6,         // 检查标志
    CHECK_TIME = 7,         // 检查时间
    CHECK_MAP = 8,          // 检查地图
    CHECK_GUILD = 9,        // 检查行会
    CHECK_PK = 10           // 检查PK值
};

/**
 * @brief 脚本动作类型
 * 基于原项目ScriptEngine中的动作执行
 */
enum class ScriptActionType {
    GIVE_ITEM = 1,          // 给予物品
    TAKE_ITEM = 2,          // 取走物品
    GIVE_GOLD = 3,          // 给予金币
    TAKE_GOLD = 4,          // 取走金币
    GIVE_EXP = 5,           // 给予经验
    SEND_MSG = 6,           // 发送消息
    MAP_MOVE = 7,           // 地图传送
    SET_VAR = 8,            // 设置变量
    CALC_VAR = 9,           // 计算变量
    OPEN_MERCHANT = 10,     // 打开商店
    OPEN_STORAGE = 11,      // 打开仓库
    CLOSE_WINDOW = 12,      // 关闭窗口
    GOTO_LABEL = 13,        // 跳转标签
    SYSTEM_MSG = 14,        // 系统消息
    BROADCAST = 15          // 广播消息
};

/**
 * @brief 脚本条件结构
 */
struct ScriptCondition {
    ScriptConditionType type;       // 条件类型
    std::string param1;             // 参数1
    std::string param2;             // 参数2
    std::string param3;             // 参数3
    std::string operator_str;       // 操作符 (>, <, =, >=, <=, !=)
    
    ScriptCondition() : type(ScriptConditionType::CHECK_LEVEL) {}
};

/**
 * @brief 脚本动作结构
 */
struct ScriptAction {
    ScriptActionType type;          // 动作类型
    std::string param1;             // 参数1
    std::string param2;             // 参数2
    std::string param3;             // 参数3
    std::string param4;             // 参数4
    
    ScriptAction() : type(ScriptActionType::GIVE_ITEM) {}
};

/**
 * @brief 脚本选项结构
 */
struct ScriptOption {
    std::string text;               // 选项文本
    std::string gotoLabel;          // 跳转标签
    std::vector<ScriptCondition> conditions; // 显示条件
    
    ScriptOption() = default;
};

/**
 * @brief 脚本块结构
 * 基于原项目的脚本块格式
 */
struct ScriptBlock {
    std::string label;              // 标签名
    std::string text;               // 对话文本
    std::vector<ScriptCondition> conditions;    // 条件列表
    std::vector<ScriptAction> actions;          // 动作列表
    std::vector<ScriptAction> elseActions;      // ELSE动作列表
    std::vector<ScriptOption> options;          // 选项列表
    
    ScriptBlock() = default;
};

/**
 * @brief 脚本文件结构
 */
struct ScriptFile {
    std::string fileName;           // 文件名
    ScriptType type;                // 脚本类型
    std::unordered_map<std::string, std::unique_ptr<ScriptBlock>> blocks; // 脚本块映射
    DWORD lastModified;             // 最后修改时间
    bool loaded;                    // 是否已加载
    
    ScriptFile() : type(ScriptType::NPC_SCRIPT), lastModified(0), loaded(false) {}
    
    const ScriptBlock* GetBlock(const std::string& label) const {
        auto it = blocks.find(label);
        return (it != blocks.end()) ? it->second.get() : nullptr;
    }
};

/**
 * @brief 脚本执行上下文
 */
struct ScriptContext {
    PlayObject* player;             // 玩家对象
    BaseObject* npc;                // NPC对象
    std::string currentLabel;       // 当前标签
    std::unordered_map<std::string, int> variables; // 脚本变量
    bool finished;                  // 是否完成
    
    ScriptContext() : player(nullptr), npc(nullptr), finished(false) {}
};

/**
 * @brief 脚本管理器
 * 基于原项目QuestNPC.pas和ScriptEngine的功能
 * 负责脚本的加载、解析、执行和管理
 */
class ScriptManager : public IManager {
private:
    // 脚本存储
    std::unordered_map<std::string, std::unique_ptr<ScriptFile>> m_scripts;
    std::mutex m_scriptMutex;
    
    // 脚本变量存储
    std::unordered_map<std::string, std::unordered_map<std::string, int>> m_globalVariables;
    std::mutex m_variableMutex;
    
    // 脚本执行上下文
    std::unordered_map<uint32_t, std::unique_ptr<ScriptContext>> m_contexts;
    std::mutex m_contextMutex;
    std::atomic<uint32_t> m_nextContextId;
    
    // 条件检查器和动作执行器
    std::unordered_map<ScriptConditionType, std::function<bool(const ScriptCondition&, ScriptContext&)>> m_conditionCheckers;
    std::unordered_map<ScriptActionType, std::function<bool(const ScriptAction&, ScriptContext&)>> m_actionExecutors;
    std::mutex m_handlerMutex;
    
    // 统计信息
    std::atomic<uint64_t> m_totalScripts;
    std::atomic<uint64_t> m_loadedScripts;
    std::atomic<uint64_t> m_executedActions;
    std::atomic<uint64_t> m_failedActions;
    
    // 配置参数
    std::string m_scriptDirectory;
    bool m_autoReload;
    DWORD m_reloadInterval;
    DWORD m_lastReloadCheck;
    
    // 管理器状态
    std::string m_managerName;
    bool m_initialized;

public:
    ScriptManager();
    virtual ~ScriptManager();
    
    // IManager接口实现
    bool Initialize() override;
    void Finalize() override;
    void Update() override;
    const std::string& GetManagerName() const override;
    
    // 脚本加载和管理
    bool LoadScript(const std::string& fileName, ScriptType type = ScriptType::NPC_SCRIPT);
    bool ReloadScript(const std::string& fileName);
    bool UnloadScript(const std::string& fileName);
    void ReloadAllScripts();
    
    // 脚本执行
    uint32_t StartScript(const std::string& scriptName, const std::string& label, 
                        PlayObject* player, BaseObject* npc = nullptr);
    bool ContinueScript(uint32_t contextId, const std::string& gotoLabel = "");
    bool ExecuteScriptBlock(uint32_t contextId, const std::string& label);
    void EndScript(uint32_t contextId);
    
    // 脚本查询
    const ScriptFile* GetScript(const std::string& fileName) const;
    const ScriptBlock* GetScriptBlock(const std::string& fileName, const std::string& label) const;
    bool IsScriptLoaded(const std::string& fileName) const;
    
    // 变量管理
    void SetGlobalVariable(const std::string& varName, int value);
    int GetGlobalVariable(const std::string& varName) const;
    void SetPlayerVariable(PlayObject* player, const std::string& varName, int value);
    int GetPlayerVariable(PlayObject* player, const std::string& varName) const;
    
    // 条件检查和动作执行注册
    void RegisterConditionChecker(ScriptConditionType type, 
                                 std::function<bool(const ScriptCondition&, ScriptContext&)> checker);
    void RegisterActionExecutor(ScriptActionType type, 
                               std::function<bool(const ScriptAction&, ScriptContext&)> executor);
    
    // 配置管理
    void SetScriptDirectory(const std::string& directory) { m_scriptDirectory = directory; }
    void SetAutoReload(bool autoReload, DWORD interval = 60000) { 
        m_autoReload = autoReload; 
        m_reloadInterval = interval; 
    }
    
    // 统计信息
    uint64_t GetTotalScripts() const { return m_totalScripts; }
    uint64_t GetLoadedScripts() const { return m_loadedScripts; }
    uint64_t GetExecutedActions() const { return m_executedActions; }
    uint64_t GetFailedActions() const { return m_failedActions; }
    double GetActionSuccessRate() const;
    
    // 调试和监控
    std::vector<std::string> GetLoadedScriptNames() const;
    std::string GetScriptInfo(const std::string& fileName) const;
    void DumpScriptStatistics() const;

private:
    // 脚本解析
    bool ParseScriptFile(const std::string& filePath, ScriptFile& script);
    bool ParseScriptBlock(const std::vector<std::string>& lines, size_t& lineIndex, ScriptBlock& block);
    bool ParseCondition(const std::string& line, ScriptCondition& condition);
    bool ParseAction(const std::string& line, ScriptAction& action);
    bool ParseOption(const std::string& line, ScriptOption& option);
    
    // 脚本执行
    bool CheckConditions(const std::vector<ScriptCondition>& conditions, ScriptContext& context);
    bool ExecuteActions(const std::vector<ScriptAction>& actions, ScriptContext& context);
    bool CheckCondition(const ScriptCondition& condition, ScriptContext& context);
    bool ExecuteAction(const ScriptAction& action, ScriptContext& context);
    
    // 内置条件检查器
    void RegisterBuiltinConditionCheckers();
    void RegisterBuiltinActionExecutors();
    
    // 工具方法
    uint32_t GenerateContextId();
    std::string ReplaceVariables(const std::string& text, ScriptContext& context);
    std::vector<std::string> SplitString(const std::string& str, char delimiter);
    std::string TrimString(const std::string& str);
    bool IsComment(const std::string& line);
    bool IsLabel(const std::string& line);
    std::string ExtractLabel(const std::string& line);
    
    // 文件操作
    bool LoadTextFile(const std::string& filePath, std::vector<std::string>& lines);
    DWORD GetFileModificationTime(const std::string& filePath);
    void CheckScriptReload();
};
