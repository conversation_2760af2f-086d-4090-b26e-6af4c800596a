#pragma once

#include "EventData.h"
#include <functional>
#include <unordered_map>
#include <vector>
#include <mutex>
#include <queue>
#include <thread>
#include <atomic>
#include <condition_variable>
#include <memory>

using EventHandler = std::function<void(const EventData&)>;

/**
 * @brief 事件总线
 * 负责Manager间的事件通信
 */
class EventBus {
private:
    // 同步事件处理器
    std::unordered_map<std::string, std::vector<EventHandler>> m_syncHandlers;
    std::mutex m_syncMutex;
    
    // 异步事件队列
    std::queue<std::pair<std::string, std::unique_ptr<EventData>>> m_asyncQueue;
    std::mutex m_asyncMutex;
    std::condition_variable m_asyncCondition;
    
    // 异步处理线程
    std::thread m_asyncThread;
    std::atomic<bool> m_running;
    
    // 统计信息
    std::atomic<uint64_t> m_totalEvents;
    std::atomic<uint64_t> m_processedEvents;
    size_t m_maxQueueSize;

public:
    EventBus();
    ~EventBus();
    
    // 生命周期管理
    bool Start();
    void Stop();
    bool IsRunning() const { return m_running; }
    
    // 事件订阅
    void Subscribe(const std::string& eventType, EventHandler handler);
    void Unsubscribe(const std::string& eventType);
    
    // 同步事件发布
    void Publish(const std::string& eventType, const EventData& data);
    
    // 异步事件发布
    void PublishAsync(const std::string& eventType, std::unique_ptr<EventData> data);
    
    // 批量处理异步事件
    void ProcessAsyncEvents();
    
    // 统计信息
    uint64_t GetTotalEvents() const { return m_totalEvents; }
    uint64_t GetProcessedEvents() const { return m_processedEvents; }
    size_t GetQueueSize() const;
    
    // 配置
    void SetMaxQueueSize(size_t maxSize) { m_maxQueueSize = maxSize; }

private:
    void AsyncEventLoop();
    void PublishToHandlers(const std::string& eventType, const EventData& data);
};
