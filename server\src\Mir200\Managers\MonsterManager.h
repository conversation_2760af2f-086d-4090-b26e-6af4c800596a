#pragma once

#include "IManager.h"
#include "EventData.h"
#include "../Common/Types.h"
#include <string>
#include <vector>
#include <unordered_map>
#include <mutex>
#include <shared_mutex>
#include <memory>

// 前向声明
class EventBus;
class Monster;
class PlayObject;

/**
 * @brief 怪物AI状态
 */
enum class MonsterAIState {
    IDLE = 0,       // 空闲
    PATROL,         // 巡逻
    CHASE,          // 追击
    ATTACK,         // 攻击
    FLEE,           // 逃跑
    RETURN,         // 返回
    DEAD            // 死亡
};

/**
 * @brief 怪物刷新配置
 */
struct MonsterSpawnConfig {
    std::string monsterName;    // 怪物名称
    std::string mapName;        // 地图名称
    int x, y;                   // 刷新坐标
    int range;                  // 刷新范围
    int count;                  // 刷新数量
    int maxCount;               // 最大数量
    int interval;               // 刷新间隔(秒)
    int level;                  // 怪物等级
    bool active;                // 是否激活
    
    MonsterSpawnConfig() : x(0), y(0), range(5), count(1), maxCount(10), 
                          interval(60), level(1), active(true) {}
};

/**
 * @brief 怪物AI配置
 */
struct MonsterAIConfig {
    int viewRange;              // 视野范围
    int chaseRange;             // 追击范围
    int attackRange;            // 攻击范围
    int patrolRange;            // 巡逻范围
    int fleeHealthPercent;      // 逃跑血量百分比
    int returnTime;             // 返回时间(秒)
    bool aggressive;            // 是否主动攻击
    bool canFlee;               // 是否可以逃跑
    
    MonsterAIConfig() : viewRange(5), chaseRange(10), attackRange(1), patrolRange(3),
                       fleeHealthPercent(10), returnTime(30), aggressive(true), canFlee(false) {}
};

/**
 * @brief 怪物管理器
 * 负责管理所有怪物相关功能
 * 对应原项目的怪物系统功能
 */
class MonsterManager : public IManager, public IMonsterProvider, public IEventSubscriber {
private:
    std::string m_managerName;
    bool m_initialized;
    
    // 依赖注入
    EventBus* m_eventBus;
    IPlayerProvider* m_playerProvider;
    
    // 怪物数据
    std::unordered_map<std::string, std::unique_ptr<Monster>> m_monsters;
    std::unordered_map<std::string, std::vector<MonsterSpawnConfig>> m_spawnConfigs;
    std::unordered_map<std::string, MonsterAIConfig> m_aiConfigs;
    std::unordered_map<std::string, std::vector<std::string>> m_mapMonsters; // 地图->怪物列表
    
    // 线程安全
    mutable std::shared_mutex m_monsterMutex;
    mutable std::shared_mutex m_spawnMutex;
    mutable std::shared_mutex m_aiMutex;
    mutable std::shared_mutex m_mapMutex;
    
    // 统计信息
    std::atomic<uint64_t> m_totalMonstersSpawned;
    std::atomic<uint64_t> m_totalMonstersKilled;
    std::atomic<uint64_t> m_totalAIUpdates;

public:
    MonsterManager();
    virtual ~MonsterManager();
    
    // IManager接口实现
    bool Initialize() override;
    void Finalize() override;
    void Update() override;
    const std::string& GetManagerName() const override;
    
    // 依赖注入
    void SetEventBus(EventBus* eventBus);
    void SetPlayerProvider(IPlayerProvider* provider);
    
    // IMonsterProvider接口实现
    const MonsterInfo* GetMonsterInfo(const std::string& monsterName) const override;
    Monster* CreateMonster(const std::string& monsterName, const std::string& mapName, int x, int y) override;
    int SpawnMonsters(const std::string& mapName) override;
    
    // IEventSubscriber接口实现
    void OnEvent(const std::string& eventType, const EventData& data) override;
    
    // 怪物管理功能
    bool LoadMonsterData();
    bool LoadSpawnConfigs(const std::string& configFile);
    bool LoadAIConfigs(const std::string& configFile);
    
    // 怪物操作
    bool SpawnMonster(const std::string& monsterName, const std::string& mapName, int x, int y, int level = 0);
    bool RemoveMonster(const std::string& monsterId);
    Monster* FindMonster(const std::string& monsterId);
    std::vector<Monster*> GetMonstersInMap(const std::string& mapName);
    std::vector<Monster*> GetMonstersInRange(const std::string& mapName, int x, int y, int range);
    
    // 怪物AI系统
    void UpdateMonsterAI();
    void UpdateMonsterAI(Monster* monster);
    MonsterAIState GetMonsterAIState(Monster* monster);
    void SetMonsterAIState(Monster* monster, MonsterAIState state);
    
    // 怪物战斗系统
    bool MonsterAttackPlayer(Monster* monster, PlayObject* player);
    bool PlayerAttackMonster(PlayObject* player, Monster* monster, int damage);
    void MonsterDeath(Monster* monster, PlayObject* killer);
    void ProcessMonsterCombat(Monster* monster);
    
    // 怪物刷新系统
    void ProcessSpawns();
    void ProcessMapSpawns(const std::string& mapName);
    bool ShouldSpawnMonster(const MonsterSpawnConfig& config);
    int GetMonsterCount(const std::string& mapName, const std::string& monsterName);
    
    // 怪物配置管理
    void AddSpawnConfig(const MonsterSpawnConfig& config);
    void RemoveSpawnConfig(const std::string& mapName, const std::string& monsterName);
    void UpdateSpawnConfig(const std::string& mapName, const std::string& monsterName, const MonsterSpawnConfig& config);
    std::vector<MonsterSpawnConfig> GetSpawnConfigs(const std::string& mapName);
    
    // 怪物AI配置
    void SetAIConfig(const std::string& monsterName, const MonsterAIConfig& config);
    MonsterAIConfig GetAIConfig(const std::string& monsterName);
    void UpdateAIConfig(const std::string& monsterName, const MonsterAIConfig& config);
    
    // 怪物寻路和移动
    bool MoveMonster(Monster* monster, int targetX, int targetY);
    std::vector<Point> FindPath(Monster* monster, int targetX, int targetY);
    Point GetRandomPatrolPosition(Monster* monster);
    bool CanMonsterMoveTo(Monster* monster, int x, int y);
    
    // 怪物目标系统
    PlayObject* FindNearestPlayer(Monster* monster);
    PlayObject* GetMonsterTarget(Monster* monster);
    void SetMonsterTarget(Monster* monster, PlayObject* target);
    void ClearMonsterTarget(Monster* monster);
    bool IsValidTarget(Monster* monster, PlayObject* target);
    
    // 统计信息
    uint64_t GetTotalMonstersSpawned() const { return m_totalMonstersSpawned; }
    uint64_t GetTotalMonstersKilled() const { return m_totalMonstersKilled; }
    uint64_t GetTotalAIUpdates() const { return m_totalAIUpdates; }
    
    // 地图相关
    void RegisterMonsterToMap(const std::string& mapName, const std::string& monsterId);
    void UnregisterMonsterFromMap(const std::string& mapName, const std::string& monsterId);
    std::vector<std::string> GetMapMonsterIds(const std::string& mapName) const;

private:
    // 内部辅助方法
    bool LoadSpawnConfigFromFile(const std::string& fileName);
    bool LoadAIConfigFromFile(const std::string& fileName);
    bool ParseSpawnConfigLine(const std::string& line, MonsterSpawnConfig& config);
    bool ParseAIConfigLine(const std::string& line, std::string& monsterName, MonsterAIConfig& config);
    
    // 怪物创建和销毁
    std::unique_ptr<Monster> CreateMonsterInstance(const std::string& monsterName, 
                                                  const std::string& mapName, int x, int y, int level);
    bool ValidateMonsterConfig(const std::string& monsterName) const;
    bool ValidateSpawnPosition(const std::string& mapName, int x, int y) const;
    std::string GenerateMonsterId();
    
    // AI逻辑实现
    void ProcessIdleState(Monster* monster);
    void ProcessPatrolState(Monster* monster);
    void ProcessChaseState(Monster* monster);
    void ProcessAttackState(Monster* monster);
    void ProcessFleeState(Monster* monster);
    void ProcessReturnState(Monster* monster);
    
    // 战斗逻辑
    int CalculateMonsterDamage(Monster* monster, PlayObject* target);
    bool CanMonsterAttack(Monster* monster, PlayObject* target);
    void ApplyMonsterAttackEffects(Monster* monster, PlayObject* target, int damage);
    void ProcessMonsterDeath(Monster* monster, PlayObject* killer);
    
    // 刷新逻辑
    Point FindSpawnPosition(const MonsterSpawnConfig& config);
    bool IsSpawnPositionValid(const std::string& mapName, int x, int y);
    void ScheduleRespawn(const MonsterSpawnConfig& config);
    
    // 事件处理
    void HandlePlayerLogin(const struct PlayerLoginEventData& data);
    void HandlePlayerMapChange(const struct MapChangeEventData& data);
    void HandleMonsterKilled(const struct MonsterKilledEventData& data);
    
    // 日志记录
    void LogMonsterOperation(const std::string& operation, const std::string& details) const;
    void LogMonsterError(const std::string& operation, const std::string& error) const;
};
