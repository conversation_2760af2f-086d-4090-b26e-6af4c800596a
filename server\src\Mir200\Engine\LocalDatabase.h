#pragma once

// Mir200 LocalDatabase - Local data management system
// Based on delphi/EM2Engine/LocalDB.pas - Following original project structure
// Phase 1 Implementation - Complete LocalDatabase implementation

#include "Common/M2Share.h"
#include "../../Database/IDatabase.h"
#include <memory>
#include <vector>
#include <string>
#include <list>
#include <mutex>
#include <shared_mutex>
#include <unordered_map>
#include <functional>

// Forward declarations
class UserEngine;
class TMerchant;
class TNormNpc;
struct MonGenInfo;

// Additional data structures based on original project
struct TGoodFileHeader {
    int item_count;
    std::array<int, 251> reserved;

    TGoodFileHeader() : item_count(0) {
        reserved.fill(0);
    }
};

struct MakeItemInfo {
    std::string item_name;
    std::vector<std::pair<std::string, int>> materials; // material name, count

    MakeItemInfo() = default;
};

struct MapEventInfo {
    std::string map_name;
    int curr_x;
    int curr_y;
    int range;
    int quest_unit;
    bool quest_open;
    int hum_status;
    std::string item_name;
    bool need_group;
    int random_count;
    int label_num;
    std::string label_name;

    MapEventInfo() : curr_x(0), curr_y(0), range(0), quest_unit(0),
                     quest_open(false), hum_status(0), need_group(false),
                     random_count(999999), label_num(0) {}
};

struct UpgradeWeaponInfo {
    std::string weapon_name;
    int upgrade_level;
    std::vector<std::string> materials;
    int success_rate;
    int gold_cost;

    UpgradeWeaponInfo() : upgrade_level(0), success_rate(0), gold_cost(0) {}
};

// Data structures based on original project
struct StartPointInfo {
    std::string map_name;
    int curr_x;
    int curr_y;
    bool not_allow_say;
    int range;
    int type;
    int pk_zone;
    int pk_fire;
    uint8_t shape;
};

struct UnbindItemInfo {
    int unbind_code;
    std::string item_name;
};

struct NPCInfo {
    std::string name;
    int type;
    std::string map_name;
    int x;
    int y;
    int flag;
    int appr;
    bool auto_change_color;
    uint32_t auto_change_color_time;
};

struct MinMapInfo {
    int index;
    std::string map_name;
    std::string file_name;
    bool enabled;
};

struct MapQuestInfo {
    std::string map_name;
    int flags;
    int flag;
    int value;
    bool flag_bool;
    std::string mon_name;
    std::string need_item;
    std::string script_name;
    bool group;
};

struct QuestDiaryInfo {
    int index;
    std::string title;
    std::vector<std::string> content;
};

// LocalDatabase class - Main local data management system
class LocalDatabase {
private:
    // Database state
    bool m_initialized;
    bool m_running;

    // Data paths
    std::string m_data_path;
    std::string m_envir_dir;

    // Database connection
    std::unique_ptr<MirServer::IDatabase> m_database;
    UserEngine* m_user_engine;

    // Thread safety
    mutable std::shared_mutex m_data_mutex;
    std::mutex m_operation_mutex;

    // Data containers
    std::vector<std::unique_ptr<StartPointInfo>> m_start_points;
    std::vector<std::unique_ptr<UnbindItemInfo>> m_unbind_items;
    std::vector<std::unique_ptr<MonGenInfo>> m_mon_gens;
    std::vector<std::unique_ptr<NPCInfo>> m_npcs;
    std::vector<std::unique_ptr<MinMapInfo>> m_min_maps;
    std::vector<std::unique_ptr<MapQuestInfo>> m_map_quests;
    std::vector<std::unique_ptr<QuestDiaryInfo>> m_quest_diaries;
    std::vector<std::unique_ptr<MakeItemInfo>> m_make_items;
    std::vector<std::unique_ptr<MapEventInfo>> m_map_events;

    // Index maps for fast lookup
    std::unordered_map<std::string, StartPointInfo*> m_start_point_map;
    std::unordered_map<std::string, NPCInfo*> m_npc_map;
    std::unordered_map<std::string, std::vector<MonGenInfo*>> m_mon_gen_map;
    std::unordered_map<std::string, MakeItemInfo*> m_make_item_map;
    std::unordered_map<std::string, std::vector<MapEventInfo*>> m_map_event_map;

    // Statistics
    struct Statistics {
        size_t start_point_count = 0;
        size_t unbind_item_count = 0;
        size_t mon_gen_count = 0;
        size_t npc_count = 0;
        size_t min_map_count = 0;
        size_t map_quest_count = 0;
        size_t quest_diary_count = 0;
        uint32_t last_update_time = 0;
    } m_statistics;

    // Callback for data loading events
    std::function<void(const std::string&, bool)> m_data_loaded_callback;

    // Helper methods for data parsing
    bool ParseStartPointData(const std::string& line, StartPointInfo& info);
    bool ParseUnbindItemData(const std::string& line, UnbindItemInfo& info);
    bool ParseMonGenData(const std::string& line, MonGenInfo& info);
    bool ParseNPCData(const std::string& line, NPCInfo& info);
    bool ParseMinMapData(const std::string& line, MinMapInfo& info);
    bool ParseMapQuestData(const std::string& line, MapQuestInfo& info);
    bool ParseQuestDiaryData(const std::string& line, QuestDiaryInfo& info);
    bool ParseMakeItemData(const std::string& line, MakeItemInfo& info);
    bool ParseMapEventData(const std::string& line, MapEventInfo& info);

    // File loading helpers
    bool LoadTextFile(const std::string& filename, std::vector<std::string>& lines);
    void UpdateStatistics();
    void NotifyDataLoaded(const std::string& data_type, bool success);

public:
    LocalDatabase();
    ~LocalDatabase();

    // Core lifecycle
    bool Initialize(std::unique_ptr<MirServer::IDatabase> database, UserEngine* user_engine);
    void Finalize();
    bool Start();
    void Stop();

    // Database data loading (from SQL database)
    bool LoadItemsDB();
    bool LoadMagicDB();
    bool LoadMonsterDB();

    // File data loading (from text files)
    bool LoadAdminList();
    bool LoadGuardList();
    bool LoadMerchant();
    bool LoadNpcs();
    bool LoadStartPoint();
    bool LoadMinMap();
    bool LoadMonGen();
    bool LoadUnbindList();
    bool LoadMapQuest();
    bool LoadQuestDiary();
    bool LoadMakeItem();
    bool LoadMapEvent();

    // Script loading
    int LoadNpcScript(TNormNpc* npc, const std::string& patch, const std::string& script_name);
    int LoadScriptFile(TNormNpc* npc, const std::string& patch, const std::string& script_name, bool flag);

    // Merchant data management
    int LoadGoodRecord(TMerchant* npc, const std::string& file);
    int LoadGoodPriceRecord(TMerchant* npc, const std::string& file);
    int SaveGoodRecord(TMerchant* npc, const std::string& file);
    int SaveGoodPriceRecord(TMerchant* npc, const std::string& file);

    // Weapon upgrade system
    int LoadUpgradeWeaponRecord(const std::string& npc_name, void* data_list);
    int SaveUpgradeWeaponRecord(const std::string& npc_name, void* data_list);

    // Reload operations
    void ReLoadMerchants();
    void ReLoadNpc();

    // Data processing
    void ProcessOperations();

    // Emergency operations
    void EmergencyStop();

    // Query interfaces
    const StartPointInfo* GetStartPointInfo(const std::string& map_name) const;
    const std::vector<std::unique_ptr<StartPointInfo>>& GetStartPoints() const;
    const NPCInfo* GetNPCInfo(const std::string& name) const;
    const std::vector<std::unique_ptr<MinMapInfo>>& GetMinMaps() const;
    const std::vector<std::unique_ptr<MapQuestInfo>>& GetMapQuests(const std::string& map_name = "") const;
    const std::vector<std::unique_ptr<QuestDiaryInfo>>& GetQuestDiaries() const;
    const std::vector<std::unique_ptr<UnbindItemInfo>>& GetUnbindItems() const;
    const std::vector<MonGenInfo*> GetMonGens(const std::string& map_name = "") const;
    const std::vector<std::unique_ptr<MakeItemInfo>>& GetMakeItems() const;
    const MakeItemInfo* GetMakeItemInfo(const std::string& item_name) const;
    const std::vector<std::unique_ptr<MapEventInfo>>& GetMapEvents() const;
    const std::vector<MapEventInfo*> GetMapEvents(const std::string& map_name) const;

    // Cache management
    void RefreshCache();
    void ClearCache();
    size_t GetCacheSize() const;

    // Statistics
    const Statistics& GetStatistics() const;

    // Configuration
    void SetDataPath(const std::string& path) { m_data_path = path; }
    const std::string& GetDataPath() const { return m_data_path; }
    void SetEnvirDir(const std::string& dir) { m_envir_dir = dir; }
    const std::string& GetEnvirDir() const { return m_envir_dir; }

    // Event callbacks
    void SetDataLoadedCallback(std::function<void(const std::string&, bool)> callback) {
        m_data_loaded_callback = callback;
    }

    // State management
    bool IsInitialized() const { return m_initialized; }
    bool IsRunning() const { return m_running; }
};
