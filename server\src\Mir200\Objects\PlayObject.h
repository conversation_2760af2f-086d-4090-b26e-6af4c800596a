#pragma once

#include "../Common/Types.h"
#include <memory>
#include <string>

// Forward declarations
class Environment;
class BaseObject;

// PlayObject class - simplified version for compilation
// Following original TPlayObject from ObjBase.pas
class PlayObject {
public:
    // Constructor and destructor
    PlayObject() = default;
    virtual ~PlayObject() = default;

    // Basic methods needed for UserEngine compilation
    virtual void SendMsg(std::shared_ptr<PlayObject> play_object, 
                        WORD ident, 
                        WORD series, 
                        int recog, 
                        WORD param, 
                        WORD tag, 
                        const std::string& msg) {
        // Placeholder implementation
    }

    virtual void SendUpdateMsg(std::shared_ptr<PlayObject> play_object, 
                              WORD ident, 
                              WORD tag, 
                              WORD lo_word, 
                              WORD hi_word, 
                              DWORD make_long, 
                              const std::string& msg) {
        // Placeholder implementation
    }

    virtual void SendActionMsg(std::shared_ptr<PlayObject> play_object, 
                              WORD ident, 
                              WORD tag, 
                              WORD lo_word, 
                              WORD hi_word, 
                              int param, 
                              const std::string& msg) {
        // Placeholder implementation
    }

    virtual bool IsReadyRun() const {
        return true; // Placeholder
    }

    virtual void AdjustRunTick(int adjustment) {
        // Placeholder implementation
    }

    // Basic properties
    virtual std::string GetCharName() const {
        return m_char_name;
    }

    virtual std::string GetMapName() const {
        return m_map_name;
    }

    virtual int GetCurrX() const {
        return m_curr_x;
    }

    virtual int GetCurrY() const {
        return m_curr_y;
    }

    virtual bool IsGhost() const {
        return m_is_ghost;
    }

    virtual void MakeGhost() {
        m_is_ghost = true;
    }

    virtual std::shared_ptr<Environment> GetPEnvir() const {
        return m_envir;
    }

    virtual bool IsNotOnlineAddExp() const {
        return false; // Placeholder
    }

    virtual bool IsSwitchData() const {
        return false; // Placeholder
    }

    virtual void SetSwitchData(bool value) {
        // Placeholder
    }

    // Basic member variables
protected:
    std::string m_char_name;
    std::string m_map_name;
    int m_curr_x = 0;
    int m_curr_y = 0;
    bool m_is_ghost = false;
    std::shared_ptr<Environment> m_envir;
};

// Compatibility aliases
using TPlayObject = PlayObject;
