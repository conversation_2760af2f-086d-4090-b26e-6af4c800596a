#pragma once

#include "IManager.h"
#include "../Common/Types.h"
#include <string>
#include <vector>
#include <memory>
#include <mutex>
#include <atomic>
#include <unordered_map>
#include <queue>
#include <thread>
#include <functional>

// Forward declarations
class RunSocket;

/**
 * @brief 网络连接类型枚举
 * 基于原项目RunSocket.pas和GateSocket.pas的连接类型
 */
enum class ConnectionType {
    UNKNOWN = 0,
    GAME_CLIENT = 1,        // 游戏客户端连接
    GATE_SERVER = 2,        // 网关服务器连接
    DB_SERVER = 3,          // 数据库服务器连接
    LOGIN_SERVER = 4,       // 登录服务器连接
    ADMIN_CLIENT = 5        // 管理客户端连接
};

/**
 * @brief 网络连接状态枚举
 */
enum class ConnectionState {
    DISCONNECTED = 0,       // 断开连接
    CONNECTING = 1,         // 正在连接
    CONNECTED = 2,          // 已连接
    AUTHENTICATED = 3,      // 已认证
    ERROR_STATE = 4         // 错误状态
};

/**
 * @brief 网络消息结构
 * 基于原项目的消息结构
 */
struct NetworkMessage {
    uint32_t messageId;         // 消息ID
    uint16_t messageType;       // 消息类型
    uint32_t connectionId;      // 连接ID
    std::vector<uint8_t> data;  // 消息数据
    DWORD timestamp;            // 时间戳
    bool processed;             // 是否已处理
    
    NetworkMessage() 
        : messageId(0), messageType(0), connectionId(0), timestamp(0), processed(false) {
    }
};

/**
 * @brief 网络连接信息
 * 基于原项目的连接管理结构
 */
struct ConnectionInfo {
    uint32_t connectionId;      // 连接ID
    ConnectionType type;        // 连接类型
    ConnectionState state;      // 连接状态
    std::string remoteAddress;  // 远程地址
    uint16_t remotePort;        // 远程端口
    DWORD connectTime;          // 连接时间
    DWORD lastActiveTime;       // 最后活动时间
    uint64_t bytesReceived;     // 接收字节数
    uint64_t bytesSent;         // 发送字节数
    uint32_t messagesReceived;  // 接收消息数
    uint32_t messagesSent;      // 发送消息数
    std::string userAccount;    // 用户账号
    std::string playerName;     // 玩家名称
    
    ConnectionInfo() 
        : connectionId(0), type(ConnectionType::UNKNOWN), state(ConnectionState::DISCONNECTED)
        , remotePort(0), connectTime(0), lastActiveTime(0)
        , bytesReceived(0), bytesSent(0), messagesReceived(0), messagesSent(0) {
    }
};

/**
 * @brief 网络统计信息
 */
struct NetworkStatistics {
    uint64_t totalConnections;      // 总连接数
    uint64_t activeConnections;     // 活动连接数
    uint64_t totalBytesReceived;    // 总接收字节数
    uint64_t totalBytesSent;        // 总发送字节数
    uint64_t totalMessagesReceived; // 总接收消息数
    uint64_t totalMessagesSent;     // 总发送消息数
    uint64_t connectionErrors;      // 连接错误数
    uint64_t messageErrors;         // 消息错误数
    double averageLatency;          // 平均延迟
    
    NetworkStatistics() 
        : totalConnections(0), activeConnections(0)
        , totalBytesReceived(0), totalBytesSent(0)
        , totalMessagesReceived(0), totalMessagesSent(0)
        , connectionErrors(0), messageErrors(0), averageLatency(0.0) {
    }
};

/**
 * @brief 网络管理器
 * 基于原项目RunSocket.pas和GateSocket.pas的功能
 * 统一管理所有网络连接和消息处理
 */
class NetworkManager : public IManager {
private:
    // 连接管理
    std::unordered_map<uint32_t, std::unique_ptr<ConnectionInfo>> m_connections;
    std::mutex m_connectionMutex;
    std::atomic<uint32_t> m_nextConnectionId;
    
    // 消息队列
    std::queue<std::unique_ptr<NetworkMessage>> m_incomingMessages;
    std::queue<std::unique_ptr<NetworkMessage>> m_outgoingMessages;
    std::mutex m_messageMutex;
    
    // 网络组件
    std::unique_ptr<RunSocket> m_runSocket;
    
    // 消息处理器
    std::unordered_map<uint16_t, std::function<void(const NetworkMessage&)>> m_messageHandlers;
    std::mutex m_handlerMutex;
    
    // 统计信息
    NetworkStatistics m_statistics;
    std::mutex m_statisticsMutex;
    
    // 配置参数
    std::string m_serverAddress;
    uint16_t m_serverPort;
    uint16_t m_gatePort;
    uint32_t m_maxConnections;
    uint32_t m_connectionTimeout;
    uint32_t m_messageTimeout;
    
    // 管理器状态
    std::string m_managerName;
    bool m_initialized;
    bool m_running;

public:
    NetworkManager();
    virtual ~NetworkManager();
    
    // IManager接口实现
    bool Initialize() override;
    void Finalize() override;
    void Update() override;
    const std::string& GetManagerName() const override;
    
    // 网络服务控制
    bool StartNetworkServices();
    void StopNetworkServices();
    bool IsNetworkRunning() const { return m_running; }
    
    // 连接管理
    uint32_t CreateConnection(ConnectionType type, const std::string& remoteAddress, uint16_t remotePort);
    bool CloseConnection(uint32_t connectionId);
    bool IsConnectionActive(uint32_t connectionId) const;
    ConnectionInfo* GetConnectionInfo(uint32_t connectionId);
    std::vector<ConnectionInfo*> GetConnectionsByType(ConnectionType type);
    std::vector<ConnectionInfo*> GetAllConnections();
    
    // 消息发送
    bool SendMessage(uint32_t connectionId, uint16_t messageType, const std::vector<uint8_t>& data);
    bool SendMessage(uint32_t connectionId, uint16_t messageType, const void* data, size_t size);
    bool BroadcastMessage(uint16_t messageType, const std::vector<uint8_t>& data, ConnectionType targetType = ConnectionType::GAME_CLIENT);
    bool BroadcastMessage(ConnectionType targetType, uint16_t messageType, const void* data, size_t size);
    
    // 消息处理
    void RegisterMessageHandler(uint16_t messageType, std::function<void(const NetworkMessage&)> handler);
    void UnregisterMessageHandler(uint16_t messageType);
    void ProcessIncomingMessages();
    void ProcessOutgoingMessages();
    
    // 连接状态管理
    void SetConnectionState(uint32_t connectionId, ConnectionState state);
    void SetConnectionUserInfo(uint32_t connectionId, const std::string& account, const std::string& playerName);
    void UpdateConnectionActivity(uint32_t connectionId);
    
    // 配置管理
    void SetServerAddress(const std::string& address) { m_serverAddress = address; }
    void SetServerPort(uint16_t port) { m_serverPort = port; }
    void SetGatePort(uint16_t port) { m_gatePort = port; }
    void SetMaxConnections(uint32_t maxConnections) { m_maxConnections = maxConnections; }
    void SetConnectionTimeout(uint32_t timeout) { m_connectionTimeout = timeout; }
    void SetMessageTimeout(uint32_t timeout) { m_messageTimeout = timeout; }
    
    // 统计信息
    NetworkStatistics GetStatistics() const;
    uint32_t GetActiveConnectionCount() const;
    uint32_t GetConnectionCount(ConnectionType type) const;
    std::vector<std::string> GetConnectionList() const;
    
    // 监控和调试
    void DumpConnectionInfo() const;
    void DumpNetworkStatistics() const;
    std::string GetNetworkStatus() const;
    
    // 安全和限制
    bool IsIPBlocked(const std::string& ipAddress) const;
    void BlockIP(const std::string& ipAddress, DWORD duration = 0);
    void UnblockIP(const std::string& ipAddress);
    bool CheckConnectionLimit(const std::string& ipAddress) const;

private:
    // 内部消息处理
    void HandleIncomingMessage(std::unique_ptr<NetworkMessage> message);
    void HandleOutgoingMessage(std::unique_ptr<NetworkMessage> message);
    void ProcessMessage(const NetworkMessage& message);
    
    // 连接生命周期
    void OnConnectionEstablished(uint32_t connectionId, const std::string& remoteAddress, uint16_t remotePort);
    void OnConnectionClosed(uint32_t connectionId);
    void OnConnectionError(uint32_t connectionId, const std::string& error);
    
    // 统计更新
    void UpdateStatistics(const NetworkMessage& message, bool incoming);
    void UpdateConnectionStatistics(uint32_t connectionId, size_t bytes, bool incoming);
    
    // 超时检查
    void CheckConnectionTimeouts();
    void CheckMessageTimeouts();
    
    // 工具方法
    uint32_t GenerateConnectionId();
    std::string GetConnectionTypeString(ConnectionType type) const;
    std::string GetConnectionStateString(ConnectionState state) const;
    void ValidateMessage(const NetworkMessage& message) const;
    
    // IP黑名单管理
    std::unordered_map<std::string, DWORD> m_blockedIPs;
    std::mutex m_ipBlockMutex;
};
