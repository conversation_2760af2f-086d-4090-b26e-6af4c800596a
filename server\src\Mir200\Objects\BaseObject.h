#pragma once

// Mir200 BaseObject - Core game object base class
// Based on delphi/EM2Engine/ObjBase.pas - Modularized following original structure
// This is the main interface, implementation is split across multiple modules

#include "../Common/M2Share.h"
#include "ObjectState.h"
#include "ObjectMovement.h"
#include "ObjectCombat.h"
#include "ObjectMagic.h"
#include "ObjectInventory.h"
#include "ObjectStatus.h"
#include "ObjectGroup.h"
#include "ObjectGuild.h"

// Forward declarations
class Environment;
class UserEngine;

// BaseObject class - Core game object (following original TBaseObject structure)
class BaseObject {
public:
    // Core object data (matching original ObjBase.pas structure exactly)
    MapName m_map_name;                     // 0x04 - 地图名称
    CharName m_char_name;                   // 0x15 - 角色名称
    int m_curr_x;                           // 0x24 - 当前X坐标
    int m_curr_y;                           // 0x28 - 当前Y坐标
    BYTE m_direction;                       // 方向
    BYTE m_gender;                          // 0x2D - 性别
    BYTE m_hair;                            // 0x2E - 头发
    BYTE m_job;                             // 0x2F - 职业
    int m_gold;                             // 0x30 - 金币
    Ability m_ability;                      // 0x34 -> 0x5B - 能力值
    int m_char_status;                      // 0x5C - 角色状态
    MapName m_home_map;                     // 0x78 - 回城地图
    int m_home_x;                           // 0x8C - 回城X坐标
    int m_home_y;                           // 0x90 - 回城Y坐标
    bool m_on_horse;                        // 0x95 - 是否骑马
    BYTE m_horse_type;                      // 马匹类型
    BYTE m_dress_eff_type;                  // 服装效果类型
    int m_pk_point;                         // 0xAC - PK值
    bool m_allow_group;                     // 0xB0 - 允许组队
    bool m_allow_guild;                     // 0xB1 - 允许加入行会
    int m_inc_health;                       // 0x0B4 - 生命增量
    int m_inc_spell;                        // 0x0B8 - 魔法增量
    int m_inc_healing;                      // 0x0BC - 治疗增量
    int m_fight_zone_die_count;             // 0x0C0 - 战斗区域死亡次数
    NakedAbility m_bonus_ability;           // 0x0CA - 奖励能力
    NakedAbility m_cur_bonus_ability;       // 0x0DE - 当前奖励能力
    int m_bonus_point;                      // 0x0F4 - 奖励点数
    int m_hunger_status;                    // 0x0F8 - 饥饿状态
    bool m_allow_guild_recall;              // 0xFC - 允许行会召唤
    double m_body_luck;                     // 0x100 - 身体幸运值
    int m_body_luck_level;                  // 0x108 - 身体幸运等级
    WORD m_group_recall_time;               // 0x10C - 组队召唤时间
    bool m_allow_group_recall;              // 0x10E - 允许组队召唤

    // Quest data
    QuestUnit m_quest_unit_open;            // 0x10F - 任务单元开启
    QuestUnit m_quest_unit;                 // 0x11C - 任务单元
    QuestFlag m_quest_flag;                 // 0x128 - 任务标志

    int m_char_status_ex;                   // 扩展角色状态
    DWORD m_fight_exp;                      // 0x194 - 战斗经验值
    Ability m_w_ability;                    // 0x198 - W能力值
    AddAbility m_add_ability;               // 0x1C0 - 附加能力值
    int m_view_range;                       // 0x1E4 - 视野范围

    // Status arrays
    StatusTime m_status_time_arr;           // 0x60 - 状态时间数组
    std::array<DWORD, MAX_STATUS_ATTRIBUTE> m_status_arr_tick; // 0x1E8 - 状态数组计时
    std::array<bool, 8> m_status_flags;    // 状态标志数组
    std::array<WORD, 6> m_status_arr_value; // 0x218 - 状态数组值
    std::array<DWORD, 6> m_status_arr_timeout_tick; // 0x220 - 状态数组超时计时

protected:
    // Component modules (following separation of concerns)
    std::unique_ptr<ObjectState> m_state_manager;
    std::unique_ptr<ObjectMovement> m_movement_manager;
    std::unique_ptr<ObjectCombat> m_combat_manager;
    std::unique_ptr<ObjectMagic> m_magic_manager;
    std::unique_ptr<ObjectInventory> m_inventory_manager;
    std::unique_ptr<ObjectStatus> m_status_manager;
    std::unique_ptr<ObjectGroup> m_group_manager;
    std::unique_ptr<ObjectGuild> m_guild_manager;

    // Core object references
    Environment* m_environment;
    UserEngine* m_user_engine;
    BaseObject* m_target_object;
    BaseObject* m_last_hitter;
    BaseObject* m_exp_hitter;
    BaseObject* m_master;
    BaseObject* m_group_owner;

    // Network data
    int m_socket;                           // 0x59C - Socket连接
    int m_gate_socket_idx;                  // 0x5A0 - 网关Socket索引
    int m_gate_idx;                         // 0x5A8 - 网关索引
    int m_soft_version_date;                // 0x5AC - 软件版本日期

    // Timing data
    DWORD m_search_time;                    // 0x360 - 搜索时间
    DWORD m_search_tick;                    // 0x364 - 搜索计时
    DWORD m_run_tick;                       // 0x368 - 跑步计时
    int m_run_time;                         // 0x36C - 跑步时间
    int m_health_tick;                      // 0x370 - 生命计时
    int m_spell_tick;                       // 0x374 - 魔法计时
    DWORD m_target_focus_tick;              // 0x37C - 目标焦点计时
    DWORD m_last_hitter_tick;               // 0x384 - 最后攻击者计时
    DWORD m_exp_hitter_tick;                // 0x38C - 经验攻击者计时
    DWORD m_next_run_time;                  // Next allowed run time (for timing control)

public:
    // Constructor and destructor
    BaseObject();
    virtual ~BaseObject();

    // Core virtual methods (matching original ObjBase.pas virtual methods)
    virtual void Initialize();              // FFFE
    virtual void Finalize();                // Custom cleanup
    virtual void Disappear();               // FFFD
    virtual bool Operate(const DefaultMessage& msg); // FFFC
    virtual void SearchObjectViewRange();   // Search objects in view range
    virtual void Run();                     // FFFB - Main update loop
    virtual void MakeGhost();               // Make object ghost
    virtual void Die();                     // Object death
    virtual void ReAlive();                 // FFF8 - Revive object
    virtual void RecalcAbilitys();          // FFF7 - Recalculate abilities

    // Target and relationship methods (matching original)
    virtual bool IsProtectTarget(BaseObject* target);     // FFF6
    virtual bool IsAttackTarget(BaseObject* target);      // FFF5
    virtual bool IsProperTarget(BaseObject* target);      // FFF4
    virtual bool IsProperFriend(BaseObject* target);      // FFF3
    virtual void SetTargetCreat(BaseObject* target);      // FFF2
    virtual void DelTargetCreat();                        // FFF1

    // Skill target methods (matching original exactly)
    virtual bool IsProperTargetSKILL_54(BaseObject* target);
    virtual bool IsProperTargetSKILL_55(int level, BaseObject* target);
    virtual bool IsProperTargetSKILL_56(BaseObject* target, int target_x, int target_y);
    virtual bool IsProperTargetSKILL_57(BaseObject* target);
    virtual bool IsProperTargetSKILL_70(BaseObject* target);

    // Core property accessors
    const MapName& GetMapName() const { return m_map_name; }
    const CharName& GetCharName() const { return m_char_name; }
    Point GetCurrentPos() const { return Point(m_curr_x, m_curr_y); }
    BYTE GetDirection() const { return m_direction; }
    BYTE GetGender() const { return m_gender; }
    BYTE GetJob() const { return m_job; }
    int GetGold() const { return m_gold; }
    const Ability& GetAbility() const { return m_ability; }
    int GetPKPoint() const { return m_pk_point; }
    int GetLevel() const { return m_ability.level; }

    // Core property setters
    void SetMapName(const MapName& map_name) { m_map_name = map_name; }
    void SetCharName(const CharName& char_name) { m_char_name = char_name; }
    void SetCurrentPos(const Point& pos) { m_curr_x = pos.x; m_curr_y = pos.y; }
    void SetDirection(BYTE direction) { m_direction = direction; }
    void SetGold(int gold) { m_gold = gold; }

    // Component accessors (for delegation)
    ObjectState* GetStateManager() const { return m_state_manager.get(); }
    ObjectMovement* GetMovementManager() const { return m_movement_manager.get(); }
    ObjectCombat* GetCombatManager() const { return m_combat_manager.get(); }
    ObjectMagic* GetMagicManager() const { return m_magic_manager.get(); }
    ObjectInventory* GetInventoryManager() const { return m_inventory_manager.get(); }
    ObjectStatus* GetStatusManager() const { return m_status_manager.get(); }
    ObjectGroup* GetGroupManager() const { return m_group_manager.get(); }
    ObjectGuild* GetGuildManager() const { return m_guild_manager.get(); }

    // Environment and engine accessors
    Environment* GetEnvironment() const { return m_environment; }
    UserEngine* GetUserEngine() const { return m_user_engine; }
    void SetEnvironment(Environment* env) { m_environment = env; }
    void SetUserEngine(UserEngine* engine) { m_user_engine = engine; }

    // Network accessors
    int GetSocket() const { return m_socket; }
    int GetGateSocketIdx() const { return m_gate_socket_idx; }
    int GetGateIdx() const { return m_gate_idx; }
    void SetSocket(int socket) { m_socket = socket; }
    void SetGateSocketIdx(int idx) { m_gate_socket_idx = idx; }
    void SetGateIdx(int idx) { m_gate_idx = idx; }

    // Utility methods
    virtual std::string GetShowName();
    virtual void DropUseItems(BaseObject* killer);
    virtual void ScatterBagItems(BaseObject* killer);
    bool GetMessage(DefaultMessage& msg);

    // Core game methods (delegated to appropriate managers)
    bool WalkTo(BYTE direction, bool flag);
    bool TurnTo(BYTE direction);
    void SpaceMove(const MapName& map, int x, int y, int mode);
    bool AttackTarget(BaseObject* target);
    void SendRefMsg(WORD ident, WORD param, int param1, int param2, int param3, const std::string& msg);
    void SendMsg(BaseObject* target, WORD ident, WORD param, int param1, int param2, int param3, const std::string& msg);

    // Additional message methods for UserEngine compatibility
    void SendUpdateMsg(BaseObject* target, WORD ident, WORD param, int param1, int param2, int param3, const std::string& msg);
    void SendActionMsg(BaseObject* target, WORD ident, WORD param, int param1, int param2, int param3, const std::string& msg);

    // Movement timing methods (following original project logic)
    bool IsReadyRun() const;
    void AdjustRunTick(int adjustment);

    // Status and ability methods
    void HealthSpellChanged();
    void StatusChanged();
    void FeatureChanged();
    void GoldChanged();
    void WeightChanged();

    // Distance and position utilities
    int GetDistance(BaseObject* target) const;
    int GetDistance(int x, int y) const;
    bool InRange(BaseObject* target, int range) const;
    bool InRange(int x, int y, int range) const;

    // Safety and validation
    bool IsValidObject() const;
    bool IsAlive() const;
    bool IsDead() const;
    bool IsGhost() const;

protected:
    // Internal initialization methods
    virtual void InitializeComponents();
    virtual void FinalizeComponents();
    virtual void UpdateComponents();

    // Internal utility methods
    void NotifyComponentsOfStateChange();
    void ValidateObjectState();
};

// Utility functions for BaseObject
namespace BaseObjectUtils {
    // Object creation helpers
    std::shared_ptr<BaseObject> CreateBaseObject();
    
    // Object validation
    bool IsValidBaseObject(const BaseObject* obj);
    
    // Distance and position calculations
    int CalculateDistance(const BaseObject* obj1, const BaseObject* obj2);
    BYTE CalculateDirection(const BaseObject* from, const BaseObject* to);
    Point CalculateFrontPosition(const BaseObject* obj);
    Point CalculateBackPosition(const BaseObject* obj);
    
    // Object relationship checks
    bool AreAllies(const BaseObject* obj1, const BaseObject* obj2);
    bool AreEnemies(const BaseObject* obj1, const BaseObject* obj2);
    bool CanAttack(const BaseObject* attacker, const BaseObject* target);
    bool CanCast(const BaseObject* caster, const BaseObject* target);
}
