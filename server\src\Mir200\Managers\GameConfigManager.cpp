#include "GameConfigManager.h"
#include "../Common/M2Share.h"
#include <fstream>
#include <sstream>
#include <iostream>

GameConfigManager::GameConfigManager()
    : m_manager<PERSON><PERSON>("GameConfigManager"), m_initialized(false) {
    SetDefaultConfigs();
}

GameConfigManager::~GameConfigManager() {
    Finalize();
}

bool GameConfigManager::Initialize() {
    std::cout << "[GameConfigManager] Initializing..." << std::endl;

    // 设置默认配置路径
    if (m_configPath.empty()) {
        m_configPath = "config/";
    }

    // 确保配置目录存在（简化实现）
    // std::filesystem::create_directories(m_configPath); // 暂时注释掉，避免编译问题

    // 加载配置文件
    if (!LoadConfig(m_configPath)) {
        std::cout << "[GameConfigManager] Warning: Failed to load config, using defaults" << std::endl;
        // 使用默认配置并保存
        SaveConfig(m_configPath);
    }

    m_initialized = true;
    std::cout << "[GameConfigManager] Initialized successfully" << std::endl;
    return true;
}

void GameConfigManager::Finalize() {
    if (!m_initialized) return;

    std::cout << "[GameConfigManager] Finalizing..." << std::endl;

    // 保存当前配置
    SaveConfig();

    m_initialized = false;
    std::cout << "[GameConfigManager] Finalized" << std::endl;
}

void GameConfigManager::Update() {
    // 配置管理器通常不需要每帧更新
    // 可以在这里检查配置文件是否有变化并重新加载
}

const std::string& GameConfigManager::GetManagerName() const {
    return m_managerName;
}

bool GameConfigManager::LoadConfig(const std::string& configPath) {
    std::lock_guard<std::mutex> lock(m_configMutex);
    
    m_configPath = configPath;
    
    bool success = true;
    success &= LoadServerConfig(GetConfigFilePath("server.ini"));
    success &= LoadGameRuleConfig(GetConfigFilePath("gamerule.ini"));
    success &= LoadRateConfig(GetConfigFilePath("rate.ini"));
    success &= LoadMapConfig(GetConfigFilePath("map.ini"));
    
    return success;
}

bool GameConfigManager::SaveConfig(const std::string& configPath) {
    std::lock_guard<std::mutex> lock(m_configMutex);
    
    std::string savePath = configPath.empty() ? m_configPath : configPath;
    
    bool success = true;
    success &= SaveServerConfig(GetConfigFilePath("server.ini"));
    success &= SaveGameRuleConfig(GetConfigFilePath("gamerule.ini"));
    success &= SaveRateConfig(GetConfigFilePath("rate.ini"));
    success &= SaveMapConfig(GetConfigFilePath("map.ini"));
    
    return success;
}

void GameConfigManager::ReloadConfig() {
    LoadConfig(m_configPath);
}

const ServerConfig& GameConfigManager::GetServerConfig() const {
    std::lock_guard<std::mutex> lock(m_configMutex);
    return m_serverConfig;
}

const GameRuleConfig& GameConfigManager::GetGameRuleConfig() const {
    std::lock_guard<std::mutex> lock(m_configMutex);
    return m_gameRuleConfig;
}

const RateConfig& GameConfigManager::GetRateConfig() const {
    std::lock_guard<std::mutex> lock(m_configMutex);
    return m_rateConfig;
}

const MapConfig& GameConfigManager::GetMapConfig() const {
    std::lock_guard<std::mutex> lock(m_configMutex);
    return m_mapConfig;
}

void GameConfigManager::SetServerConfig(const ServerConfig& config) {
    std::lock_guard<std::mutex> lock(m_configMutex);
    m_serverConfig = config;
}

void GameConfigManager::SetGameRuleConfig(const GameRuleConfig& config) {
    std::lock_guard<std::mutex> lock(m_configMutex);
    m_gameRuleConfig = config;
}

void GameConfigManager::SetRateConfig(const RateConfig& config) {
    std::lock_guard<std::mutex> lock(m_configMutex);
    m_rateConfig = config;
}

void GameConfigManager::SetMapConfig(const MapConfig& config) {
    std::lock_guard<std::mutex> lock(m_configMutex);
    m_mapConfig = config;
}

std::string GameConfigManager::GetCustomConfig(const std::string& key, const std::string& defaultValue) const {
    std::lock_guard<std::mutex> lock(m_configMutex);
    auto it = m_customConfigs.find(key);
    return (it != m_customConfigs.end()) ? it->second : defaultValue;
}

void GameConfigManager::SetCustomConfig(const std::string& key, const std::string& value) {
    std::lock_guard<std::mutex> lock(m_configMutex);
    m_customConfigs[key] = value;
}

bool GameConfigManager::ValidateConfig() const {
    std::lock_guard<std::mutex> lock(m_configMutex);
    
    // 验证服务器配置
    if (m_serverConfig.maxUserCount <= 0 || m_serverConfig.maxUserCount > 10000) {
        return false;
    }
    
    // 验证游戏规则配置
    if (m_gameRuleConfig.hitIntervalTime < 100 || m_gameRuleConfig.hitIntervalTime > 5000) {
        return false;
    }
    
    // 验证倍率配置
    if (m_rateConfig.monsterPowerRate <= 0 || m_rateConfig.monsterPowerRate > 1000) {
        return false;
    }
    
    return true;
}

bool GameConfigManager::IsInitialized() const {
    std::lock_guard<std::mutex> lock(m_configMutex);
    return m_initialized;
}

void GameConfigManager::SetDefaultConfigs() {
    // 默认配置已在结构体构造函数中设置
    m_serverConfig = ServerConfig();
    m_gameRuleConfig = GameRuleConfig();
    m_rateConfig = RateConfig();
    m_mapConfig = MapConfig();
}

std::string GameConfigManager::GetConfigFilePath(const std::string& configName) const {
    return m_configPath + configName;
}

bool GameConfigManager::LoadServerConfig(const std::string& configFile) {
    std::ifstream file(configFile);
    if (!file.is_open()) {
        return false;
    }

    std::string line;
    while (std::getline(file, line)) {
        if (line.empty() || line[0] == ';' || line[0] == '#') continue;

        size_t pos = line.find('=');
        if (pos == std::string::npos) continue;

        std::string key = line.substr(0, pos);
        std::string value = line.substr(pos + 1);

        // 去除空格
        key.erase(0, key.find_first_not_of(" \t"));
        key.erase(key.find_last_not_of(" \t") + 1);
        value.erase(0, value.find_first_not_of(" \t"));
        value.erase(value.find_last_not_of(" \t") + 1);

        if (key == "ServerName") m_serverConfig.serverName = value;
        else if (key == "Version") m_serverConfig.version = value;
        else if (key == "VersionDate") m_serverConfig.versionDate = value;
        else if (key == "MaxUserCount") m_serverConfig.maxUserCount = std::stoi(value);
        else if (key == "TestUserLimit") m_serverConfig.testUserLimit = std::stoi(value);
        else if (key == "StartPermission") m_serverConfig.startPermission = std::stoi(value);
        else if (key == "TestServer") m_serverConfig.testServer = (value == "1" || value == "true");
        else if (key == "ServiceMode") m_serverConfig.serviceMode = (value == "1" || value == "true");
        else if (key == "VentureMode") m_serverConfig.ventureMode = (value == "1" || value == "true");
        else if (key == "NonPKMode") m_serverConfig.nonPKMode = (value == "1" || value == "true");
    }

    return true;
}

bool GameConfigManager::LoadGameRuleConfig(const std::string& configFile) {
    std::ifstream file(configFile);
    if (!file.is_open()) {
        return false;
    }

    std::string line;
    while (std::getline(file, line)) {
        if (line.empty() || line[0] == ';' || line[0] == '#') continue;

        size_t pos = line.find('=');
        if (pos == std::string::npos) continue;

        std::string key = line.substr(0, pos);
        std::string value = line.substr(pos + 1);

        // 去除空格
        key.erase(0, key.find_first_not_of(" \t"));
        key.erase(key.find_last_not_of(" \t") + 1);
        value.erase(0, value.find_first_not_of(" \t"));
        value.erase(value.find_last_not_of(" \t") + 1);

        if (key == "HitIntervalTime") m_gameRuleConfig.hitIntervalTime = std::stoi(value);
        else if (key == "MagicHitIntervalTime") m_gameRuleConfig.magicHitIntervalTime = std::stoi(value);
        else if (key == "RunIntervalTime") m_gameRuleConfig.runIntervalTime = std::stoi(value);
        else if (key == "WalkIntervalTime") m_gameRuleConfig.walkIntervalTime = std::stoi(value);
        else if (key == "TurnIntervalTime") m_gameRuleConfig.turnIntervalTime = std::stoi(value);
        else if (key == "DigUpIntervalTime") m_gameRuleConfig.digUpIntervalTime = std::stoi(value);
        else if (key == "ItemSpeedTime") m_gameRuleConfig.itemSpeedTime = std::stoi(value);
        else if (key == "MaxHitMsgCount") m_gameRuleConfig.maxHitMsgCount = std::stoi(value);
        else if (key == "MaxSpellMsgCount") m_gameRuleConfig.maxSpellMsgCount = std::stoi(value);
        else if (key == "MaxRunMsgCount") m_gameRuleConfig.maxRunMsgCount = std::stoi(value);
        else if (key == "MaxWalkMsgCount") m_gameRuleConfig.maxWalkMsgCount = std::stoi(value);
        else if (key == "MaxTurnMsgCount") m_gameRuleConfig.maxTurnMsgCount = std::stoi(value);
        else if (key == "MaxDigUpMsgCount") m_gameRuleConfig.maxDigUpMsgCount = std::stoi(value);
        else if (key == "DecPkPointTime") m_gameRuleConfig.decPkPointTime = std::stoi(value);
        else if (key == "DecPkPointCount") m_gameRuleConfig.decPkPointCount = std::stoi(value);
        else if (key == "PKFlagTime") m_gameRuleConfig.pkFlagTime = std::stoi(value);
        else if (key == "KillHumanAddPKPoint") m_gameRuleConfig.killHumanAddPKPoint = std::stoi(value);
        else if (key == "PKProtectLevel") m_gameRuleConfig.pkProtectLevel = std::stoi(value);
        else if (key == "RedPKProtectLevel") m_gameRuleConfig.redPKProtectLevel = std::stoi(value);
        else if (key == "PKLevelProtect") m_gameRuleConfig.pkLevelProtect = (value == "1" || value == "true");
        else if (key == "KillMonExpMultiple") m_gameRuleConfig.killMonExpMultiple = std::stoi(value);
        else if (key == "HighLevelKillMonFixExp") m_gameRuleConfig.highLevelKillMonFixExp = (value == "1" || value == "true");
        else if (key == "FixExp") m_gameRuleConfig.fixExp = (value == "1" || value == "true");
        else if (key == "BaseExp") m_gameRuleConfig.baseExp = std::stoi(value);
        else if (key == "AddExp") m_gameRuleConfig.addExp = std::stoi(value);
        else if (key == "HighLevelGroupFixExp") m_gameRuleConfig.highLevelGroupFixExp = (value == "1" || value == "true");
        else if (key == "LimitExpLevel") m_gameRuleConfig.limitExpLevel = std::stoi(value);
        else if (key == "LimitExpValue") m_gameRuleConfig.limitExpValue = std::stoi(value);
    }

    return true;
}

bool GameConfigManager::LoadRateConfig(const std::string& configFile) {
    std::ifstream file(configFile);
    if (!file.is_open()) {
        return false;
    }

    std::string line;
    while (std::getline(file, line)) {
        if (line.empty() || line[0] == ';' || line[0] == '#') continue;

        size_t pos = line.find('=');
        if (pos == std::string::npos) continue;

        std::string key = line.substr(0, pos);
        std::string value = line.substr(pos + 1);

        // 去除空格
        key.erase(0, key.find_first_not_of(" \t"));
        key.erase(key.find_last_not_of(" \t") + 1);
        value.erase(0, value.find_first_not_of(" \t"));
        value.erase(value.find_last_not_of(" \t") + 1);

        if (key == "MonsterPowerRate") m_rateConfig.monsterPowerRate = std::stoi(value);
        else if (key == "ItemsPowerRate") m_rateConfig.itemsPowerRate = std::stoi(value);
        else if (key == "ItemsACPowerRate") m_rateConfig.itemsACPowerRate = std::stoi(value);
        else if (key == "SuperRepairPriceRate") m_rateConfig.superRepairPriceRate = std::stoi(value);
        else if (key == "RepairItemDecDura") m_rateConfig.repairItemDecDura = std::stoi(value);
    }

    return true;
}

bool GameConfigManager::LoadMapConfig(const std::string& configFile) {
    std::ifstream file(configFile);
    if (!file.is_open()) {
        return false;
    }

    std::string line;
    while (std::getline(file, line)) {
        if (line.empty() || line[0] == ';' || line[0] == '#') continue;

        size_t pos = line.find('=');
        if (pos == std::string::npos) continue;

        std::string key = line.substr(0, pos);
        std::string value = line.substr(pos + 1);

        // 去除空格
        key.erase(0, key.find_first_not_of(" \t"));
        key.erase(key.find_last_not_of(" \t") + 1);
        value.erase(0, value.find_first_not_of(" \t"));
        value.erase(value.find_last_not_of(" \t") + 1);

        if (key == "SafeZoneSize") m_mapConfig.safeZoneSize = std::stoi(value);
        else if (key == "StartPointSize") m_mapConfig.startPointSize = std::stoi(value);
        else if (key == "HomeMap") m_mapConfig.homeMap = value;
        else if (key == "HomeX") m_mapConfig.homeX = std::stoi(value);
        else if (key == "HomeY") m_mapConfig.homeY = std::stoi(value);
        else if (key == "RedHomeMap") m_mapConfig.redHomeMap = value;
        else if (key == "RedHomeX") m_mapConfig.redHomeX = std::stoi(value);
        else if (key == "RedHomeY") m_mapConfig.redHomeY = std::stoi(value);
        else if (key == "RedDieHomeMap") m_mapConfig.redDieHomeMap = value;
        else if (key == "RedDieHomeX") m_mapConfig.redDieHomeX = std::stoi(value);
        else if (key == "RedDieHomeY") m_mapConfig.redDieHomeY = std::stoi(value);
    }

    return true;
}

bool GameConfigManager::SaveServerConfig(const std::string& configFile) const {
    std::ofstream file(configFile);
    if (!file.is_open()) {
        return false;
    }

    file << "; Server Configuration\n";
    file << "ServerName=" << m_serverConfig.serverName << "\n";
    file << "Version=" << m_serverConfig.version << "\n";
    file << "VersionDate=" << m_serverConfig.versionDate << "\n";
    file << "MaxUserCount=" << m_serverConfig.maxUserCount << "\n";
    file << "TestUserLimit=" << m_serverConfig.testUserLimit << "\n";
    file << "StartPermission=" << m_serverConfig.startPermission << "\n";
    file << "TestServer=" << (m_serverConfig.testServer ? "1" : "0") << "\n";
    file << "ServiceMode=" << (m_serverConfig.serviceMode ? "1" : "0") << "\n";
    file << "VentureMode=" << (m_serverConfig.ventureMode ? "1" : "0") << "\n";
    file << "NonPKMode=" << (m_serverConfig.nonPKMode ? "1" : "0") << "\n";

    return true;
}

bool GameConfigManager::SaveGameRuleConfig(const std::string& configFile) const {
    std::ofstream file(configFile);
    if (!file.is_open()) {
        return false;
    }

    file << "; Game Rule Configuration\n";
    file << "; Speed Settings\n";
    file << "HitIntervalTime=" << m_gameRuleConfig.hitIntervalTime << "\n";
    file << "MagicHitIntervalTime=" << m_gameRuleConfig.magicHitIntervalTime << "\n";
    file << "RunIntervalTime=" << m_gameRuleConfig.runIntervalTime << "\n";
    file << "WalkIntervalTime=" << m_gameRuleConfig.walkIntervalTime << "\n";
    file << "TurnIntervalTime=" << m_gameRuleConfig.turnIntervalTime << "\n";
    file << "DigUpIntervalTime=" << m_gameRuleConfig.digUpIntervalTime << "\n";
    file << "ItemSpeedTime=" << m_gameRuleConfig.itemSpeedTime << "\n";

    file << "\n; Message Limits\n";
    file << "MaxHitMsgCount=" << m_gameRuleConfig.maxHitMsgCount << "\n";
    file << "MaxSpellMsgCount=" << m_gameRuleConfig.maxSpellMsgCount << "\n";
    file << "MaxRunMsgCount=" << m_gameRuleConfig.maxRunMsgCount << "\n";
    file << "MaxWalkMsgCount=" << m_gameRuleConfig.maxWalkMsgCount << "\n";
    file << "MaxTurnMsgCount=" << m_gameRuleConfig.maxTurnMsgCount << "\n";
    file << "MaxDigUpMsgCount=" << m_gameRuleConfig.maxDigUpMsgCount << "\n";

    file << "\n; PK Settings\n";
    file << "DecPkPointTime=" << m_gameRuleConfig.decPkPointTime << "\n";
    file << "DecPkPointCount=" << m_gameRuleConfig.decPkPointCount << "\n";
    file << "PKFlagTime=" << m_gameRuleConfig.pkFlagTime << "\n";
    file << "KillHumanAddPKPoint=" << m_gameRuleConfig.killHumanAddPKPoint << "\n";
    file << "PKProtectLevel=" << m_gameRuleConfig.pkProtectLevel << "\n";
    file << "RedPKProtectLevel=" << m_gameRuleConfig.redPKProtectLevel << "\n";
    file << "PKLevelProtect=" << (m_gameRuleConfig.pkLevelProtect ? "1" : "0") << "\n";

    file << "\n; Experience Settings\n";
    file << "KillMonExpMultiple=" << m_gameRuleConfig.killMonExpMultiple << "\n";
    file << "HighLevelKillMonFixExp=" << (m_gameRuleConfig.highLevelKillMonFixExp ? "1" : "0") << "\n";
    file << "FixExp=" << (m_gameRuleConfig.fixExp ? "1" : "0") << "\n";
    file << "BaseExp=" << m_gameRuleConfig.baseExp << "\n";
    file << "AddExp=" << m_gameRuleConfig.addExp << "\n";
    file << "HighLevelGroupFixExp=" << (m_gameRuleConfig.highLevelGroupFixExp ? "1" : "0") << "\n";
    file << "LimitExpLevel=" << m_gameRuleConfig.limitExpLevel << "\n";
    file << "LimitExpValue=" << m_gameRuleConfig.limitExpValue << "\n";

    return true;
}

bool GameConfigManager::SaveRateConfig(const std::string& configFile) const {
    std::ofstream file(configFile);
    if (!file.is_open()) {
        return false;
    }

    file << "; Rate Configuration\n";
    file << "MonsterPowerRate=" << m_rateConfig.monsterPowerRate << "\n";
    file << "ItemsPowerRate=" << m_rateConfig.itemsPowerRate << "\n";
    file << "ItemsACPowerRate=" << m_rateConfig.itemsACPowerRate << "\n";
    file << "SuperRepairPriceRate=" << m_rateConfig.superRepairPriceRate << "\n";
    file << "RepairItemDecDura=" << m_rateConfig.repairItemDecDura << "\n";

    return true;
}

bool GameConfigManager::SaveMapConfig(const std::string& configFile) const {
    std::ofstream file(configFile);
    if (!file.is_open()) {
        return false;
    }

    file << "; Map Configuration\n";
    file << "SafeZoneSize=" << m_mapConfig.safeZoneSize << "\n";
    file << "StartPointSize=" << m_mapConfig.startPointSize << "\n";
    file << "HomeMap=" << m_mapConfig.homeMap << "\n";
    file << "HomeX=" << m_mapConfig.homeX << "\n";
    file << "HomeY=" << m_mapConfig.homeY << "\n";
    file << "RedHomeMap=" << m_mapConfig.redHomeMap << "\n";
    file << "RedHomeX=" << m_mapConfig.redHomeX << "\n";
    file << "RedHomeY=" << m_mapConfig.redHomeY << "\n";
    file << "RedDieHomeMap=" << m_mapConfig.redDieHomeMap << "\n";
    file << "RedDieHomeX=" << m_mapConfig.redDieHomeX << "\n";
    file << "RedDieHomeY=" << m_mapConfig.redDieHomeY << "\n";

    return true;
}
