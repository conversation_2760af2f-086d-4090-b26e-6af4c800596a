#pragma once

#include "IManager.h"
#include "../Common/Types.h"
#include <string>
#include <queue>
#include <mutex>
#include <thread>
#include <atomic>
#include <condition_variable>
#include <unordered_map>
#include <functional>
#include <memory>

// Forward declarations
class PlayObject;

/**
 * @brief 保存数据类型枚举
 * 基于原项目FrnEngn.pas中的数据保存类型
 */
enum class SaveDataType {
    PLAYER_DATA = 1,        // 玩家数据
    GUILD_DATA = 2,         // 行会数据
    CASTLE_DATA = 3,        // 城堡数据
    MARKET_DATA = 4,        // 市场数据
    SYSTEM_DATA = 5,        // 系统数据
    LOG_DATA = 6,           // 日志数据
    BACKUP_FILE_DATA = 7    // 备份数据
};

/**
 * @brief 保存优先级枚举
 */
enum class SavePriority {
    LOW = 0,               // 低优先级
    NORMAL = 1,            // 普通优先级
    HIGH = 2,              // 高优先级
    CRITICAL = 3           // 关键优先级
};

/**
 * @brief 保存任务结构
 * 基于原项目FrnEngn.pas中的保存任务结构
 */
struct SaveTask {
    uint32_t taskId;                    // 任务ID
    SaveDataType dataType;              // 数据类型
    SavePriority priority;              // 优先级
    std::string fileName;               // 文件名
    std::string data;                   // 数据内容
    std::function<bool()> saveFunction; // 保存函数
    DWORD createTime;                   // 创建时间
    DWORD deadline;                     // 截止时间
    int retryCount;                     // 重试次数
    int maxRetries;                     // 最大重试次数
    bool completed;                     // 是否完成
    std::string errorMessage;           // 错误信息
    
    SaveTask() 
        : taskId(0), dataType(SaveDataType::PLAYER_DATA), priority(SavePriority::NORMAL)
        , createTime(0), deadline(0), retryCount(0), maxRetries(3), completed(false) {
    }
};

/**
 * @brief 自动保存配置
 */
struct AutoSaveConfig {
    bool enabled;                       // 是否启用
    DWORD interval;                     // 保存间隔(毫秒)
    SaveDataType dataType;              // 数据类型
    std::function<bool()> saveFunction; // 保存函数
    DWORD lastSaveTime;                 // 上次保存时间
    
    AutoSaveConfig() 
        : enabled(false), interval(300000), dataType(SaveDataType::PLAYER_DATA), lastSaveTime(0) {
    }
};

/**
 * @brief 数据保存管理器
 * 基于原项目FrnEngn.pas的功能，优化数据保存性能
 * 提供异步保存、批量保存、优先级队列等功能
 */
class SaveDataManager : public IManager {
private:
    // 保存任务队列
    std::priority_queue<std::shared_ptr<SaveTask>, 
                       std::vector<std::shared_ptr<SaveTask>>,
                       std::function<bool(const std::shared_ptr<SaveTask>&, const std::shared_ptr<SaveTask>&)>> m_taskQueue;
    std::mutex m_queueMutex;
    std::condition_variable m_queueCondition;
    
    // 工作线程
    std::thread m_workerThread;
    std::atomic<bool> m_running;
    
    // 任务管理
    std::unordered_map<uint32_t, std::shared_ptr<SaveTask>> m_activeTasks;
    std::atomic<uint32_t> m_nextTaskId;
    std::mutex m_taskMutex;
    
    // 自动保存配置
    std::unordered_map<SaveDataType, AutoSaveConfig> m_autoSaveConfigs;
    std::mutex m_autoSaveMutex;
    
    // 统计信息
    std::atomic<uint64_t> m_totalTasks;
    std::atomic<uint64_t> m_completedTasks;
    std::atomic<uint64_t> m_failedTasks;
    std::atomic<uint64_t> m_totalSaveTime;
    
    // 配置参数
    size_t m_maxQueueSize;
    DWORD m_saveTimeout;
    std::string m_dataDirectory;
    std::string m_backupDirectory;
    
    // 管理器状态
    std::string m_managerName;
    bool m_initialized;

public:
    SaveDataManager();
    virtual ~SaveDataManager();
    
    // IManager接口实现
    bool Initialize() override;
    void Finalize() override;
    void Update() override;
    const std::string& GetManagerName() const override;
    
    // 异步保存任务
    uint32_t QueueSave(SaveDataType dataType, const std::string& fileName, 
                      const std::string& data, SavePriority priority = SavePriority::NORMAL);
    uint32_t QueueSave(SaveDataType dataType, std::function<bool()> saveFunction, 
                      SavePriority priority = SavePriority::NORMAL);
    
    // 玩家数据保存
    uint32_t SavePlayerData(const std::string& playerName, const std::string& data, 
                           SavePriority priority = SavePriority::NORMAL);
    uint32_t SavePlayerData(PlayObject* player, SavePriority priority = SavePriority::NORMAL);
    
    // 批量保存
    std::vector<uint32_t> BatchSave(const std::vector<std::pair<std::string, std::string>>& files,
                                   SaveDataType dataType, SavePriority priority = SavePriority::NORMAL);
    
    // 同步保存（立即执行）
    bool SaveImmediate(SaveDataType dataType, const std::string& fileName, const std::string& data);
    bool SaveImmediate(SaveDataType dataType, std::function<bool()> saveFunction);
    
    // 自动保存配置
    void SetAutoSave(SaveDataType dataType, bool enabled, DWORD interval, std::function<bool()> saveFunction);
    void EnableAutoSave(SaveDataType dataType, bool enabled);
    bool IsAutoSaveEnabled(SaveDataType dataType) const;
    
    // 任务管理
    bool CancelTask(uint32_t taskId);
    bool IsTaskCompleted(uint32_t taskId) const;
    bool IsTaskFailed(uint32_t taskId) const;
    std::string GetTaskError(uint32_t taskId) const;
    
    // 队列管理
    void FlushAll();
    void FlushByType(SaveDataType dataType);
    void FlushByPriority(SavePriority priority);
    bool IsQueueFull() const;
    size_t GetQueueSize() const;
    
    // 配置管理
    void SetMaxQueueSize(size_t maxSize) { m_maxQueueSize = maxSize; }
    void SetSaveTimeout(DWORD timeout) { m_saveTimeout = timeout; }
    void SetDataDirectory(const std::string& directory) { m_dataDirectory = directory; }
    void SetBackupDirectory(const std::string& directory) { m_backupDirectory = directory; }
    
    // 统计信息
    uint64_t GetTotalTasks() const { return m_totalTasks; }
    uint64_t GetCompletedTasks() const { return m_completedTasks; }
    uint64_t GetFailedTasks() const { return m_failedTasks; }
    uint64_t GetAverageSaveTime() const;
    double GetSuccessRate() const;
    
    // 调试和监控
    std::vector<std::string> GetQueueInfo() const;
    std::string GetStatistics() const;
    void DumpQueueStatus() const;

private:
    // 工作线程函数
    void WorkerThreadFunction();
    
    // 任务处理
    void ProcessTask(std::shared_ptr<SaveTask> task);
    bool ExecuteSaveTask(SaveTask& task);
    void HandleTaskFailure(SaveTask& task);
    void CompleteTask(SaveTask& task, bool success);
    
    // 自动保存处理
    void ProcessAutoSave();
    bool ShouldAutoSave(const AutoSaveConfig& config) const;
    
    // 文件操作
    bool SaveToFile(const std::string& fileName, const std::string& data);
    bool CreateBackup(const std::string& fileName);
    std::string GetFullPath(const std::string& fileName, SaveDataType dataType) const;
    
    // 工具方法
    uint32_t GenerateTaskId();
    std::string GetDataTypeString(SaveDataType dataType) const;
    std::string GetPriorityString(SavePriority priority) const;
    void ValidateTask(const SaveTask& task) const;
    
    // 队列比较函数
    static bool TaskComparator(const std::shared_ptr<SaveTask>& a, const std::shared_ptr<SaveTask>& b);
};
