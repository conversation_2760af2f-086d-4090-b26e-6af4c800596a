#include "MapManager.h"
#include "EventBus.h"
#include "../Engine/Environment.h"
#include "../BaseObject/BaseObject.h"
#include "../BaseObject/PlayObject.h"
#include "../Common/M2Share.h"
#include <iostream>
#include <fstream>
#include <sstream>
#include <random>
#include <algorithm>

MapManager::MapManager() 
    : m_manager<PERSON>ame("MapManager")
    , m_initialized(false)
    , m_eventBus(nullptr)
    , m_totalMapLoads(0)
    , m_totalTeleports(0)
    , m_totalSpawns(0) {
}

MapManager::~MapManager() {
    Finalize();
}

bool MapManager::Initialize() {
    if (m_initialized) return true;
    
    std::cout << "[MapManager] Initializing..." << std::endl;
    
    // 加载地图数据
    if (!LoadMapData()) {
        std::cerr << "[MapManager] Failed to load map data" << std::endl;
        return false;
    }
    
    // 加载配置文件
    LoadMapConfigs("MapConfig.txt");
    LoadGateConfigs("MapGates.txt");
    LoadSafeZoneConfigs("SafeZones.txt");
    LoadSpawnConfigs("MonsterSpawns.txt");
    
    m_initialized = true;
    std::cout << "[MapManager] Initialized successfully" << std::endl;
    return true;
}

void MapManager::Finalize() {
    if (!m_initialized) return;
    
    std::cout << "[MapManager] Finalizing..." << std::endl;
    
    // 清理地图数据
    {
        std::unique_lock<std::shared_mutex> lock(m_environmentMutex);
        m_environments.clear();
    }
    
    // 清理配置数据
    {
        std::unique_lock<std::shared_mutex> lock(m_gateMutex);
        m_mapGates.clear();
    }
    
    {
        std::unique_lock<std::shared_mutex> lock(m_safeMutex);
        m_safeZones.clear();
    }
    
    {
        std::unique_lock<std::shared_mutex> lock(m_spawnMutex);
        m_spawnConfigs.clear();
    }
    
    m_initialized = false;
    std::cout << "[MapManager] Finalized" << std::endl;
}

void MapManager::Update() {
    if (!m_initialized) return;
    
    // 处理怪物刷新
    ProcessSpawns();
}

const std::string& MapManager::GetManagerName() const {
    return m_managerName;
}

void MapManager::SetEventBus(EventBus* eventBus) {
    m_eventBus = eventBus;
    
    // 订阅相关事件
    if (m_eventBus) {
        m_eventBus->Subscribe("MapChange", [this](const EventData& data) {
            OnEvent("MapChange", data);
        });
        
        m_eventBus->Subscribe("MonsterKilled", [this](const EventData& data) {
            OnEvent("MonsterKilled", data);
        });
        
        m_eventBus->Subscribe("ObjectSpawned", [this](const EventData& data) {
            OnEvent("ObjectSpawned", data);
        });
    }
}

Environment* MapManager::GetEnvironment(const std::string& mapName) {
    std::shared_lock<std::shared_mutex> lock(m_environmentMutex);
    
    auto it = m_environments.find(mapName);
    if (it != m_environments.end()) {
        return it->second.get();
    }
    
    return nullptr;
}

bool MapManager::CanWalk(const std::string& mapName, int x, int y) {
    Environment* env = GetEnvironment(mapName);
    if (!env) return false;
    
    return env->CanWalk(x, y);
}

std::vector<BaseObject*> MapManager::GetObjectsInRange(const std::string& mapName, 
                                                      const Point& center, int range) {
    std::vector<BaseObject*> objects;
    
    Environment* env = GetEnvironment(mapName);
    if (!env) return objects;
    
    // 这里需要实现获取范围内对象的逻辑
    // 暂时返回空列表
    return objects;
}

void MapManager::OnEvent(const std::string& eventType, const EventData& data) {
    if (eventType == "MapChange") {
        // 处理地图切换事件
        LogMapOperation("OnEvent", "Map change event received");
    }
    else if (eventType == "MonsterKilled") {
        // 处理怪物死亡事件，可能需要重新刷新
        LogMapOperation("OnEvent", "Monster killed event received");
    }
    else if (eventType == "ObjectSpawned") {
        // 处理对象生成事件
        LogMapOperation("OnEvent", "Object spawned event received");
    }
}

bool MapManager::LoadMapData() {
    // 加载基础地图数据
    // 这里应该从数据库或文件中加载地图信息
    LogMapOperation("LoadMapData", "Map data loaded");
    return true;
}

bool MapManager::LoadMapConfigs(const std::string& configFile) {
    // 加载地图基础配置
    LogMapOperation("LoadMapConfigs", "Map configs loaded from " + configFile);
    return true;
}

bool MapManager::LoadGateConfigs(const std::string& configFile) {
    return LoadGateConfigFromFile(configFile);
}

bool MapManager::LoadSafeZoneConfigs(const std::string& configFile) {
    return LoadSafeZoneConfigFromFile(configFile);
}

bool MapManager::LoadSpawnConfigs(const std::string& configFile) {
    return LoadSpawnConfigFromFile(configFile);
}

bool MapManager::LoadMap(const std::string& mapName) {
    std::unique_lock<std::shared_mutex> lock(m_environmentMutex);
    
    // 检查是否已经加载
    if (m_environments.find(mapName) != m_environments.end()) {
        return true;
    }
    
    // 加载地图文件
    if (!LoadMapFromFile(mapName)) {
        LogMapError("LoadMap", "Failed to load map: " + mapName);
        return false;
    }
    
    ++m_totalMapLoads;
    LogMapOperation("LoadMap", "Map loaded: " + mapName);
    return true;
}

bool MapManager::UnloadMap(const std::string& mapName) {
    std::unique_lock<std::shared_mutex> lock(m_environmentMutex);
    
    auto it = m_environments.find(mapName);
    if (it != m_environments.end()) {
        m_environments.erase(it);
        LogMapOperation("UnloadMap", "Map unloaded: " + mapName);
        return true;
    }
    
    return false;
}

bool MapManager::IsMapLoaded(const std::string& mapName) const {
    std::shared_lock<std::shared_mutex> lock(m_environmentMutex);
    return m_environments.find(mapName) != m_environments.end();
}

std::vector<std::string> MapManager::GetLoadedMaps() const {
    std::vector<std::string> maps;
    std::shared_lock<std::shared_mutex> lock(m_environmentMutex);
    
    for (const auto& pair : m_environments) {
        maps.push_back(pair.first);
    }
    
    return maps;
}

bool MapManager::CanTeleport(const std::string& fromMap, int fromX, int fromY, 
                           const std::string& toMap, int toX, int toY) const {
    // 检查传送条件
    MapGateConfig* gate = const_cast<MapManager*>(this)->FindGate(fromMap, fromX, fromY);
    if (!gate) return false;
    
    // 验证目标地图和坐标
    return (gate->toMap == toMap && gate->toX == toX && gate->toY == toY);
}

bool MapManager::TeleportPlayer(PlayObject* player, const std::string& toMap, int toX, int toY) {
    if (!player) return false;
    
    // 检查目标地图是否存在
    if (!IsMapLoaded(toMap)) {
        if (!const_cast<MapManager*>(this)->LoadMap(toMap)) {
            return false;
        }
    }
    
    // 检查目标位置是否可行走
    if (!CanWalk(toMap, toX, toY)) {
        // 寻找附近可行走的位置
        Point newPos = FindRandomWalkablePosition(toMap, {toX, toY}, 5);
        if (newPos.x == -1) return false;
        toX = newPos.x;
        toY = newPos.y;
    }
    
    // 执行传送
    // 这里需要调用玩家的传送方法
    // player->SpaceMove(toMap, toX, toY);
    
    ++m_totalTeleports;
    LogMapOperation("TeleportPlayer", "Player teleported to " + toMap);
    return true;
}

MapGateConfig* MapManager::FindGate(const std::string& mapName, int x, int y) {
    std::shared_lock<std::shared_mutex> lock(m_gateMutex);
    
    auto it = m_mapGates.find(mapName);
    if (it != m_mapGates.end()) {
        for (auto& gate : it->second) {
            if (abs(gate.fromX - x) <= 1 && abs(gate.fromY - y) <= 1) {
                return &gate;
            }
        }
    }
    
    return nullptr;
}

bool MapManager::IsInSafeZone(const std::string& mapName, int x, int y) const {
    std::shared_lock<std::shared_mutex> lock(m_safeMutex);
    
    auto it = m_safeZones.find(mapName);
    if (it != m_safeZones.end()) {
        for (const auto& zone : it->second) {
            int distance = abs(zone.x - x) + abs(zone.y - y);
            if (distance <= zone.range) {
                return true;
            }
        }
    }
    
    return false;
}

SafeZoneConfig* MapManager::GetSafeZone(const std::string& mapName, int x, int y) {
    std::shared_lock<std::shared_mutex> lock(m_safeMutex);
    
    auto it = m_safeZones.find(mapName);
    if (it != m_safeZones.end()) {
        for (auto& zone : it->second) {
            int distance = abs(zone.x - x) + abs(zone.y - y);
            if (distance <= zone.range) {
                return &zone;
            }
        }
    }
    
    return nullptr;
}

bool MapManager::CanPKInArea(const std::string& mapName, int x, int y) const {
    SafeZoneConfig* zone = const_cast<MapManager*>(this)->GetSafeZone(mapName, x, y);
    if (zone) {
        return zone->allowPK;
    }
    
    // 默认允许PK
    return true;
}

bool MapManager::CanTradeInArea(const std::string& mapName, int x, int y) const {
    SafeZoneConfig* zone = const_cast<MapManager*>(this)->GetSafeZone(mapName, x, y);
    if (zone) {
        return zone->allowTrade;
    }
    
    // 默认允许交易
    return true;
}

bool MapManager::CanDropInArea(const std::string& mapName, int x, int y) const {
    SafeZoneConfig* zone = const_cast<MapManager*>(this)->GetSafeZone(mapName, x, y);
    if (zone) {
        return zone->allowDrop;
    }
    
    // 默认允许丢弃
    return true;
}

void MapManager::ProcessSpawns() {
    std::shared_lock<std::shared_mutex> lock(m_spawnMutex);
    
    for (const auto& mapPair : m_spawnConfigs) {
        ProcessMapSpawns(mapPair.first);
    }
}

bool MapManager::SpawnMonster(const std::string& mapName, const std::string& monsterName, int x, int y) {
    // 检查位置是否有效
    if (!CanWalk(mapName, x, y)) {
        return false;
    }
    
    // 这里需要通过MonsterManager创建怪物
    // 暂时只记录日志
    ++m_totalSpawns;
    LogMapOperation("SpawnMonster", "Monster " + monsterName + " spawned at " + mapName);
    return true;
}

int MapManager::GetMonsterCount(const std::string& mapName, const std::string& monsterName) const {
    // 这里需要统计地图上指定怪物的数量
    // 暂时返回0
    return 0;
}

void MapManager::AddSpawnConfig(const MapSpawnConfig& config) {
    std::unique_lock<std::shared_mutex> lock(m_spawnMutex);
    m_spawnConfigs[config.mapName].push_back(config);
}

void MapManager::RemoveSpawnConfig(const std::string& mapName, const std::string& monsterName) {
    std::unique_lock<std::shared_mutex> lock(m_spawnMutex);
    
    auto it = m_spawnConfigs.find(mapName);
    if (it != m_spawnConfigs.end()) {
        auto& configs = it->second;
        configs.erase(std::remove_if(configs.begin(), configs.end(),
            [&monsterName](const MapSpawnConfig& config) {
                return config.monsterName == monsterName;
            }), configs.end());
    }
}

bool MapManager::IsValidPosition(const std::string& mapName, int x, int y) const {
    Environment* env = const_cast<MapManager*>(this)->GetEnvironment(mapName);
    if (!env) return false;
    
    return ValidatePosition(env, x, y);
}

Point MapManager::FindRandomWalkablePosition(const std::string& mapName, const Point& center, int range) const {
    std::vector<Point> walkablePositions = GetWalkablePositions(mapName, center, range);
    
    if (walkablePositions.empty()) {
        return {-1, -1}; // 无效位置
    }
    
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, walkablePositions.size() - 1);
    
    return walkablePositions[dis(gen)];
}

std::vector<Point> MapManager::GetWalkablePositions(const std::string& mapName, const Point& center, int range) const {
    std::vector<Point> positions;
    
    for (int x = center.x - range; x <= center.x + range; ++x) {
        for (int y = center.y - range; y <= center.y + range; ++y) {
            if (CanWalk(mapName, x, y)) {
                positions.push_back({x, y});
            }
        }
    }
    
    return positions;
}

bool MapManager::AddObjectToMap(const std::string& mapName, BaseObject* obj) {
    Environment* env = GetEnvironment(mapName);
    if (!env || !obj) return false;
    
    // 这里需要将对象添加到地图
    // env->AddObject(obj);
    
    LogMapOperation("AddObjectToMap", "Object added to " + mapName);
    return true;
}

bool MapManager::RemoveObjectFromMap(const std::string& mapName, BaseObject* obj) {
    Environment* env = GetEnvironment(mapName);
    if (!env || !obj) return false;
    
    // 这里需要从地图移除对象
    // env->RemoveObject(obj);
    
    LogMapOperation("RemoveObjectFromMap", "Object removed from " + mapName);
    return true;
}

std::vector<BaseObject*> MapManager::GetNearbyObjects(const std::string& mapName, int x, int y, int range) const {
    Point center = {x, y};
    return const_cast<MapManager*>(this)->GetObjectsInRange(mapName, center, range);
}

void MapManager::AddGate(const MapGateConfig& gate) {
    std::unique_lock<std::shared_mutex> lock(m_gateMutex);
    m_mapGates[gate.fromMap].push_back(gate);
}

void MapManager::RemoveGate(const std::string& mapName, int x, int y) {
    std::unique_lock<std::shared_mutex> lock(m_gateMutex);
    
    auto it = m_mapGates.find(mapName);
    if (it != m_mapGates.end()) {
        auto& gates = it->second;
        gates.erase(std::remove_if(gates.begin(), gates.end(),
            [x, y](const MapGateConfig& gate) {
                return gate.fromX == x && gate.fromY == y;
            }), gates.end());
    }
}

void MapManager::AddSafeZone(const SafeZoneConfig& safeZone) {
    std::unique_lock<std::shared_mutex> lock(m_safeMutex);
    m_safeZones[safeZone.mapName].push_back(safeZone);
}

void MapManager::RemoveSafeZone(const std::string& mapName, int x, int y) {
    std::unique_lock<std::shared_mutex> lock(m_safeMutex);
    
    auto it = m_safeZones.find(mapName);
    if (it != m_safeZones.end()) {
        auto& zones = it->second;
        zones.erase(std::remove_if(zones.begin(), zones.end(),
            [x, y](const SafeZoneConfig& zone) {
                return zone.x == x && zone.y == y;
            }), zones.end());
    }
}

bool MapManager::LoadMapFromFile(const std::string& mapName) {
    // 这里应该加载具体的地图文件
    // 创建Environment对象并加载地图数据
    
    auto environment = std::make_unique<Environment>();
    // environment->LoadFromFile(mapName + ".map");
    
    if (ValidateMapData(environment.get())) {
        m_environments[mapName] = std::move(environment);
        return true;
    }
    
    return false;
}

bool MapManager::LoadGateConfigFromFile(const std::string& fileName) {
    std::ifstream file(fileName);
    if (!file.is_open()) {
        LogMapError("LoadGateConfigFromFile", "Failed to open file: " + fileName);
        return false;
    }
    
    std::string line;
    while (std::getline(file, line)) {
        if (line.empty() || line[0] == ';') continue;
        
        MapGateConfig config;
        if (ParseGateConfigLine(line, config)) {
            AddGate(config);
        }
    }
    
    LogMapOperation("LoadGateConfigFromFile", "Gate config loaded from " + fileName);
    return true;
}

bool MapManager::LoadSafeZoneConfigFromFile(const std::string& fileName) {
    std::ifstream file(fileName);
    if (!file.is_open()) {
        LogMapError("LoadSafeZoneConfigFromFile", "Failed to open file: " + fileName);
        return false;
    }
    
    std::string line;
    while (std::getline(file, line)) {
        if (line.empty() || line[0] == ';') continue;
        
        SafeZoneConfig config;
        if (ParseSafeZoneConfigLine(line, config)) {
            AddSafeZone(config);
        }
    }
    
    LogMapOperation("LoadSafeZoneConfigFromFile", "SafeZone config loaded from " + fileName);
    return true;
}

bool MapManager::LoadSpawnConfigFromFile(const std::string& fileName) {
    std::ifstream file(fileName);
    if (!file.is_open()) {
        LogMapError("LoadSpawnConfigFromFile", "Failed to open file: " + fileName);
        return false;
    }
    
    std::string line;
    while (std::getline(file, line)) {
        if (line.empty() || line[0] == ';') continue;
        
        MapSpawnConfig config;
        if (ParseSpawnConfigLine(line, config)) {
            AddSpawnConfig(config);
        }
    }
    
    LogMapOperation("LoadSpawnConfigFromFile", "Spawn config loaded from " + fileName);
    return true;
}

bool MapManager::ParseGateConfigLine(const std::string& line, MapGateConfig& config) {
    std::istringstream iss(line);
    
    // 解析格式: FromMap FromX FromY ToMap ToX ToY MinLevel MaxLevel Cost NeedItem ItemIndex
    if (!(iss >> config.fromMap >> config.fromX >> config.fromY >> 
          config.toMap >> config.toX >> config.toY >> 
          config.minLevel >> config.maxLevel >> config.cost >> 
          config.needItem >> config.itemIndex)) {
        return false;
    }
    
    return true;
}

bool MapManager::ParseSafeZoneConfigLine(const std::string& line, SafeZoneConfig& config) {
    std::istringstream iss(line);
    
    // 解析格式: MapName X Y Range AllowPK AllowTrade AllowDrop
    if (!(iss >> config.mapName >> config.x >> config.y >> config.range >> 
          config.allowPK >> config.allowTrade >> config.allowDrop)) {
        return false;
    }
    
    return true;
}

bool MapManager::ParseSpawnConfigLine(const std::string& line, MapSpawnConfig& config) {
    std::istringstream iss(line);
    
    // 解析格式: MapName MonsterName X Y Range Count Interval MaxCount
    if (!(iss >> config.mapName >> config.monsterName >> config.x >> config.y >> 
          config.range >> config.count >> config.interval >> config.maxCount)) {
        return false;
    }
    
    return true;
}

bool MapManager::ValidateMapData(Environment* env) const {
    // 验证地图数据的有效性
    return (env != nullptr);
}

bool MapManager::ValidatePosition(Environment* env, int x, int y) const {
    if (!env) return false;
    
    // 检查坐标是否在地图范围内
    // return env->IsValidPosition(x, y);
    return true; // 暂时返回true
}

void MapManager::ProcessMapSpawns(const std::string& mapName) {
    auto it = m_spawnConfigs.find(mapName);
    if (it == m_spawnConfigs.end()) return;
    
    for (const auto& config : it->second) {
        if (ShouldSpawnMonster(config)) {
            Point spawnPos = FindSpawnPosition(mapName, config);
            if (spawnPos.x != -1) {
                SpawnMonster(mapName, config.monsterName, spawnPos.x, spawnPos.y);
            }
        }
    }
}

bool MapManager::ShouldSpawnMonster(const MapSpawnConfig& config) const {
    // 检查当前怪物数量是否达到上限
    int currentCount = GetMonsterCount(config.mapName, config.monsterName);
    if (currentCount >= config.maxCount) {
        return false;
    }
    
    // 检查刷新间隔
    // 这里需要实现时间检查逻辑
    return true;
}

Point MapManager::FindSpawnPosition(const std::string& mapName, const MapSpawnConfig& config) const {
    Point center = {config.x, config.y};
    return FindRandomWalkablePosition(mapName, center, config.range);
}

void MapManager::HandlePlayerMapChange(const MapChangeEventData& data) {
    LogMapOperation("HandlePlayerMapChange", 
        "Player " + data.playerName + " moved from " + data.fromMap + " to " + data.toMap);
}

void MapManager::HandleMonsterKilled(const MonsterKilledEventData& data) {
    LogMapOperation("HandleMonsterKilled", 
        "Monster " + data.monsterName + " killed in " + data.mapName);
}

void MapManager::HandleObjectSpawned(const ObjectSpawnedEventData& data) {
    LogMapOperation("HandleObjectSpawned", "Object spawned in " + data.mapName);
}

void MapManager::LogMapOperation(const std::string& operation, const std::string& details) const {
    std::cout << "[MapManager] " << operation << ": " << details << std::endl;
}

void MapManager::LogMapError(const std::string& operation, const std::string& error) const {
    std::cerr << "[MapManager] ERROR in " << operation << ": " << error << std::endl;
}
