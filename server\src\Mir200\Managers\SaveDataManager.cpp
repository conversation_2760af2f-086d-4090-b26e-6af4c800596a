#include "SaveDataManager.h"
#include "../Objects/PlayObject.h"
#include "../Common/M2Share.h"
#include <filesystem>
#include <fstream>
#include <iostream>
#include <sstream>
#include <chrono>

SaveDataManager::SaveDataManager() 
    : m_taskQueue([](const std::shared_ptr<SaveTask>& a, const std::shared_ptr<SaveTask>& b) {
        return TaskComparator(a, b);
      })
    , m_running(false)
    , m_nextTaskId(1)
    , m_totalTasks(0)
    , m_completedTasks(0)
    , m_failedTasks(0)
    , m_totalSaveTime(0)
    , m_maxQueueSize(10000)
    , m_saveTimeout(30000)
    , m_dataDirectory("data/")
    , m_backupDirectory("backup/")
    , m_managerName("SaveDataManager")
    , m_initialized(false) {
}

SaveDataManager::~SaveDataManager() {
    Finalize();
}

bool SaveDataManager::Initialize() {
    std::cout << "[SaveDataManager] Initializing..." << std::endl;
    
    // 创建数据目录
    try {
        std::filesystem::create_directories(m_dataDirectory);
        std::filesystem::create_directories(m_backupDirectory);
    } catch (const std::exception& e) {
        std::cerr << "[SaveDataManager] Failed to create directories: " << e.what() << std::endl;
        return false;
    }
    
    // 启动工作线程
    m_running = true;
    m_workerThread = std::thread(&SaveDataManager::WorkerThreadFunction, this);
    
    // 设置默认自动保存配置
    SetAutoSave(SaveDataType::PLAYER_DATA, true, 300000, nullptr); // 5分钟
    SetAutoSave(SaveDataType::GUILD_DATA, true, 600000, nullptr);  // 10分钟
    SetAutoSave(SaveDataType::SYSTEM_DATA, true, 900000, nullptr); // 15分钟
    
    m_initialized = true;
    
    std::cout << "[SaveDataManager] Initialized successfully" << std::endl;
    return true;
}

void SaveDataManager::Finalize() {
    if (!m_initialized) return;
    
    std::cout << "[SaveDataManager] Finalizing..." << std::endl;
    
    // 停止工作线程
    m_running = false;
    m_queueCondition.notify_all();
    
    if (m_workerThread.joinable()) {
        m_workerThread.join();
    }
    
    // 清理队列
    {
        std::lock_guard<std::mutex> lock(m_queueMutex);
        while (!m_taskQueue.empty()) {
            m_taskQueue.pop();
        }
    }
    
    // 清理活动任务
    {
        std::lock_guard<std::mutex> lock(m_taskMutex);
        m_activeTasks.clear();
    }
    
    m_initialized = false;
    
    std::cout << "[SaveDataManager] Finalized" << std::endl;
}

void SaveDataManager::Update() {
    if (!m_initialized) return;
    
    ProcessAutoSave();
}

const std::string& SaveDataManager::GetManagerName() const {
    return m_managerName;
}

uint32_t SaveDataManager::QueueSave(SaveDataType dataType, const std::string& fileName, 
                                   const std::string& data, SavePriority priority) {
    if (!m_initialized) return 0;
    
    if (IsQueueFull()) {
        std::cerr << "[SaveDataManager] Queue is full, cannot add new task" << std::endl;
        return 0;
    }
    
    auto task = std::make_shared<SaveTask>();
    task->taskId = GenerateTaskId();
    task->dataType = dataType;
    task->priority = priority;
    task->fileName = fileName;
    task->data = data;
    task->createTime = GetCurrentTime();
    task->deadline = task->createTime + m_saveTimeout;
    
    {
        std::lock_guard<std::mutex> lock(m_queueMutex);
        m_taskQueue.push(task);
    }
    
    {
        std::lock_guard<std::mutex> lock(m_taskMutex);
        m_activeTasks[task->taskId] = task;
    }
    
    m_queueCondition.notify_one();
    ++m_totalTasks;
    
    std::cout << "[SaveDataManager] Queued save task " << task->taskId 
              << " for file: " << fileName << std::endl;
    
    return task->taskId;
}

uint32_t SaveDataManager::QueueSave(SaveDataType dataType, std::function<bool()> saveFunction, 
                                   SavePriority priority) {
    if (!m_initialized) return 0;
    
    if (IsQueueFull()) {
        std::cerr << "[SaveDataManager] Queue is full, cannot add new task" << std::endl;
        return 0;
    }
    
    auto task = std::make_shared<SaveTask>();
    task->taskId = GenerateTaskId();
    task->dataType = dataType;
    task->priority = priority;
    task->saveFunction = std::move(saveFunction);
    task->createTime = GetCurrentTime();
    task->deadline = task->createTime + m_saveTimeout;
    
    {
        std::lock_guard<std::mutex> lock(m_queueMutex);
        m_taskQueue.push(task);
    }
    
    {
        std::lock_guard<std::mutex> lock(m_taskMutex);
        m_activeTasks[task->taskId] = task;
    }
    
    m_queueCondition.notify_one();
    ++m_totalTasks;
    
    std::cout << "[SaveDataManager] Queued function save task " << task->taskId << std::endl;
    
    return task->taskId;
}

uint32_t SaveDataManager::SavePlayerData(const std::string& playerName, const std::string& data, 
                                        SavePriority priority) {
    std::string fileName = "players/" + playerName + ".dat";
    return QueueSave(SaveDataType::PLAYER_DATA, fileName, data, priority);
}

bool SaveDataManager::SaveImmediate(SaveDataType dataType, const std::string& fileName, const std::string& data) {
    if (!m_initialized) return false;
    
    SaveTask task;
    task.dataType = dataType;
    task.fileName = fileName;
    task.data = data;
    task.createTime = GetCurrentTime();
    
    return ExecuteSaveTask(task);
}

void SaveDataManager::SetAutoSave(SaveDataType dataType, bool enabled, DWORD interval, std::function<bool()> saveFunction) {
    std::lock_guard<std::mutex> lock(m_autoSaveMutex);
    
    AutoSaveConfig& config = m_autoSaveConfigs[dataType];
    config.enabled = enabled;
    config.interval = interval;
    config.dataType = dataType;
    config.saveFunction = std::move(saveFunction);
    config.lastSaveTime = GetCurrentTime();
    
    std::cout << "[SaveDataManager] Set auto save for type " << GetDataTypeString(dataType) 
              << " enabled=" << enabled << " interval=" << interval << "ms" << std::endl;
}

bool SaveDataManager::IsQueueFull() const {
    std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(m_queueMutex));
    return m_taskQueue.size() >= m_maxQueueSize;
}

size_t SaveDataManager::GetQueueSize() const {
    std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(m_queueMutex));
    return m_taskQueue.size();
}

void SaveDataManager::FlushAll() {
    std::unique_lock<std::mutex> lock(m_queueMutex);
    
    while (!m_taskQueue.empty()) {
        m_queueCondition.wait(lock);
    }
    
    std::cout << "[SaveDataManager] Flushed all pending tasks" << std::endl;
}

uint64_t SaveDataManager::GetAverageSaveTime() const {
    uint64_t completed = m_completedTasks.load();
    return completed > 0 ? m_totalSaveTime.load() / completed : 0;
}

double SaveDataManager::GetSuccessRate() const {
    uint64_t total = m_totalTasks.load();
    uint64_t completed = m_completedTasks.load();
    return total > 0 ? (double)completed / total * 100.0 : 0.0;
}

void SaveDataManager::WorkerThreadFunction() {
    while (m_running) {
        std::shared_ptr<SaveTask> task;
        
        {
            std::unique_lock<std::mutex> lock(m_queueMutex);
            m_queueCondition.wait(lock, [this] { return !m_taskQueue.empty() || !m_running; });
            
            if (!m_running) break;
            
            if (!m_taskQueue.empty()) {
                task = m_taskQueue.top();
                m_taskQueue.pop();
            }
        }
        
        if (task) {
            ProcessTask(task);
        }
    }
}

void SaveDataManager::ProcessTask(std::shared_ptr<SaveTask> task) {
    auto startTime = std::chrono::high_resolution_clock::now();
    
    bool success = ExecuteSaveTask(*task);
    
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
    
    m_totalSaveTime += duration.count();
    
    CompleteTask(*task, success);
}

bool SaveDataManager::ExecuteSaveTask(SaveTask& task) {
    try {
        if (task.saveFunction) {
            return task.saveFunction();
        } else if (!task.fileName.empty() && !task.data.empty()) {
            return SaveToFile(task.fileName, task.data);
        }
        return false;
    } catch (const std::exception& e) {
        task.errorMessage = e.what();
        return false;
    }
}

void SaveDataManager::CompleteTask(SaveTask& task, bool success) {
    task.completed = true;
    
    if (success) {
        ++m_completedTasks;
        std::cout << "[SaveDataManager] Task " << task.taskId << " completed successfully" << std::endl;
    } else {
        ++m_failedTasks;
        std::cerr << "[SaveDataManager] Task " << task.taskId << " failed: " << task.errorMessage << std::endl;
    }
    
    // 从活动任务中移除
    {
        std::lock_guard<std::mutex> lock(m_taskMutex);
        m_activeTasks.erase(task.taskId);
    }
}

bool SaveDataManager::SaveToFile(const std::string& fileName, const std::string& data) {
    std::string fullPath = GetFullPath(fileName, SaveDataType::PLAYER_DATA);
    
    // 创建目录
    std::filesystem::path filePath(fullPath);
    std::filesystem::create_directories(filePath.parent_path());
    
    // 写入文件
    std::ofstream file(fullPath, std::ios::binary);
    if (!file.is_open()) {
        return false;
    }
    
    file.write(data.c_str(), data.size());
    return file.good();
}

std::string SaveDataManager::GetFullPath(const std::string& fileName, SaveDataType dataType) const {
    return m_dataDirectory + fileName;
}

uint32_t SaveDataManager::GenerateTaskId() {
    return m_nextTaskId.fetch_add(1);
}

std::string SaveDataManager::GetDataTypeString(SaveDataType dataType) const {
    switch (dataType) {
        case SaveDataType::PLAYER_DATA: return "PLAYER_DATA";
        case SaveDataType::GUILD_DATA: return "GUILD_DATA";
        case SaveDataType::CASTLE_DATA: return "CASTLE_DATA";
        case SaveDataType::MARKET_DATA: return "MARKET_DATA";
        case SaveDataType::SYSTEM_DATA: return "SYSTEM_DATA";
        case SaveDataType::LOG_DATA: return "LOG_DATA";
        case SaveDataType::BACKUP_FILE_DATA: return "BACKUP_FILE_DATA";
        default: return "UNKNOWN";
    }
}

void SaveDataManager::ProcessAutoSave() {
    std::lock_guard<std::mutex> lock(m_autoSaveMutex);
    
    DWORD currentTime = GetCurrentTime();
    
    for (auto& pair : m_autoSaveConfigs) {
        AutoSaveConfig& config = pair.second;
        
        if (ShouldAutoSave(config)) {
            if (config.saveFunction) {
                QueueSave(config.dataType, config.saveFunction, SavePriority::LOW);
            }
            config.lastSaveTime = currentTime;
        }
    }
}

bool SaveDataManager::ShouldAutoSave(const AutoSaveConfig& config) const {
    if (!config.enabled) return false;
    
    DWORD currentTime = GetCurrentTime();
    return (currentTime - config.lastSaveTime) >= config.interval;
}

bool SaveDataManager::TaskComparator(const std::shared_ptr<SaveTask>& a, const std::shared_ptr<SaveTask>& b) {
    // 优先级高的任务优先执行（注意：priority_queue是最大堆，所以这里返回相反的比较结果）
    if (a->priority != b->priority) {
        return a->priority < b->priority;
    }
    
    // 相同优先级按创建时间排序
    return a->createTime > b->createTime;
}
