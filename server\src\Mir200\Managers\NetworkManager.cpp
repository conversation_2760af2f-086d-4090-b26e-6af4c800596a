#include "NetworkManager.h"
#include "../Network/RunSocket.h"
#include "../Common/M2Share.h"
#include <iostream>
#include <algorithm>
#include <sstream>

NetworkManager::NetworkManager() 
    : m_nextConnectionId(1)
    , m_server<PERSON><PERSON>ress("127.0.0.1")
    , m_serverPort(7100)
    , m_gatePort(7200)
    , m_maxConnections(1000)
    , m_connectionTimeout(30000)
    , m_messageTimeout(5000)
    , m_managerName("NetworkManager")
    , m_initialized(false)
    , m_running(false) {
}

NetworkManager::~NetworkManager() {
    Finalize();
}

bool NetworkManager::Initialize() {
    std::cout << "[NetworkManager] Initializing..." << std::endl;
    
    // 创建网络组件
    m_runSocket = std::make_unique<RunSocket>();
    
    // 初始化网络组件
    if (!m_runSocket->Initialize()) {
        std::cerr << "[NetworkManager] Failed to initialize RunSocket" << std::endl;
        return false;
    }
    
    // 重置统计信息
    {
        std::lock_guard<std::mutex> lock(m_statisticsMutex);
        m_statistics = NetworkStatistics();
    }
    
    // 清理连接和消息队列
    {
        std::lock_guard<std::mutex> lock(m_connectionMutex);
        m_connections.clear();
    }
    
    {
        std::lock_guard<std::mutex> lock(m_messageMutex);
        while (!m_incomingMessages.empty()) m_incomingMessages.pop();
        while (!m_outgoingMessages.empty()) m_outgoingMessages.pop();
    }
    
    m_nextConnectionId = 1;
    m_initialized = true;
    
    std::cout << "[NetworkManager] Initialized successfully" << std::endl;
    return true;
}

void NetworkManager::Finalize() {
    if (!m_initialized) return;
    
    std::cout << "[NetworkManager] Finalizing..." << std::endl;
    
    // 停止网络服务
    StopNetworkServices();
    
    // 关闭所有连接
    {
        std::lock_guard<std::mutex> lock(m_connectionMutex);
        for (auto& pair : m_connections) {
            OnConnectionClosed(pair.first);
        }
        m_connections.clear();
    }
    
    // 清理消息队列
    {
        std::lock_guard<std::mutex> lock(m_messageMutex);
        while (!m_incomingMessages.empty()) m_incomingMessages.pop();
        while (!m_outgoingMessages.empty()) m_outgoingMessages.pop();
    }
    
    // 清理消息处理器
    {
        std::lock_guard<std::mutex> lock(m_handlerMutex);
        m_messageHandlers.clear();
    }
    
    // 清理网络组件
    if (m_runSocket) {
        m_runSocket->Finalize();
        m_runSocket.reset();
    }
    
    m_initialized = false;
    
    std::cout << "[NetworkManager] Finalized" << std::endl;
}

void NetworkManager::Update() {
    if (!m_initialized) return;
    
    // 处理网络消息
    if (m_runSocket) {
        m_runSocket->ProcessMessages();
    }
    
    // 处理消息队列
    ProcessIncomingMessages();
    ProcessOutgoingMessages();
    
    // 检查超时
    CheckConnectionTimeouts();
    CheckMessageTimeouts();
}

const std::string& NetworkManager::GetManagerName() const {
    return m_managerName;
}

bool NetworkManager::StartNetworkServices() {
    if (!m_initialized) {
        std::cerr << "[NetworkManager] Not initialized" << std::endl;
        return false;
    }
    
    if (m_running) {
        std::cout << "[NetworkManager] Network services already running" << std::endl;
        return true;
    }
    
    std::cout << "[NetworkManager] Starting network services..." << std::endl;
    
    // 启动RunSocket
    if (m_runSocket && !m_runSocket->Start()) {
        std::cerr << "[NetworkManager] Failed to start RunSocket" << std::endl;
        return false;
    }
    
    m_running = true;
    
    std::cout << "[NetworkManager] Network services started successfully" << std::endl;
    std::cout << "[NetworkManager] Server listening on " << m_serverAddress << ":" << m_serverPort << std::endl;
    
    return true;
}

void NetworkManager::StopNetworkServices() {
    if (!m_running) {
        std::cout << "[NetworkManager] Network services not running" << std::endl;
        return;
    }
    
    std::cout << "[NetworkManager] Stopping network services..." << std::endl;
    
    // 停止RunSocket
    if (m_runSocket) {
        m_runSocket->Stop();
    }
    
    m_running = false;
    
    std::cout << "[NetworkManager] Network services stopped" << std::endl;
}

uint32_t NetworkManager::CreateConnection(ConnectionType type, const std::string& remoteAddress, uint16_t remotePort) {
    std::lock_guard<std::mutex> lock(m_connectionMutex);
    
    // 检查连接数限制
    if (m_connections.size() >= m_maxConnections) {
        std::cerr << "[NetworkManager] Connection limit reached" << std::endl;
        return 0;
    }
    
    // 检查IP是否被阻止
    if (IsIPBlocked(remoteAddress)) {
        std::cerr << "[NetworkManager] IP " << remoteAddress << " is blocked" << std::endl;
        return 0;
    }
    
    uint32_t connectionId = GenerateConnectionId();
    auto connection = std::make_unique<ConnectionInfo>();
    
    connection->connectionId = connectionId;
    connection->type = type;
    connection->state = ConnectionState::CONNECTING;
    connection->remoteAddress = remoteAddress;
    connection->remotePort = remotePort;
    connection->connectTime = GetCurrentTime();
    connection->lastActiveTime = connection->connectTime;
    
    m_connections[connectionId] = std::move(connection);
    
    // 更新统计信息
    {
        std::lock_guard<std::mutex> statsLock(m_statisticsMutex);
        ++m_statistics.totalConnections;
        ++m_statistics.activeConnections;
    }
    
    std::cout << "[NetworkManager] Created connection " << connectionId 
              << " from " << remoteAddress << ":" << remotePort << std::endl;
    
    OnConnectionEstablished(connectionId, remoteAddress, remotePort);
    
    return connectionId;
}

bool NetworkManager::CloseConnection(uint32_t connectionId) {
    std::lock_guard<std::mutex> lock(m_connectionMutex);
    
    auto it = m_connections.find(connectionId);
    if (it == m_connections.end()) {
        return false;
    }
    
    auto& connection = it->second;
    
    std::cout << "[NetworkManager] Closing connection " << connectionId 
              << " (" << connection->remoteAddress << ":" << connection->remotePort << ")" << std::endl;
    
    // 更新统计信息
    {
        std::lock_guard<std::mutex> statsLock(m_statisticsMutex);
        if (m_statistics.activeConnections > 0) {
            --m_statistics.activeConnections;
        }
    }
    
    OnConnectionClosed(connectionId);
    m_connections.erase(it);
    
    return true;
}

bool NetworkManager::IsConnectionActive(uint32_t connectionId) const {
    std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(m_connectionMutex));

    auto it = m_connections.find(connectionId);
    if (it == m_connections.end()) {
        return false;
    }

    return it->second->state == ConnectionState::CONNECTED ||
           it->second->state == ConnectionState::AUTHENTICATED;
}

ConnectionInfo* NetworkManager::GetConnectionInfo(uint32_t connectionId) {
    std::lock_guard<std::mutex> lock(m_connectionMutex);
    
    auto it = m_connections.find(connectionId);
    return (it != m_connections.end()) ? it->second.get() : nullptr;
}

bool NetworkManager::SendMessage(uint32_t connectionId, uint16_t messageType, const std::vector<uint8_t>& data) {
    if (!IsConnectionActive(connectionId)) {
        return false;
    }
    
    auto message = std::make_unique<NetworkMessage>();
    message->messageId = GenerateConnectionId(); // 重用ID生成器
    message->messageType = messageType;
    message->connectionId = connectionId;
    message->data = data;
    message->timestamp = GetCurrentTime();
    
    {
        std::lock_guard<std::mutex> lock(m_messageMutex);
        m_outgoingMessages.push(std::move(message));
    }
    
    return true;
}

bool NetworkManager::SendMessage(uint32_t connectionId, uint16_t messageType, const void* data, size_t size) {
    std::vector<uint8_t> dataVector(static_cast<const uint8_t*>(data), 
                                   static_cast<const uint8_t*>(data) + size);
    return SendMessage(connectionId, messageType, dataVector);
}

bool NetworkManager::BroadcastMessage(uint16_t messageType, const std::vector<uint8_t>& data, ConnectionType targetType) {
    std::lock_guard<std::mutex> lock(m_connectionMutex);
    
    bool success = true;
    
    for (const auto& pair : m_connections) {
        const auto& connection = pair.second;
        
        if (connection->type == targetType && IsConnectionActive(connection->connectionId)) {
            if (!SendMessage(connection->connectionId, messageType, data)) {
                success = false;
            }
        }
    }
    
    return success;
}

void NetworkManager::RegisterMessageHandler(uint16_t messageType, std::function<void(const NetworkMessage&)> handler) {
    std::lock_guard<std::mutex> lock(m_handlerMutex);
    m_messageHandlers[messageType] = std::move(handler);
    
    std::cout << "[NetworkManager] Registered handler for message type " << messageType << std::endl;
}

void NetworkManager::ProcessIncomingMessages() {
    std::queue<std::unique_ptr<NetworkMessage>> messagesToProcess;
    
    {
        std::lock_guard<std::mutex> lock(m_messageMutex);
        messagesToProcess.swap(m_incomingMessages);
    }
    
    while (!messagesToProcess.empty()) {
        auto message = std::move(messagesToProcess.front());
        messagesToProcess.pop();
        
        HandleIncomingMessage(std::move(message));
    }
}

void NetworkManager::ProcessOutgoingMessages() {
    std::queue<std::unique_ptr<NetworkMessage>> messagesToProcess;
    
    {
        std::lock_guard<std::mutex> lock(m_messageMutex);
        messagesToProcess.swap(m_outgoingMessages);
    }
    
    while (!messagesToProcess.empty()) {
        auto message = std::move(messagesToProcess.front());
        messagesToProcess.pop();
        
        HandleOutgoingMessage(std::move(message));
    }
}

void NetworkManager::SetConnectionState(uint32_t connectionId, ConnectionState state) {
    std::lock_guard<std::mutex> lock(m_connectionMutex);
    
    auto it = m_connections.find(connectionId);
    if (it != m_connections.end()) {
        it->second->state = state;
        it->second->lastActiveTime = GetCurrentTime();
        
        std::cout << "[NetworkManager] Connection " << connectionId 
                  << " state changed to " << GetConnectionStateString(state) << std::endl;
    }
}

void NetworkManager::SetConnectionUserInfo(uint32_t connectionId, const std::string& account, const std::string& playerName) {
    std::lock_guard<std::mutex> lock(m_connectionMutex);

    auto it = m_connections.find(connectionId);
    if (it != m_connections.end()) {
        it->second->userAccount = account;
        it->second->playerName = playerName;
        it->second->lastActiveTime = GetCurrentTime();

        std::cout << "[NetworkManager] Connection " << connectionId
                  << " user info set: " << account << "/" << playerName << std::endl;
    }
}

void NetworkManager::UpdateConnectionActivity(uint32_t connectionId) {
    std::lock_guard<std::mutex> lock(m_connectionMutex);

    auto it = m_connections.find(connectionId);
    if (it != m_connections.end()) {
        it->second->lastActiveTime = GetCurrentTime();
    }
}

NetworkStatistics NetworkManager::GetStatistics() const {
    std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(m_statisticsMutex));
    return m_statistics;
}

uint32_t NetworkManager::GetActiveConnectionCount() const {
    std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(m_connectionMutex));

    uint32_t count = 0;
    for (const auto& pair : m_connections) {
        if (IsConnectionActive(pair.first)) {
            ++count;
        }
    }

    return count;
}

void NetworkManager::HandleIncomingMessage(std::unique_ptr<NetworkMessage> message) {
    UpdateStatistics(*message, true);
    UpdateConnectionActivity(message->connectionId);
    ProcessMessage(*message);
}

void NetworkManager::HandleOutgoingMessage(std::unique_ptr<NetworkMessage> message) {
    UpdateStatistics(*message, false);
    UpdateConnectionActivity(message->connectionId);
    
    // 这里应该实际发送消息到网络
    // 目前只是模拟发送
    std::cout << "[NetworkManager] Sending message type " << message->messageType 
              << " to connection " << message->connectionId << std::endl;
}

void NetworkManager::ProcessMessage(const NetworkMessage& message) {
    std::lock_guard<std::mutex> lock(m_handlerMutex);
    
    auto it = m_messageHandlers.find(message.messageType);
    if (it != m_messageHandlers.end()) {
        try {
            it->second(message);
        } catch (const std::exception& e) {
            std::cerr << "[NetworkManager] Error processing message type " 
                      << message.messageType << ": " << e.what() << std::endl;
        }
    }
}

void NetworkManager::OnConnectionEstablished(uint32_t connectionId, const std::string& remoteAddress, uint16_t remotePort) {
    std::cout << "[NetworkManager] Connection established: " << connectionId 
              << " from " << remoteAddress << ":" << remotePort << std::endl;
}

void NetworkManager::OnConnectionClosed(uint32_t connectionId) {
    std::cout << "[NetworkManager] Connection closed: " << connectionId << std::endl;
}

void NetworkManager::UpdateStatistics(const NetworkMessage& message, bool incoming) {
    std::lock_guard<std::mutex> lock(m_statisticsMutex);
    
    if (incoming) {
        ++m_statistics.totalMessagesReceived;
        m_statistics.totalBytesReceived += message.data.size();
    } else {
        ++m_statistics.totalMessagesSent;
        m_statistics.totalBytesSent += message.data.size();
    }
}

void NetworkManager::CheckConnectionTimeouts() {
    std::lock_guard<std::mutex> lock(m_connectionMutex);
    
    DWORD currentTime = GetCurrentTime();
    std::vector<uint32_t> timedOutConnections;
    
    for (const auto& pair : m_connections) {
        const auto& connection = pair.second;
        
        if (currentTime - connection->lastActiveTime > m_connectionTimeout) {
            timedOutConnections.push_back(connection->connectionId);
        }
    }
    
    // 关闭超时连接
    for (uint32_t connectionId : timedOutConnections) {
        std::cout << "[NetworkManager] Connection " << connectionId << " timed out" << std::endl;
        CloseConnection(connectionId);
    }
}

uint32_t NetworkManager::GenerateConnectionId() {
    return m_nextConnectionId.fetch_add(1);
}

std::string NetworkManager::GetConnectionTypeString(ConnectionType type) const {
    switch (type) {
        case ConnectionType::GAME_CLIENT: return "GAME_CLIENT";
        case ConnectionType::GATE_SERVER: return "GATE_SERVER";
        case ConnectionType::DB_SERVER: return "DB_SERVER";
        case ConnectionType::LOGIN_SERVER: return "LOGIN_SERVER";
        case ConnectionType::ADMIN_CLIENT: return "ADMIN_CLIENT";
        default: return "UNKNOWN";
    }
}

std::string NetworkManager::GetConnectionStateString(ConnectionState state) const {
    switch (state) {
        case ConnectionState::DISCONNECTED: return "DISCONNECTED";
        case ConnectionState::CONNECTING: return "CONNECTING";
        case ConnectionState::CONNECTED: return "CONNECTED";
        case ConnectionState::AUTHENTICATED: return "AUTHENTICATED";
        case ConnectionState::ERROR_STATE: return "ERROR_STATE";
        default: return "UNKNOWN";
    }
}

bool NetworkManager::IsIPBlocked(const std::string& ipAddress) const {
    std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(m_ipBlockMutex));

    auto it = m_blockedIPs.find(ipAddress);
    if (it == m_blockedIPs.end()) {
        return false;
    }

    // 检查是否过期（0表示永久阻止）
    if (it->second == 0) {
        return true;
    }

    return GetCurrentTime() < it->second;
}

void NetworkManager::CheckMessageTimeouts() {
    // 消息超时检查的实现
    // 这里可以检查消息队列中的消息是否超时
}
