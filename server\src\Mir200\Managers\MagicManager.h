#pragma once

#include "IManager.h"
#include "EventData.h"
#include "../Common/Types.h"
#include <string>
#include <vector>
#include <unordered_map>
#include <mutex>
#include <shared_mutex>
#include <memory>

// 前向声明
class EventBus;
class BaseObject;
class PlayObject;

/**
 * @brief 魔法类型
 */
enum class MagicType {
    ATTACK = 0,         // 攻击魔法
    HEAL,               // 治疗魔法
    BUFF,               // 增益魔法
    DEBUFF,             // 减益魔法
    SUMMON,             // 召唤魔法
    TELEPORT,           // 传送魔法
    SPECIAL             // 特殊魔法
};

/**
 * @brief 魔法目标类型
 */
enum class MagicTargetType {
    SELF = 0,           // 自身
    SINGLE_TARGET,      // 单体目标
    AREA_TARGET,        // 区域目标
    ALL_ENEMIES,        // 所有敌人
    ALL_ALLIES,         // 所有盟友
    GROUND              // 地面位置
};

/**
 * @brief 魔法效果配置
 */
struct MagicEffect {
    int effectType;         // 效果类型
    int value;              // 效果数值
    int duration;           // 持续时间(毫秒)
    int interval;           // 间隔时间(毫秒)
    bool stackable;         // 是否可叠加
    
    MagicEffect() : effectType(0), value(0), duration(0), interval(0), stackable(false) {}
};

/**
 * @brief 魔法配置
 */
struct MagicConfig {
    int magicId;                    // 魔法ID
    std::string magicName;          // 魔法名称
    MagicType magicType;            // 魔法类型
    MagicTargetType targetType;     // 目标类型
    
    int minLevel;                   // 最小等级要求
    int maxLevel;                   // 最大等级要求
    int mpCost;                     // MP消耗
    int castTime;                   // 施法时间(毫秒)
    int cooldown;                   // 冷却时间(毫秒)
    int range;                      // 施法距离
    int areaRange;                  // 作用范围
    
    int baseDamage;                 // 基础伤害
    int damagePerLevel;             // 每级伤害增长
    int accuracy;                   // 命中率
    int criticalRate;               // 暴击率
    
    std::vector<MagicEffect> effects; // 魔法效果列表
    
    MagicConfig() : magicId(0), magicType(MagicType::ATTACK), targetType(MagicTargetType::SINGLE_TARGET),
                   minLevel(1), maxLevel(99), mpCost(10), castTime(1000), cooldown(0), 
                   range(1), areaRange(0), baseDamage(0), damagePerLevel(0), 
                   accuracy(100), criticalRate(0) {}
};

/**
 * @brief 魔法状态
 */
struct MagicState {
    int magicId;                // 魔法ID
    BaseObject* caster;         // 施法者
    BaseObject* target;         // 目标
    int targetX, targetY;       // 目标坐标
    DWORD startTime;            // 开始时间
    DWORD endTime;              // 结束时间
    bool active;                // 是否激活
    
    MagicState() : magicId(0), caster(nullptr), target(nullptr), 
                  targetX(0), targetY(0), startTime(0), endTime(0), active(false) {}
};

/**
 * @brief 魔法管理器
 * 负责管理所有魔法相关功能
 * 对应原项目的魔法系统功能
 */
class MagicManager : public IManager, public IMagicProvider, public IEventSubscriber {
private:
    std::string m_managerName;
    bool m_initialized;
    
    // 依赖注入
    EventBus* m_eventBus;
    IPlayerProvider* m_playerProvider;
    
    // 魔法数据
    std::unordered_map<int, MagicConfig> m_magicConfigs;
    std::unordered_map<std::string, std::vector<MagicState>> m_activeMagics; // 地图->激活魔法列表
    std::unordered_map<std::string, DWORD> m_magicCooldowns; // 玩家魔法冷却
    
    // 线程安全
    mutable std::shared_mutex m_configMutex;
    mutable std::shared_mutex m_stateMutex;
    mutable std::shared_mutex m_cooldownMutex;
    
    // 统计信息
    std::atomic<uint64_t> m_totalMagicsCast;
    std::atomic<uint64_t> m_totalDamageDealt;
    std::atomic<uint64_t> m_totalHealingDone;

public:
    MagicManager();
    virtual ~MagicManager();
    
    // IManager接口实现
    bool Initialize() override;
    void Finalize() override;
    void Update() override;
    const std::string& GetManagerName() const override;
    
    // 依赖注入
    void SetEventBus(EventBus* eventBus);
    void SetPlayerProvider(IPlayerProvider* provider);
    
    // IMagicProvider接口实现
    const Magic* GetMagic(int magicId) const override;
    bool CastMagic(BaseObject* caster, BaseObject* target, int magicId, int targetX, int targetY) override;
    bool CheckMagicConditions(BaseObject* caster, int magicId) override;
    
    // IEventSubscriber接口实现
    void OnEvent(const std::string& eventType, const EventData& data) override;
    
    // 魔法管理功能
    bool LoadMagicData();
    bool LoadMagicConfigs(const std::string& configFile);
    
    // 魔法施放系统
    bool CanCastMagic(BaseObject* caster, int magicId, BaseObject* target = nullptr, int targetX = 0, int targetY = 0);
    bool StartCastMagic(BaseObject* caster, int magicId, BaseObject* target, int targetX, int targetY);
    bool CompleteCastMagic(BaseObject* caster, int magicId, BaseObject* target, int targetX, int targetY);
    void CancelCastMagic(BaseObject* caster, int magicId);
    
    // 魔法效果系统
    void ProcessMagicEffects();
    void ApplyMagicEffect(const MagicConfig& config, BaseObject* caster, BaseObject* target);
    void ApplyAreaMagicEffect(const MagicConfig& config, BaseObject* caster, int centerX, int centerY);
    void RemoveMagicEffect(BaseObject* target, int magicId);
    
    // 魔法伤害计算
    int CalculateMagicDamage(const MagicConfig& config, BaseObject* caster, BaseObject* target);
    int CalculateHealAmount(const MagicConfig& config, BaseObject* caster, BaseObject* target);
    bool CheckMagicHit(const MagicConfig& config, BaseObject* caster, BaseObject* target);
    bool CheckMagicCritical(const MagicConfig& config, BaseObject* caster);
    
    // 魔法冷却系统
    bool IsOnCooldown(BaseObject* caster, int magicId);
    void SetCooldown(BaseObject* caster, int magicId, int cooldownTime);
    void ClearCooldown(BaseObject* caster, int magicId);
    DWORD GetRemainingCooldown(BaseObject* caster, int magicId);
    
    // 魔法目标系统
    std::vector<BaseObject*> GetMagicTargets(const MagicConfig& config, BaseObject* caster, 
                                            BaseObject* target, int targetX, int targetY);
    bool IsValidMagicTarget(const MagicConfig& config, BaseObject* caster, BaseObject* target);
    bool IsInMagicRange(const MagicConfig& config, BaseObject* caster, int targetX, int targetY);
    
    // 魔法配置管理
    void AddMagicConfig(const MagicConfig& config);
    void RemoveMagicConfig(int magicId);
    void UpdateMagicConfig(int magicId, const MagicConfig& config);
    MagicConfig* GetMagicConfig(int magicId);
    
    // 特殊魔法处理
    bool CastAttackMagic(BaseObject* caster, BaseObject* target, const MagicConfig& config);
    bool CastHealMagic(BaseObject* caster, BaseObject* target, const MagicConfig& config);
    bool CastBuffMagic(BaseObject* caster, BaseObject* target, const MagicConfig& config);
    bool CastDebuffMagic(BaseObject* caster, BaseObject* target, const MagicConfig& config);
    bool CastSummonMagic(BaseObject* caster, int targetX, int targetY, const MagicConfig& config);
    bool CastTeleportMagic(BaseObject* caster, int targetX, int targetY, const MagicConfig& config);
    
    // 统计信息
    uint64_t GetTotalMagicsCast() const { return m_totalMagicsCast; }
    uint64_t GetTotalDamageDealt() const { return m_totalDamageDealt; }
    uint64_t GetTotalHealingDone() const { return m_totalHealingDone; }
    
    // 魔法状态管理
    void AddActiveMagic(const std::string& mapName, const MagicState& state);
    void RemoveActiveMagic(const std::string& mapName, int magicId, BaseObject* caster);
    std::vector<MagicState> GetActiveMagics(const std::string& mapName);

private:
    // 内部辅助方法
    bool LoadMagicConfigFromFile(const std::string& fileName);
    bool ParseMagicConfigLine(const std::string& line, MagicConfig& config);
    bool ParseMagicEffectLine(const std::string& line, MagicEffect& effect);
    
    // 魔法验证
    bool ValidateMagicConfig(const MagicConfig& config) const;
    bool ValidateCastConditions(BaseObject* caster, const MagicConfig& config);
    bool ValidateTargetConditions(BaseObject* caster, BaseObject* target, const MagicConfig& config);
    
    // 魔法计算
    int GetMagicLevel(BaseObject* caster, int magicId);
    int CalculateActualDamage(int baseDamage, BaseObject* caster, BaseObject* target);
    int CalculateActualHealing(int baseHealing, BaseObject* caster, BaseObject* target);
    
    // 魔法效果处理
    void ProcessMagicState(MagicState& state);
    void ApplyInstantEffect(const MagicEffect& effect, BaseObject* caster, BaseObject* target);
    void ApplyDurationEffect(const MagicEffect& effect, BaseObject* caster, BaseObject* target);
    
    // 冷却管理
    std::string GetCooldownKey(BaseObject* caster, int magicId);
    void UpdateCooldowns();
    
    // 事件处理
    void HandlePlayerLogin(const struct PlayerLoginEventData& data);
    void HandleMagicCast(const struct MagicCastEventData& data);
    void HandleObjectDeath(const struct ObjectDeathEventData& data);
    
    // 日志记录
    void LogMagicOperation(const std::string& operation, const std::string& details) const;
    void LogMagicError(const std::string& operation, const std::string& error) const;
};
