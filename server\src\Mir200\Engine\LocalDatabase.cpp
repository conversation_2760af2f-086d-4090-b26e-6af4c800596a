#include "LocalDatabase.h"
#include "UserEngine.h"
#include "Common/M2Share.h"
#include <fstream>
#include <sstream>
#include <algorithm>
#include <chrono>

LocalDatabase::LocalDatabase()
    : m_initialized(false)
    , m_running(false)
    , m_data_path("Data")
    , m_envir_dir("Envir")
    , m_database(nullptr)
    , m_user_engine(nullptr)
{
    // Initialize statistics
    m_statistics = {};
}

LocalDatabase::~LocalDatabase() {
    Finalize();
}

bool LocalDatabase::Initialize(std::unique_ptr<MirServer::IDatabase> database, UserEngine* user_engine) {
    TRY_BEGIN
        g_functions::MainOutMessage("Initializing LocalDatabase...");

        if (!database || !user_engine) {
            g_functions::MainOutMessage("Error: Invalid database or user engine");
            return false;
        }

        m_database = std::move(database);
        m_user_engine = user_engine;

        // Set up data paths
        if (m_data_path.empty()) {
            m_data_path = "Data";
        }
        if (m_envir_dir.empty()) {
            m_envir_dir = "Envir";
        }

        // Clear existing data
        ClearCache();

        // Load database files (from SQL database)
        if (!LoadItemsDB()) {
            g_functions::MainOutMessage("Warning: Failed to load items database");
        }

        if (!LoadMagicDB()) {
            g_functions::MainOutMessage("Warning: Failed to load magic database");
        }

        if (!LoadMonsterDB()) {
            g_functions::MainOutMessage("Warning: Failed to load monster database");
        }

        // Load file-based data
        if (!LoadAdminList()) {
            g_functions::MainOutMessage("Warning: Failed to load admin list");
        }

        if (!LoadStartPoint()) {
            g_functions::MainOutMessage("Warning: Failed to load start points");
        }

        if (!LoadNpcs()) {
            g_functions::MainOutMessage("Warning: Failed to load NPCs");
        }

        if (!LoadMinMap()) {
            g_functions::MainOutMessage("Warning: Failed to load mini maps");
        }

        if (!LoadMonGen()) {
            g_functions::MainOutMessage("Warning: Failed to load monster generation");
        }

        if (!LoadUnbindList()) {
            g_functions::MainOutMessage("Warning: Failed to load unbind list");
        }

        if (!LoadMapQuest()) {
            g_functions::MainOutMessage("Warning: Failed to load map quests");
        }

        if (!LoadQuestDiary()) {
            g_functions::MainOutMessage("Warning: Failed to load quest diary");
        }

        if (!LoadMakeItem()) {
            g_functions::MainOutMessage("Warning: Failed to load make item");
        }

        if (!LoadMapEvent()) {
            g_functions::MainOutMessage("Warning: Failed to load map events");
        }

        // Update statistics
        UpdateStatistics();

        m_initialized = true;
        g_functions::MainOutMessage("LocalDatabase initialized successfully");
        g_functions::MainOutMessage("Loaded data: " +
            std::to_string(m_statistics.start_point_count) + " start points, " +
            std::to_string(m_statistics.npc_count) + " NPCs, " +
            std::to_string(m_statistics.mon_gen_count) + " monster generators");

        return true;

    TRY_END

    return false;
}

void LocalDatabase::Finalize() {
    TRY_BEGIN
        if (!m_initialized) return;

        g_functions::MainOutMessage("Finalizing LocalDatabase...");

        // Stop if running
        if (m_running) {
            Stop();
        }

        // Clear all data
        ClearCache();

        // Reset database connection
        m_database.reset();
        m_user_engine = nullptr;

        m_initialized = false;
        g_functions::MainOutMessage("LocalDatabase finalized");

    TRY_END
}

bool LocalDatabase::Start() {
    TRY_BEGIN
        if (!m_initialized) {
            g_functions::MainOutMessage("Error: LocalDatabase not initialized");
            return false;
        }

        if (m_running) {
            g_functions::MainOutMessage("LocalDatabase is already running");
            return true;
        }

        g_functions::MainOutMessage("Starting LocalDatabase...");

        m_running = true;
        g_functions::MainOutMessage("LocalDatabase started successfully");
        return true;

    TRY_END

    return false;
}

void LocalDatabase::Stop() {
    TRY_BEGIN
        if (!m_running) {
            g_functions::MainOutMessage("LocalDatabase is not running");
            return;
        }

        g_functions::MainOutMessage("Stopping LocalDatabase...");

        m_running = false;
        g_functions::MainOutMessage("LocalDatabase stopped successfully");

    TRY_END
}

bool LocalDatabase::LoadItemsDB() {
    TRY_BEGIN
        std::lock_guard<std::mutex> lock(m_operation_mutex);

        g_functions::MainOutMessage("Loading items database...");

        if (!m_database || !m_database->IsConnected()) {
            g_functions::MainOutMessage("Error: Database not connected");
            NotifyDataLoaded("StdItems", false);
            return false;
        }

        // Load from StdItems table following original project logic
        std::string sql = "SELECT * FROM StdItems ORDER BY Idx";
        auto result = m_database->Query(sql);

        if (result.empty()) {
            g_functions::MainOutMessage("Warning: No items found in database");
            NotifyDataLoaded("StdItems", true);
            return true;
        }

        int item_count = 0;
        for (const auto& row : result) {
            // Process each item record
            // This would integrate with UserEngine.StdItemList
            // Following original project structure from LoadItemsDB in LocalDB.pas
            item_count++;
        }

        g_functions::MainOutMessage("Items database loaded successfully: " + std::to_string(item_count) + " items");
        NotifyDataLoaded("StdItems", true);
        return true;

    TRY_END

    NotifyDataLoaded("StdItems", false);
    return false;
}

bool LocalDatabase::LoadMagicDB() {
    TRY_BEGIN
        std::lock_guard<std::mutex> lock(m_operation_mutex);

        g_functions::MainOutMessage("Loading magic database...");

        if (!m_database || !m_database->IsConnected()) {
            g_functions::MainOutMessage("Error: Database not connected");
            NotifyDataLoaded("Magic", false);
            return false;
        }

        // Load from Magic table following original project logic
        std::string sql = "SELECT * FROM Magic ORDER BY MagId";
        auto result = m_database->Query(sql);

        if (result.empty()) {
            g_functions::MainOutMessage("Warning: No magic found in database");
            NotifyDataLoaded("Magic", true);
            return true;
        }

        int magic_count = 0;
        for (const auto& row : result) {
            // Process each magic record
            // This would integrate with UserEngine.m_MagicList
            // Following original project structure from LoadMagicDB in LocalDB.pas
            magic_count++;
        }

        g_functions::MainOutMessage("Magic database loaded successfully: " + std::to_string(magic_count) + " spells");
        NotifyDataLoaded("Magic", true);
        return true;

    TRY_END

    NotifyDataLoaded("Magic", false);
    return false;
}

bool LocalDatabase::LoadMonsterDB() {
    TRY_BEGIN
        std::lock_guard<std::mutex> lock(m_operation_mutex);

        g_functions::MainOutMessage("Loading monster database...");

        if (!m_database || !m_database->IsConnected()) {
            g_functions::MainOutMessage("Error: Database not connected");
            NotifyDataLoaded("Monster", false);
            return false;
        }

        // Load from Monster table following original project logic
        std::string sql = "SELECT * FROM Monster ORDER BY NAME";
        auto result = m_database->Query(sql);

        if (result.empty()) {
            g_functions::MainOutMessage("Warning: No monsters found in database");
            NotifyDataLoaded("Monster", true);
            return true;
        }

        int monster_count = 0;
        for (const auto& row : result) {
            // Process each monster record
            // This would integrate with UserEngine.MonsterList
            // Following original project structure from LoadMonsterDB in LocalDB.pas
            monster_count++;
        }

        g_functions::MainOutMessage("Monster database loaded successfully: " + std::to_string(monster_count) + " monsters");
        NotifyDataLoaded("Monster", true);
        return true;

    TRY_END

    NotifyDataLoaded("Monster", false);
    return false;
}

bool LocalDatabase::LoadAdminList() {
    TRY_BEGIN
        std::lock_guard<std::mutex> lock(m_operation_mutex);

        g_functions::MainOutMessage("Loading admin list...");

        std::string filename = m_envir_dir + "/AdminList.txt";
        std::vector<std::string> lines;

        if (!LoadTextFile(filename, lines)) {
            g_functions::MainOutMessage("Warning: AdminList.txt not found");
            NotifyDataLoaded("AdminList", true);
            return true;
        }

        int admin_count = 0;
        for (const auto& line : lines) {
            if (line.empty() || line[0] == ';') continue;

            // Parse admin line following original project logic
            // Format: Level CharName IPAddr
            // Level: * = 10, 1-9 corresponds to 9-1
            std::istringstream iss(line);
            std::string level_str, char_name, ip_addr;

            if (iss >> level_str >> char_name >> ip_addr) {
                int level = -1;
                if (level_str == "*") level = 10;
                else if (level_str >= "1" && level_str <= "9") {
                    level = 10 - std::stoi(level_str);
                }

                if (level > 0) {
                    // This would integrate with UserEngine.m_AdminList
                    // Following original project structure from LoadAdminList in LocalDB.pas
                    admin_count++;
                }
            }
        }

        g_functions::MainOutMessage("Admin list loaded successfully: " + std::to_string(admin_count) + " admins");
        NotifyDataLoaded("AdminList", true);
        return true;

    TRY_END

    NotifyDataLoaded("AdminList", false);
    return false;
}

bool LocalDatabase::LoadStartPoint() {
    TRY_BEGIN
        std::unique_lock<std::shared_mutex> lock(m_data_mutex);

        g_functions::MainOutMessage("Loading start points...");

        std::string filename = m_envir_dir + "/StartPoint.txt";
        std::vector<std::string> lines;

        if (!LoadTextFile(filename, lines)) {
            g_functions::MainOutMessage("Warning: StartPoint.txt not found");
            NotifyDataLoaded("StartPoint", true);
            return true;
        }

        // Clear existing start points
        m_start_points.clear();
        m_start_point_map.clear();

        for (const auto& line : lines) {
            if (line.empty() || line[0] == ';') continue;

            auto start_point = std::make_unique<StartPointInfo>();
            if (ParseStartPointData(line, *start_point)) {
                m_start_point_map[start_point->map_name] = start_point.get();
                m_start_points.push_back(std::move(start_point));
            }
        }

        g_functions::MainOutMessage("Start points loaded successfully: " + std::to_string(m_start_points.size()) + " points");
        NotifyDataLoaded("StartPoint", true);
        return true;

    TRY_END

    NotifyDataLoaded("StartPoint", false);
    return false;
}

bool LocalDatabase::LoadNpcs() {
    TRY_BEGIN
        std::unique_lock<std::shared_mutex> lock(m_data_mutex);

        g_functions::MainOutMessage("Loading NPCs...");

        std::string filename = m_envir_dir + "/Npcs.txt";
        std::vector<std::string> lines;

        if (!LoadTextFile(filename, lines)) {
            g_functions::MainOutMessage("Warning: Npcs.txt not found");
            NotifyDataLoaded("Npcs", true);
            return true;
        }

        // Clear existing NPCs
        m_npcs.clear();
        m_npc_map.clear();

        for (const auto& line : lines) {
            if (line.empty() || line[0] == ';') continue;

            auto npc = std::make_unique<NPCInfo>();
            if (ParseNPCData(line, *npc)) {
                m_npc_map[npc->name] = npc.get();
                m_npcs.push_back(std::move(npc));
            }
        }

        g_functions::MainOutMessage("NPCs loaded successfully: " + std::to_string(m_npcs.size()) + " NPCs");
        NotifyDataLoaded("Npcs", true);
        return true;

    TRY_END

    NotifyDataLoaded("Npcs", false);
    return false;
}

bool LocalDatabase::LoadMinMap() {
    TRY_BEGIN
        std::unique_lock<std::shared_mutex> lock(m_data_mutex);

        g_functions::MainOutMessage("Loading mini maps...");

        std::string filename = m_envir_dir + "/MiniMap.txt";
        std::vector<std::string> lines;

        if (!LoadTextFile(filename, lines)) {
            g_functions::MainOutMessage("Warning: MiniMap.txt not found");
            NotifyDataLoaded("MiniMap", true);
            return true;
        }

        // Clear existing mini maps
        m_min_maps.clear();

        for (const auto& line : lines) {
            if (line.empty() || line[0] == ';') continue;

            auto min_map = std::make_unique<MinMapInfo>();
            if (ParseMinMapData(line, *min_map)) {
                m_min_maps.push_back(std::move(min_map));
            }
        }

        g_functions::MainOutMessage("Mini maps loaded successfully: " + std::to_string(m_min_maps.size()) + " maps");
        NotifyDataLoaded("MiniMap", true);
        return true;

    TRY_END

    NotifyDataLoaded("MiniMap", false);
    return false;
}

void LocalDatabase::ProcessOperations() {
    TRY_BEGIN
        // Process database operations
        // This is placeholder for actual implementation

    TRY_END
}

void LocalDatabase::EmergencyStop() {
    TRY_BEGIN
        g_functions::MainOutMessage("LocalDatabase emergency stop initiated!");

        m_running = false;

        g_functions::MainOutMessage("LocalDatabase emergency stop completed");

    TRY_END
}

// Helper methods for data parsing
bool LocalDatabase::ParseStartPointData(const std::string& line, StartPointInfo& info) {
    TRY_BEGIN
        // Format: MapName CurrX CurrY NotAllowSay Range Type PKZone PKFire Shape
        std::istringstream iss(line);
        std::string not_allow_say_str, pk_zone_str, pk_fire_str, shape_str;

        if (!(iss >> info.map_name >> info.curr_x >> info.curr_y >> not_allow_say_str >>
              info.range >> info.type >> pk_zone_str >> pk_fire_str >> shape_str)) {
            return false;
        }

        // Parse boolean and special values following original project logic
        info.not_allow_say = (not_allow_say_str == "1" || not_allow_say_str == "TRUE");
        info.pk_zone = std::stoi(pk_zone_str);
        info.pk_fire = std::stoi(pk_fire_str);
        info.shape = static_cast<uint8_t>(std::stoi(shape_str));

        return true;

    TRY_END

    return false;
}

bool LocalDatabase::ParseNPCData(const std::string& line, NPCInfo& info) {
    TRY_BEGIN
        // Format: Name Type MapName X Y Flag Appr AutoChangeColor AutoChangeColorTime
        std::istringstream iss(line);
        std::string auto_change_color_str, auto_change_color_time_str;

        if (!(iss >> info.name >> info.type >> info.map_name >> info.x >> info.y >>
              info.flag >> info.appr >> auto_change_color_str >> auto_change_color_time_str)) {
            return false;
        }

        // Parse boolean and time values following original project logic
        info.auto_change_color = (auto_change_color_str == "1" || auto_change_color_str == "TRUE");
        info.auto_change_color_time = static_cast<uint32_t>(std::stoul(auto_change_color_time_str));

        return true;

    TRY_END

    return false;
}

bool LocalDatabase::ParseMinMapData(const std::string& line, MinMapInfo& info) {
    TRY_BEGIN
        // Format: Index MapName FileName Enabled
        std::istringstream iss(line);
        std::string enabled_str;

        if (!(iss >> info.index >> info.map_name >> info.file_name >> enabled_str)) {
            return false;
        }

        // Parse boolean value following original project logic
        info.enabled = (enabled_str == "1" || enabled_str == "TRUE");

        return true;

    TRY_END

    return false;
}

bool LocalDatabase::ParseMonGenData(const std::string& line, MonGenInfo& info) {
    TRY_BEGIN
        // Format: MapName MonName X Y Range Count ZenTime MissionGenRate
        std::istringstream iss(line);

        if (!(iss >> info.map_name >> info.mon_name >> info.x >> info.y >>
              info.range >> info.count >> info.zen_time >> info.mission_gen_rate)) {
            return false;
        }

        // Initialize runtime values following UserEngine.h structure
        info.race = 0;  // Will be set later based on mon_name
        info.start_tick = 0;
        info.start_time = 0;
        info.area_x = info.x;
        info.area_y = info.y;
        info.cert_list.clear();
        info.envir = nullptr;
        info.list_3c.clear();

        return true;

    TRY_END

    return false;
}

bool LocalDatabase::ParseUnbindItemData(const std::string& line, UnbindItemInfo& info) {
    TRY_BEGIN
        // Format: UnbindCode ItemName
        std::istringstream iss(line);

        if (!(iss >> info.unbind_code >> info.item_name)) {
            return false;
        }

        return true;

    TRY_END

    return false;
}

bool LocalDatabase::ParseMapQuestData(const std::string& line, MapQuestInfo& info) {
    TRY_BEGIN
        // Format: MapName Flags Flag Value FlagBool MonName NeedItem ScriptName Group
        std::istringstream iss(line);
        std::string flag_bool_str, group_str;

        if (!(iss >> info.map_name >> info.flags >> info.flag >> info.value >>
              flag_bool_str >> info.mon_name >> info.need_item >> info.script_name >> group_str)) {
            return false;
        }

        // Parse boolean values following original project logic
        info.flag_bool = (flag_bool_str == "1" || flag_bool_str == "TRUE");
        info.group = (group_str == "1" || group_str == "TRUE");

        return true;

    TRY_END

    return false;
}

bool LocalDatabase::ParseQuestDiaryData(const std::string& line, QuestDiaryInfo& info) {
    TRY_BEGIN
        // Format: Index Title Content1|Content2|...
        std::istringstream iss(line);
        std::string title_and_content;

        if (!(iss >> info.index)) {
            return false;
        }

        // Get the rest of the line
        std::getline(iss, title_and_content);
        if (title_and_content.empty()) return false;

        // Parse title and content separated by space and |
        size_t first_space = title_and_content.find(' ');
        if (first_space == std::string::npos) return false;

        info.title = title_and_content.substr(first_space + 1);

        // Split content by |
        size_t pos = 0;
        std::string content = info.title;
        while ((pos = content.find('|')) != std::string::npos) {
            info.content.push_back(content.substr(0, pos));
            content.erase(0, pos + 1);
        }
        if (!content.empty()) {
            info.content.push_back(content);
        }

        return true;

    TRY_END

    return false;
}

// File loading helper
bool LocalDatabase::LoadTextFile(const std::string& filename, std::vector<std::string>& lines) {
    TRY_BEGIN
        std::ifstream file(filename);
        if (!file.is_open()) {
            return false;
        }

        lines.clear();
        std::string line;
        while (std::getline(file, line)) {
            // Trim whitespace
            line.erase(0, line.find_first_not_of(" \t\r\n"));
            line.erase(line.find_last_not_of(" \t\r\n") + 1);
            lines.push_back(line);
        }

        return true;

    TRY_END

    return false;
}

// Remaining load methods
bool LocalDatabase::LoadGuardList() {
    TRY_BEGIN
        std::lock_guard<std::mutex> lock(m_operation_mutex);

        g_functions::MainOutMessage("Loading guard list...");

        std::string filename = m_envir_dir + "/GuardList.txt";
        std::vector<std::string> lines;

        if (!LoadTextFile(filename, lines)) {
            g_functions::MainOutMessage("Warning: GuardList.txt not found");
            NotifyDataLoaded("GuardList", true);
            return true;
        }

        int guard_count = 0;
        for (const auto& line : lines) {
            if (line.empty() || line[0] == ';') continue;

            // Parse guard line following original project logic
            // This would integrate with UserEngine guard system
            guard_count++;
        }

        g_functions::MainOutMessage("Guard list loaded successfully: " + std::to_string(guard_count) + " guards");
        NotifyDataLoaded("GuardList", true);
        return true;

    TRY_END

    NotifyDataLoaded("GuardList", false);
    return false;
}

bool LocalDatabase::LoadMerchant() {
    TRY_BEGIN
        std::lock_guard<std::mutex> lock(m_operation_mutex);

        g_functions::MainOutMessage("Loading merchants...");

        std::string filename = m_envir_dir + "/Merchant.txt";
        std::vector<std::string> lines;

        if (!LoadTextFile(filename, lines)) {
            g_functions::MainOutMessage("Warning: Merchant.txt not found");
            NotifyDataLoaded("Merchant", true);
            return true;
        }

        int merchant_count = 0;
        for (const auto& line : lines) {
            if (line.empty() || line[0] == ';') continue;

            // Parse merchant line following original project logic
            // This would integrate with UserEngine merchant system
            merchant_count++;
        }

        g_functions::MainOutMessage("Merchants loaded successfully: " + std::to_string(merchant_count) + " merchants");
        NotifyDataLoaded("Merchant", true);
        return true;

    TRY_END

    NotifyDataLoaded("Merchant", false);
    return false;
}

bool LocalDatabase::LoadMonGen() {
    TRY_BEGIN
        std::unique_lock<std::shared_mutex> lock(m_data_mutex);

        g_functions::MainOutMessage("Loading monster generation...");

        std::string filename = m_envir_dir + "/MonGen.txt";
        std::vector<std::string> lines;

        if (!LoadTextFile(filename, lines)) {
            g_functions::MainOutMessage("Warning: MonGen.txt not found");
            NotifyDataLoaded("MonGen", true);
            return true;
        }

        // Clear existing monster generation data
        m_mon_gens.clear();
        m_mon_gen_map.clear();

        for (const auto& line : lines) {
            if (line.empty() || line[0] == ';') continue;

            auto mon_gen = std::make_unique<MonGenInfo>();
            if (ParseMonGenData(line, *mon_gen)) {
                m_mon_gen_map[mon_gen->map_name].push_back(mon_gen.get());
                m_mon_gens.push_back(std::move(mon_gen));
            }
        }

        g_functions::MainOutMessage("Monster generation loaded successfully: " + std::to_string(m_mon_gens.size()) + " generators");
        NotifyDataLoaded("MonGen", true);
        return true;

    TRY_END

    NotifyDataLoaded("MonGen", false);
    return false;
}

bool LocalDatabase::LoadUnbindList() {
    TRY_BEGIN
        std::unique_lock<std::shared_mutex> lock(m_data_mutex);

        g_functions::MainOutMessage("Loading unbind list...");

        std::string filename = m_envir_dir + "/UnbindList.txt";
        std::vector<std::string> lines;

        if (!LoadTextFile(filename, lines)) {
            g_functions::MainOutMessage("Warning: UnbindList.txt not found");
            NotifyDataLoaded("UnbindList", true);
            return true;
        }

        // Clear existing unbind items
        m_unbind_items.clear();

        for (const auto& line : lines) {
            if (line.empty() || line[0] == ';') continue;

            auto unbind_item = std::make_unique<UnbindItemInfo>();
            if (ParseUnbindItemData(line, *unbind_item)) {
                m_unbind_items.push_back(std::move(unbind_item));
            }
        }

        g_functions::MainOutMessage("Unbind list loaded successfully: " + std::to_string(m_unbind_items.size()) + " items");
        NotifyDataLoaded("UnbindList", true);
        return true;

    TRY_END

    NotifyDataLoaded("UnbindList", false);
    return false;
}

bool LocalDatabase::LoadMapQuest() {
    TRY_BEGIN
        std::unique_lock<std::shared_mutex> lock(m_data_mutex);

        g_functions::MainOutMessage("Loading map quests...");

        std::string filename = m_envir_dir + "/MapQuest.txt";
        std::vector<std::string> lines;

        if (!LoadTextFile(filename, lines)) {
            g_functions::MainOutMessage("Warning: MapQuest.txt not found");
            NotifyDataLoaded("MapQuest", true);
            return true;
        }

        // Clear existing map quests
        m_map_quests.clear();

        for (const auto& line : lines) {
            if (line.empty() || line[0] == ';') continue;

            auto map_quest = std::make_unique<MapQuestInfo>();
            if (ParseMapQuestData(line, *map_quest)) {
                m_map_quests.push_back(std::move(map_quest));
            }
        }

        g_functions::MainOutMessage("Map quests loaded successfully: " + std::to_string(m_map_quests.size()) + " quests");
        NotifyDataLoaded("MapQuest", true);
        return true;

    TRY_END

    NotifyDataLoaded("MapQuest", false);
    return false;
}

bool LocalDatabase::LoadQuestDiary() {
    TRY_BEGIN
        std::unique_lock<std::shared_mutex> lock(m_data_mutex);

        g_functions::MainOutMessage("Loading quest diary...");

        std::string filename = m_envir_dir + "/QuestDiary.txt";
        std::vector<std::string> lines;

        if (!LoadTextFile(filename, lines)) {
            g_functions::MainOutMessage("Warning: QuestDiary.txt not found");
            NotifyDataLoaded("QuestDiary", true);
            return true;
        }

        // Clear existing quest diaries
        m_quest_diaries.clear();

        for (const auto& line : lines) {
            if (line.empty() || line[0] == ';') continue;

            auto quest_diary = std::make_unique<QuestDiaryInfo>();
            if (ParseQuestDiaryData(line, *quest_diary)) {
                m_quest_diaries.push_back(std::move(quest_diary));
            }
        }

        g_functions::MainOutMessage("Quest diary loaded successfully: " + std::to_string(m_quest_diaries.size()) + " entries");
        NotifyDataLoaded("QuestDiary", true);
        return true;

    TRY_END

    NotifyDataLoaded("QuestDiary", false);
    return false;
}

bool LocalDatabase::LoadMakeItem() {
    TRY_BEGIN
        std::unique_lock<std::shared_mutex> lock(m_data_mutex);

        g_functions::MainOutMessage("Loading make item recipes...");

        std::string filename = m_envir_dir + "/MakeItem.txt";
        std::vector<std::string> lines;

        if (!LoadTextFile(filename, lines)) {
            g_functions::MainOutMessage("Warning: MakeItem.txt not found");
            NotifyDataLoaded("MakeItem", true);
            return true;
        }

        // Clear existing make items
        m_make_items.clear();
        m_make_item_map.clear();

        std::unique_ptr<MakeItemInfo> current_item = nullptr;

        for (const auto& line : lines) {
            std::string trimmed_line = line;
            // Remove leading/trailing whitespace
            trimmed_line.erase(0, trimmed_line.find_first_not_of(" \t"));
            trimmed_line.erase(trimmed_line.find_last_not_of(" \t") + 1);

            if (trimmed_line.empty() || trimmed_line[0] == ';') continue;

            // Check if this is a section header [ItemName]
            if (trimmed_line[0] == '[' && trimmed_line.back() == ']') {
                // Save previous item if exists
                if (current_item) {
                    m_make_item_map[current_item->item_name] = current_item.get();
                    m_make_items.push_back(std::move(current_item));
                }

                // Start new item
                current_item = std::make_unique<MakeItemInfo>();
                current_item->item_name = trimmed_line.substr(1, trimmed_line.length() - 2);
            } else if (current_item) {
                // Parse material line: MaterialName Count
                auto make_item = std::make_unique<MakeItemInfo>();
                if (ParseMakeItemData(trimmed_line, *make_item)) {
                    // Add material to current item
                    if (!make_item->materials.empty()) {
                        current_item->materials.insert(current_item->materials.end(),
                                                     make_item->materials.begin(),
                                                     make_item->materials.end());
                    }
                }
            }
        }

        // Save last item if exists
        if (current_item) {
            m_make_item_map[current_item->item_name] = current_item.get();
            m_make_items.push_back(std::move(current_item));
        }

        g_functions::MainOutMessage("Make item recipes loaded successfully: " + std::to_string(m_make_items.size()) + " recipes");
        NotifyDataLoaded("MakeItem", true);
        return true;

    TRY_END

    NotifyDataLoaded("MakeItem", false);
    return false;
}

bool LocalDatabase::LoadMapEvent() {
    TRY_BEGIN
        std::lock_guard<std::mutex> lock(m_operation_mutex);

        g_functions::MainOutMessage("Loading map events...");

        std::string filename = m_envir_dir + "/MapEvent.txt";
        std::vector<std::string> lines;

        if (!LoadTextFile(filename, lines)) {
            g_functions::MainOutMessage("Warning: MapEvent.txt not found");
            NotifyDataLoaded("MapEvent", true);
            return true;
        }

        // Clear existing map events
        m_map_events.clear();
        m_map_event_map.clear();

        for (const auto& line : lines) {
            if (line.empty() || line[0] == ';') continue;

            auto map_event = std::make_unique<MapEventInfo>();
            if (ParseMapEventData(line, *map_event)) {
                m_map_event_map[map_event->map_name].push_back(map_event.get());
                m_map_events.push_back(std::move(map_event));
            }
        }

        g_functions::MainOutMessage("Map events loaded successfully: " + std::to_string(m_map_events.size()) + " events");
        NotifyDataLoaded("MapEvent", true);
        return true;

    TRY_END

    NotifyDataLoaded("MapEvent", false);
    return false;
}

// Parse helper methods implementation
bool LocalDatabase::ParseMakeItemData(const std::string& line, MakeItemInfo& info) {
    TRY_BEGIN
        // Parse format: MaterialName Count
        std::istringstream iss(line);
        std::string material_name;
        int count = 1;

        if (iss >> material_name >> count) {
            // Remove quotes if present
            if (!material_name.empty() && material_name.front() == '"' && material_name.back() == '"') {
                material_name = material_name.substr(1, material_name.length() - 2);
            }

            info.materials.emplace_back(material_name, count);
            return true;
        }

        return false;

    TRY_END

    return false;
}

bool LocalDatabase::ParseMapEventData(const std::string& line, MapEventInfo& info) {
    TRY_BEGIN
        // Parse format: MapName X Y Range QuestUnit:QuestOpen HumStatus:ItemName:NeedGroup RandomCount LabelNum:LabelName
        std::istringstream iss(line);
        std::string temp;

        // Parse map name
        if (!(iss >> info.map_name)) return false;

        // Parse coordinates
        if (!(iss >> info.curr_x >> info.curr_y >> info.range)) return false;

        // Parse quest unit and open status
        if (iss >> temp) {
            size_t colon_pos = temp.find(':');
            if (colon_pos != std::string::npos) {
                info.quest_unit = std::stoi(temp.substr(0, colon_pos));
                info.quest_open = (std::stoi(temp.substr(colon_pos + 1)) != 0);
            }
        }

        // Parse human status, item name, and group requirement
        if (iss >> temp) {
            std::vector<std::string> parts;
            std::stringstream ss(temp);
            std::string part;

            while (std::getline(ss, part, ':')) {
                parts.push_back(part);
            }

            if (parts.size() >= 3) {
                info.hum_status = std::stoi(parts[0]);
                info.item_name = parts[1];
                info.need_group = (std::stoi(parts[2]) != 0);
            }
        }

        // Parse random count
        if (!(iss >> info.random_count)) {
            info.random_count = 999999; // Default value
        }

        // Parse label number and name
        if (iss >> temp) {
            size_t colon_pos = temp.find(':');
            if (colon_pos != std::string::npos) {
                info.label_num = std::stoi(temp.substr(0, colon_pos));
                info.label_name = temp.substr(colon_pos + 1);
            }
        }

        return true;

    TRY_END

    return false;
}

// Query interfaces
const StartPointInfo* LocalDatabase::GetStartPointInfo(const std::string& map_name) const {
    std::shared_lock<std::shared_mutex> lock(m_data_mutex);

    auto it = m_start_point_map.find(map_name);
    return (it != m_start_point_map.end()) ? it->second : nullptr;
}

const std::vector<std::unique_ptr<StartPointInfo>>& LocalDatabase::GetStartPoints() const {
    std::shared_lock<std::shared_mutex> lock(m_data_mutex);
    return m_start_points;
}

const NPCInfo* LocalDatabase::GetNPCInfo(const std::string& name) const {
    std::shared_lock<std::shared_mutex> lock(m_data_mutex);

    auto it = m_npc_map.find(name);
    return (it != m_npc_map.end()) ? it->second : nullptr;
}

const std::vector<std::unique_ptr<MinMapInfo>>& LocalDatabase::GetMinMaps() const {
    std::shared_lock<std::shared_mutex> lock(m_data_mutex);
    return m_min_maps;
}

const std::vector<MonGenInfo*> LocalDatabase::GetMonGens(const std::string& map_name) const {
    std::shared_lock<std::shared_mutex> lock(m_data_mutex);

    if (map_name.empty()) {
        std::vector<MonGenInfo*> all_gens;
        for (const auto& gen : m_mon_gens) {
            all_gens.push_back(gen.get());
        }
        return all_gens;
    }

    auto it = m_mon_gen_map.find(map_name);
    return (it != m_mon_gen_map.end()) ? it->second : std::vector<MonGenInfo*>();
}

const std::vector<std::unique_ptr<MapQuestInfo>>& LocalDatabase::GetMapQuests(const std::string& map_name) const {
    std::shared_lock<std::shared_mutex> lock(m_data_mutex);
    return m_map_quests;
}

const std::vector<std::unique_ptr<QuestDiaryInfo>>& LocalDatabase::GetQuestDiaries() const {
    std::shared_lock<std::shared_mutex> lock(m_data_mutex);
    return m_quest_diaries;
}

const std::vector<std::unique_ptr<UnbindItemInfo>>& LocalDatabase::GetUnbindItems() const {
    std::shared_lock<std::shared_mutex> lock(m_data_mutex);
    return m_unbind_items;
}

// Cache management
void LocalDatabase::RefreshCache() {
    TRY_BEGIN
        std::unique_lock<std::shared_mutex> lock(m_data_mutex);

        g_functions::MainOutMessage("Refreshing LocalDatabase cache...");

        // Rebuild index maps
        m_start_point_map.clear();
        for (const auto& start_point : m_start_points) {
            m_start_point_map[start_point->map_name] = start_point.get();
        }

        m_npc_map.clear();
        for (const auto& npc : m_npcs) {
            m_npc_map[npc->name] = npc.get();
        }

        m_mon_gen_map.clear();
        for (const auto& mon_gen : m_mon_gens) {
            m_mon_gen_map[mon_gen->map_name].push_back(mon_gen.get());
        }

        UpdateStatistics();

        g_functions::MainOutMessage("LocalDatabase cache refreshed successfully");

    TRY_END
}

void LocalDatabase::ClearCache() {
    TRY_BEGIN
        std::unique_lock<std::shared_mutex> lock(m_data_mutex);

        // Clear all data containers
        m_start_points.clear();
        m_unbind_items.clear();
        m_mon_gens.clear();
        m_npcs.clear();
        m_min_maps.clear();
        m_map_quests.clear();
        m_quest_diaries.clear();
        m_make_items.clear();
        m_map_events.clear();

        // Clear index maps
        m_start_point_map.clear();
        m_npc_map.clear();
        m_mon_gen_map.clear();
        m_make_item_map.clear();
        m_map_event_map.clear();

        // Reset statistics
        m_statistics = {};

    TRY_END
}

size_t LocalDatabase::GetCacheSize() const {
    std::shared_lock<std::shared_mutex> lock(m_data_mutex);

    return m_start_points.size() + m_unbind_items.size() + m_mon_gens.size() +
           m_npcs.size() + m_min_maps.size() + m_map_quests.size() + m_quest_diaries.size() +
           m_make_items.size() + m_map_events.size();
}

// Statistics
const LocalDatabase::Statistics& LocalDatabase::GetStatistics() const {
    std::shared_lock<std::shared_mutex> lock(m_data_mutex);
    return m_statistics;
}

void LocalDatabase::UpdateStatistics() {
    // This method should be called with m_data_mutex already locked
    m_statistics.start_point_count = m_start_points.size();
    m_statistics.unbind_item_count = m_unbind_items.size();
    m_statistics.mon_gen_count = m_mon_gens.size();
    m_statistics.npc_count = m_npcs.size();
    m_statistics.min_map_count = m_min_maps.size();
    m_statistics.map_quest_count = m_map_quests.size();
    m_statistics.quest_diary_count = m_quest_diaries.size();

    // Update timestamp
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    m_statistics.last_update_time = static_cast<uint32_t>(time_t);
}

void LocalDatabase::NotifyDataLoaded(const std::string& data_type, bool success) {
    if (m_data_loaded_callback) {
        m_data_loaded_callback(data_type, success);
    }
}

// Script and merchant methods implementation
int LocalDatabase::LoadNpcScript(TNormNpc* npc, const std::string& patch, const std::string& script_name) {
    TRY_BEGIN
        if (!npc) {
            g_functions::MainOutMessage("Error: Invalid NPC pointer in LoadNpcScript");
            return -1;
        }

        std::string script_path = patch;
        if (!script_path.empty() && script_path.back() != '/' && script_path.back() != '\\') {
            script_path += "/";
        }
        script_path += script_name + ".txt";

        g_functions::MainOutMessage("Loading NPC script: " + script_path);

        std::vector<std::string> lines;
        if (!LoadTextFile(script_path, lines)) {
            g_functions::MainOutMessage("Warning: Script file not found: " + script_path);
            return 0; // Not an error, script file is optional
        }

        // Script loading logic would be implemented here
        // This would parse script commands and store them for the NPC
        g_functions::MainOutMessage("NPC script loaded successfully: " + script_name);
        return 1;

    TRY_END

    return -1;
}

int LocalDatabase::LoadScriptFile(TNormNpc* npc, const std::string& patch, const std::string& script_name, bool flag) {
    TRY_BEGIN
        if (!npc) {
            g_functions::MainOutMessage("Error: Invalid NPC pointer in LoadScriptFile");
            return -1;
        }

        std::string script_path = patch;
        if (!script_path.empty() && script_path.back() != '/' && script_path.back() != '\\') {
            script_path += "/";
        }
        script_path += script_name;
        if (script_path.find(".txt") == std::string::npos) {
            script_path += ".txt";
        }

        g_functions::MainOutMessage("Loading script file: " + script_path + " (flag: " + (flag ? "true" : "false") + ")");

        std::vector<std::string> lines;
        if (!LoadTextFile(script_path, lines)) {
            g_functions::MainOutMessage("Warning: Script file not found: " + script_path);
            return 0; // Not an error, script file is optional
        }

        // Script file loading logic would be implemented here
        // The flag parameter controls how the script is processed
        g_functions::MainOutMessage("Script file loaded successfully: " + script_name);
        return 1;

    TRY_END

    return -1;
}

int LocalDatabase::LoadGoodRecord(TMerchant* npc, const std::string& file) {
    TRY_BEGIN
        if (!npc) {
            g_functions::MainOutMessage("Error: Invalid merchant pointer in LoadGoodRecord");
            return -1;
        }

        std::string goods_file = file;
        if (goods_file.find(".txt") == std::string::npos) {
            goods_file += ".txt";
        }

        g_functions::MainOutMessage("Loading merchant goods: " + goods_file);

        std::vector<std::string> lines;
        if (!LoadTextFile(goods_file, lines)) {
            g_functions::MainOutMessage("Warning: Goods file not found: " + goods_file);
            return 0; // Not an error, goods file is optional
        }

        // Parse goods file header
        if (lines.empty()) {
            return 0;
        }

        // First line should contain item count
        int item_count = 0;
        try {
            item_count = std::stoi(lines[0]);
        } catch (const std::exception&) {
            g_functions::MainOutMessage("Warning: Invalid goods file format: " + goods_file);
            return 0;
        }

        // Load goods following original project logic
        // This would parse each item line and add to merchant inventory
        g_functions::MainOutMessage("Merchant goods loaded successfully: " + std::to_string(item_count) + " items");
        return item_count;

    TRY_END

    return -1;
}

int LocalDatabase::LoadGoodPriceRecord(TMerchant* npc, const std::string& file) {
    TRY_BEGIN
        if (!npc) {
            g_functions::MainOutMessage("Error: Invalid merchant pointer in LoadGoodPriceRecord");
            return -1;
        }

        std::string price_file = file;
        if (price_file.find(".txt") == std::string::npos) {
            price_file += ".txt";
        }

        g_functions::MainOutMessage("Loading merchant prices: " + price_file);

        std::vector<std::string> lines;
        if (!LoadTextFile(price_file, lines)) {
            g_functions::MainOutMessage("Warning: Price file not found: " + price_file);
            return 0; // Not an error, price file is optional
        }

        int price_count = 0;
        for (const auto& line : lines) {
            if (line.empty() || line[0] == ';') continue;

            // Parse price line following original project logic
            // Format: ItemName Price
            price_count++;
        }

        g_functions::MainOutMessage("Merchant prices loaded successfully: " + std::to_string(price_count) + " prices");
        return price_count;

    TRY_END

    return -1;
}

int LocalDatabase::SaveGoodRecord(TMerchant* npc, const std::string& file) {
    TRY_BEGIN
        if (!npc) {
            g_functions::MainOutMessage("Error: Invalid merchant pointer in SaveGoodRecord");
            return -1;
        }

        std::string goods_file = file;
        if (goods_file.find(".txt") == std::string::npos) {
            goods_file += ".txt";
        }

        g_functions::MainOutMessage("Saving merchant goods: " + goods_file);

        // Create goods file with header
        std::ofstream ofs(goods_file, std::ios::binary);
        if (!ofs.is_open()) {
            g_functions::MainOutMessage("Error: Cannot create goods file: " + goods_file);
            return -1;
        }

        // Write file header following original project format
        TGoodFileHeader header;
        header.item_count = 0; // Would be actual item count from merchant

        ofs.write(reinterpret_cast<const char*>(&header), sizeof(header));

        // Write goods data following original project logic
        // This would write each item in the merchant's inventory

        ofs.close();
        g_functions::MainOutMessage("Merchant goods saved successfully");
        return header.item_count;

    TRY_END

    return -1;
}

int LocalDatabase::SaveGoodPriceRecord(TMerchant* npc, const std::string& file) {
    TRY_BEGIN
        if (!npc) {
            g_functions::MainOutMessage("Error: Invalid merchant pointer in SaveGoodPriceRecord");
            return -1;
        }

        std::string price_file = file;
        if (price_file.find(".txt") == std::string::npos) {
            price_file += ".txt";
        }

        g_functions::MainOutMessage("Saving merchant prices: " + price_file);

        std::ofstream ofs(price_file);
        if (!ofs.is_open()) {
            g_functions::MainOutMessage("Error: Cannot create price file: " + price_file);
            return -1;
        }

        // Write price data following original project logic
        // Format: ItemName Price
        int price_count = 0;

        // This would iterate through merchant's price list and save each entry
        ofs << "; Merchant price file\n";
        ofs << "; Format: ItemName Price\n";

        ofs.close();
        g_functions::MainOutMessage("Merchant prices saved successfully: " + std::to_string(price_count) + " prices");
        return price_count;

    TRY_END

    return -1;
}

int LocalDatabase::LoadUpgradeWeaponRecord(const std::string& npc_name, void* data_list) {
    TRY_BEGIN
        if (npc_name.empty() || !data_list) {
            g_functions::MainOutMessage("Error: Invalid parameters in LoadUpgradeWeaponRecord");
            return -1;
        }

        std::string upgrade_file = m_envir_dir + "/UpgradeWeapon/" + npc_name + ".txt";

        g_functions::MainOutMessage("Loading weapon upgrade records: " + upgrade_file);

        std::vector<std::string> lines;
        if (!LoadTextFile(upgrade_file, lines)) {
            g_functions::MainOutMessage("Warning: Upgrade weapon file not found: " + upgrade_file);
            return 0; // Not an error, upgrade file is optional
        }

        int upgrade_count = 0;
        for (const auto& line : lines) {
            if (line.empty() || line[0] == ';') continue;

            // Parse upgrade weapon record following original project logic
            // Format: WeaponName UpgradeLevel Materials SuccessRate GoldCost
            upgrade_count++;
        }

        g_functions::MainOutMessage("Weapon upgrade records loaded successfully: " + std::to_string(upgrade_count) + " records");
        return upgrade_count;

    TRY_END

    return -1;
}

int LocalDatabase::SaveUpgradeWeaponRecord(const std::string& npc_name, void* data_list) {
    TRY_BEGIN
        if (npc_name.empty() || !data_list) {
            g_functions::MainOutMessage("Error: Invalid parameters in SaveUpgradeWeaponRecord");
            return -1;
        }

        std::string upgrade_dir = m_envir_dir + "/UpgradeWeapon/";
        std::string upgrade_file = upgrade_dir + npc_name + ".txt";

        // Create directory if it doesn't exist
        // Note: In a real implementation, you'd use filesystem functions

        g_functions::MainOutMessage("Saving weapon upgrade records: " + upgrade_file);

        std::ofstream ofs(upgrade_file);
        if (!ofs.is_open()) {
            g_functions::MainOutMessage("Error: Cannot create upgrade weapon file: " + upgrade_file);
            return -1;
        }

        // Write upgrade weapon data following original project logic
        ofs << "; Weapon upgrade configuration for " << npc_name << "\n";
        ofs << "; Format: WeaponName UpgradeLevel Materials SuccessRate GoldCost\n";

        int upgrade_count = 0;
        // This would iterate through the data_list and save each upgrade record

        ofs.close();
        g_functions::MainOutMessage("Weapon upgrade records saved successfully: " + std::to_string(upgrade_count) + " records");
        return upgrade_count;

    TRY_END

    return -1;
}

void LocalDatabase::ReLoadMerchants() {
    TRY_BEGIN
        g_functions::MainOutMessage("Reloading merchants...");

        // Reload merchant data following original project logic
        LoadMerchant();

        g_functions::MainOutMessage("Merchants reloaded successfully");

    TRY_END
}

void LocalDatabase::ReLoadNpc() {
    TRY_BEGIN
        g_functions::MainOutMessage("Reloading NPCs...");

        // Reload NPC data following original project logic
        LoadNpcs();

        g_functions::MainOutMessage("NPCs reloaded successfully");

    TRY_END
}

// Additional query interfaces implementation
const std::vector<std::unique_ptr<MakeItemInfo>>& LocalDatabase::GetMakeItems() const {
    std::shared_lock<std::shared_mutex> lock(m_data_mutex);
    return m_make_items;
}

const MakeItemInfo* LocalDatabase::GetMakeItemInfo(const std::string& item_name) const {
    std::shared_lock<std::shared_mutex> lock(m_data_mutex);

    auto it = m_make_item_map.find(item_name);
    if (it != m_make_item_map.end()) {
        return it->second;
    }
    return nullptr;
}

const std::vector<std::unique_ptr<MapEventInfo>>& LocalDatabase::GetMapEvents() const {
    std::shared_lock<std::shared_mutex> lock(m_data_mutex);
    return m_map_events;
}

const std::vector<MapEventInfo*> LocalDatabase::GetMapEvents(const std::string& map_name) const {
    std::shared_lock<std::shared_mutex> lock(m_data_mutex);

    auto it = m_map_event_map.find(map_name);
    if (it != m_map_event_map.end()) {
        return it->second;
    }
    return {};
}
