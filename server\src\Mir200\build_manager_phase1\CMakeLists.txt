cmake_minimum_required(VERSION 3.16)
project(Mir200_Manager_Fixed)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置编译选项
if(MSVC)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /W3")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} /Od /Zi")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /O2")
else()
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -g -O0")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O3")
endif()

# 包含目录
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/..)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../Common)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../Managers)

# 只编译GameConfigManager（修复版本）
set(MANAGER_SOURCES
    ../Managers/GameConfigManager.cpp
)

# 创建修复版测试程序
add_executable(test_manager_fixed 
    ../test_manager_fixed_simple.cpp
    ${MANAGER_SOURCES}
)

# 链接线程库
find_package(Threads REQUIRED)
target_link_libraries(test_manager_fixed Threads::Threads)

# 设置输出目录
set_target_properties(test_manager_fixed PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
)

# 创建配置和数据目录
file(MAKE_DIRECTORY "${CMAKE_BINARY_DIR}/bin/config")
file(MAKE_DIRECTORY "${CMAKE_BINARY_DIR}/bin/data")
file(MAKE_DIRECTORY "${CMAKE_BINARY_DIR}/bin/logs")
