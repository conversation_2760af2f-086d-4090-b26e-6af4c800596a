#include "ItemManager.h"
#include "EventBus.h"
#include "DatabaseManager.h"
#include "ServiceContainer.h"
#include "../Common/M2Share.h"
#include <iostream>
#include <fstream>
#include <sstream>
#include <random>
#include <algorithm>

ItemManager::ItemManager() 
    : m_manager<PERSON><PERSON>("ItemManager")
    , m_initialized(false)
    , m_player<PERSON><PERSON>ider(nullptr)
    , m_eventBus(nullptr)
    , m_totalItemsCreated(0)
    , m_totalDropsGenerated(0)
    , m_totalUpgradesAttempted(0) {
}

ItemManager::~ItemManager() {
    Finalize();
}

bool ItemManager::Initialize() {
    if (m_initialized) return true;
    
    std::cout << "[ItemManager] Initializing..." << std::endl;
    
    // 加载物品数据库
    if (!LoadItemDatabase()) {
        std::cerr << "[ItemManager] Failed to load item database" << std::endl;
        return false;
    }
    
    // 加载掉落配置
    if (!LoadDropConfigs("MonsterDrops.txt")) {
        std::cout << "[ItemManager] Warning: Failed to load drop configs, using defaults" << std::endl;
    }
    
    // 加载强化配置
    if (!LoadUpgradeConfigs("ItemUpgrade.txt")) {
        std::cout << "[ItemManager] Warning: Failed to load upgrade configs, using defaults" << std::endl;
    }
    
    m_initialized = true;
    std::cout << "[ItemManager] Initialized successfully" << std::endl;
    return true;
}

void ItemManager::Finalize() {
    if (!m_initialized) return;
    
    std::cout << "[ItemManager] Finalizing..." << std::endl;
    
    // 清理配置数据
    {
        std::unique_lock<std::shared_mutex> lock(m_dropMutex);
        m_monsterDrops.clear();
    }
    
    {
        std::unique_lock<std::shared_mutex> lock(m_upgradeMutex);
        m_upgradeConfigs.clear();
    }
    
    m_initialized = false;
    std::cout << "[ItemManager] Finalized" << std::endl;
}

void ItemManager::Update() {
    if (!m_initialized) return;
    
    // 定期检查物品过期等
    // 这里可以添加定期维护逻辑
}

const std::string& ItemManager::GetManagerName() const {
    return m_managerName;
}

void ItemManager::SetPlayerProvider(IPlayerProvider* provider) {
    m_playerProvider = provider;
}

void ItemManager::SetEventBus(EventBus* eventBus) {
    m_eventBus = eventBus;
    
    // 订阅相关事件
    if (m_eventBus) {
        m_eventBus->Subscribe("PlayerLogin", [this](const EventData& data) {
            OnEvent("PlayerLogin", data);
        });
        
        m_eventBus->Subscribe("MonsterKilled", [this](const EventData& data) {
            OnEvent("MonsterKilled", data);
        });
        
        m_eventBus->Subscribe("ItemUsed", [this](const EventData& data) {
            OnEvent("ItemUsed", data);
        });
    }
}

const StdItem* ItemManager::GetStdItem(int itemIndex) const {
    // 通过DatabaseManager获取标准物品
    // 这里需要访问DatabaseManager
    return nullptr; // 临时返回，需要实现
}

TUserItem ItemManager::CreateItem(int itemIndex) const {
    const StdItem* stdItem = GetStdItem(itemIndex);
    if (!stdItem) {
        LogItemError("CreateItem", "Invalid item index: " + std::to_string(itemIndex));
        return TUserItem{};
    }

    TUserItem item;
    item.wIndex = itemIndex;
    item.MakeIndex = static_cast<DWORD>(GetCurrentTime() + itemIndex); // 简单的唯一ID生成
    item.Dura = stdItem->DuraMax;
    item.DuraMax = stdItem->DuraMax;

    // 初始化其他属性
    memset(item.btValue, 0, sizeof(item.btValue));

    ++m_totalItemsCreated;

    LogItemOperation("CreateItem", "Created item " + std::to_string(itemIndex));
    return item;
}

bool ItemManager::ValidateItem(const TUserItem& item) const {
    // 验证物品有效性
    if (item.wIndex <= 0) return false;
    
    const StdItem* stdItem = GetStdItem(item.wIndex);
    if (!stdItem) return false;
    
    // 检查耐久度
    if (item.Dura > item.DuraMax) return false;
    if (item.DuraMax > stdItem->DuraMax * 2) return false; // 允许一定的强化
    
    return true;
}

std::vector<TUserItem> ItemManager::GenerateMonsterDrops(const std::string& monsterName, int level) {
    std::vector<TUserItem> drops;
    
    std::shared_lock<std::shared_mutex> lock(m_dropMutex);
    auto it = m_monsterDrops.find(monsterName);
    if (it == m_monsterDrops.end()) {
        return drops; // 没有配置掉落
    }
    
    for (const auto& dropConfig : it->second) {
        // 检查等级要求
        if (level < dropConfig.minLevel || level > dropConfig.maxLevel) {
            continue;
        }
        
        // 掉落概率判断
        if (RollDropChance(dropConfig.dropRate)) {
            // 生成掉落数量
            std::random_device rd;
            std::mt19937 gen(rd());
            std::uniform_int_distribution<> countDist(dropConfig.minCount, dropConfig.maxCount);
            int count = countDist(gen);
            
            for (int i = 0; i < count; ++i) {
                TUserItem item = CreateItem(dropConfig.itemIndex);
                if (item.wIndex > 0) {
                    drops.push_back(item);
                }
            }
        }
    }
    
    ++m_totalDropsGenerated;
    return drops;
}

void ItemManager::OnEvent(const std::string& eventType, const EventData& data) {
    if (eventType == "PlayerLogin") {
        // 处理玩家登录事件
        // 可以验证玩家物品等
    }
    else if (eventType == "MonsterKilled") {
        // 处理怪物死亡事件，生成掉落
        // 这里需要根据具体的事件数据结构来实现
    }
    else if (eventType == "ItemUsed") {
        // 处理物品使用事件
        // 可以记录统计信息等
    }
}

bool ItemManager::LoadItemDatabase() {
    // 这里应该通过DatabaseManager加载物品数据
    // 暂时返回true
    LogItemOperation("LoadItemDatabase", "Item database loaded");
    return true;
}

bool ItemManager::LoadDropConfigs(const std::string& configFile) {
    return LoadDropConfigFromFile(configFile);
}

bool ItemManager::LoadUpgradeConfigs(const std::string& configFile) {
    return LoadUpgradeConfigFromFile(configFile);
}

TUserItem ItemManager::CreateRandomItem(int itemType, int level) const {
    // 根据类型和等级创建随机物品
    // 这里需要实现具体的随机生成逻辑
    return TUserItem{};
}

TUserItem ItemManager::CreateItemWithProperties(int itemIndex, int dura, int duraMax) const {
    TUserItem item = CreateItem(itemIndex);
    if (item.wIndex > 0) {
        item.Dura = dura;
        item.DuraMax = duraMax;
    }
    return item;
}

bool ItemManager::IsItemExpired(const TUserItem& item) const {
    // 检查物品是否过期
    // 根据原项目逻辑，过期物品应该直接消失
    return false; // 暂时返回false
}

bool ItemManager::IsItemStackable(const TUserItem& item) const {
    const StdItem* stdItem = GetStdItem(item.wIndex);
    if (!stdItem) return false;
    
    // 根据原项目逻辑判断是否可堆叠
    // 通常药品、材料等可以堆叠
    return (stdItem->StdMode == 0 || stdItem->StdMode == 1); // 药品和其他消耗品
}

bool ItemManager::UpgradeItem(PlayObject* player, TUserItem& item, const TUserItem& material) {
    if (!player) return false;
    
    ++m_totalUpgradesAttempted;
    
    // 检查是否可以强化
    if (!CanUpgradeItem(item)) {
        return false;
    }
    
    // 获取成功率
    int successRate = GetUpgradeSuccessRate(item);
    
    // 判断是否成功
    if (RollDropChance(successRate)) {
        // 强化成功，提升属性
        item.DuraMax += 1000; // 示例：增加最大耐久
        LogItemOperation("UpgradeItem", "Item upgrade successful");
        return true;
    } else {
        // 强化失败，根据原项目逻辑可能会损坏物品
        LogItemOperation("UpgradeItem", "Item upgrade failed");
        return false;
    }
}

bool ItemManager::CanUpgradeItem(const TUserItem& item) const {
    std::shared_lock<std::shared_mutex> lock(m_upgradeMutex);
    
    const StdItem* stdItem = GetStdItem(item.wIndex);
    if (!stdItem) return false;
    
    auto it = m_upgradeConfigs.find(stdItem->StdMode);
    if (it == m_upgradeConfigs.end()) return false;
    
    // 检查当前强化等级
    int currentLevel = (item.DuraMax - stdItem->DuraMax) / 1000; // 简化的等级计算
    return currentLevel < it->second.maxUpgradeLevel;
}

int ItemManager::GetUpgradeSuccessRate(const TUserItem& item) const {
    std::shared_lock<std::shared_mutex> lock(m_upgradeMutex);
    
    const StdItem* stdItem = GetStdItem(item.wIndex);
    if (!stdItem) return 0;
    
    auto it = m_upgradeConfigs.find(stdItem->StdMode);
    if (it == m_upgradeConfigs.end()) return 0;
    
    return it->second.successRate;
}

void ItemManager::AddMonsterDrop(const std::string& monsterName, const ItemDropConfig& config) {
    std::unique_lock<std::shared_mutex> lock(m_dropMutex);
    m_monsterDrops[monsterName].push_back(config);
}

void ItemManager::RemoveMonsterDrop(const std::string& monsterName, int itemIndex) {
    std::unique_lock<std::shared_mutex> lock(m_dropMutex);
    
    auto it = m_monsterDrops.find(monsterName);
    if (it != m_monsterDrops.end()) {
        auto& drops = it->second;
        drops.erase(std::remove_if(drops.begin(), drops.end(),
            [itemIndex](const ItemDropConfig& config) {
                return config.itemIndex == itemIndex;
            }), drops.end());
    }
}

std::vector<TUserItem> ItemManager::GenerateDropsForMonster(const std::string& monsterName, int level, int playerLevel) const {
    // 考虑玩家等级的掉落生成
    return GenerateMonsterDrops(monsterName, level);
}

bool ItemManager::RepairItem(PlayObject* player, TUserItem& item, int repairType) {
    if (!player || !CanRepairItem(item)) return false;
    
    int cost = GetRepairCost(item, repairType);
    // 这里需要检查玩家金币并扣除
    
    // 修理物品
    const StdItem* stdItem = GetStdItem(item.wIndex);
    if (stdItem) {
        if (repairType == 0) {
            // 普通修理
            item.Dura = std::min(item.Dura + stdItem->DuraMax / 2, item.DuraMax);
        } else {
            // 特殊修理
            item.Dura = item.DuraMax;
        }
        
        LogItemOperation("RepairItem", "Item repaired successfully");
        return true;
    }
    
    return false;
}

int ItemManager::GetRepairCost(const TUserItem& item, int repairType) const {
    const StdItem* stdItem = GetStdItem(item.wIndex);
    if (!stdItem) return 0;
    
    int baseCost = stdItem->Price / 10; // 基础修理费用
    if (repairType == 1) {
        baseCost *= 3; // 特殊修理费用更高
    }
    
    return baseCost;
}

bool ItemManager::CanRepairItem(const TUserItem& item) const {
    const StdItem* stdItem = GetStdItem(item.wIndex);
    if (!stdItem) return false;
    
    // 只有耐久度不满的装备才能修理
    return (item.Dura < item.DuraMax && stdItem->DuraMax > 0);
}

void ItemManager::SetDropRate(const std::string& monsterName, int itemIndex, int rate) {
    std::unique_lock<std::shared_mutex> lock(m_dropMutex);
    
    auto& drops = m_monsterDrops[monsterName];
    for (auto& config : drops) {
        if (config.itemIndex == itemIndex) {
            config.dropRate = rate;
            return;
        }
    }
    
    // 如果不存在，创建新的配置
    ItemDropConfig config;
    config.itemIndex = itemIndex;
    config.dropRate = rate;
    drops.push_back(config);
}

int ItemManager::GetDropRate(const std::string& monsterName, int itemIndex) const {
    std::shared_lock<std::shared_mutex> lock(m_dropMutex);
    
    auto it = m_monsterDrops.find(monsterName);
    if (it != m_monsterDrops.end()) {
        for (const auto& config : it->second) {
            if (config.itemIndex == itemIndex) {
                return config.dropRate;
            }
        }
    }
    
    return 0;
}

bool ItemManager::LoadDropConfigFromFile(const std::string& fileName) {
    std::ifstream file(fileName);
    if (!file.is_open()) {
        LogItemError("LoadDropConfigFromFile", "Failed to open file: " + fileName);
        return false;
    }
    
    std::string line;
    while (std::getline(file, line)) {
        if (line.empty() || line[0] == ';') continue;
        
        std::string monsterName;
        ItemDropConfig config;
        if (ParseDropConfigLine(line, monsterName, config)) {
            AddMonsterDrop(monsterName, config);
        }
    }
    
    LogItemOperation("LoadDropConfigFromFile", "Drop config loaded from " + fileName);
    return true;
}

bool ItemManager::LoadUpgradeConfigFromFile(const std::string& fileName) {
    std::ifstream file(fileName);
    if (!file.is_open()) {
        LogItemError("LoadUpgradeConfigFromFile", "Failed to open file: " + fileName);
        return false;
    }
    
    std::string line;
    while (std::getline(file, line)) {
        if (line.empty() || line[0] == ';') continue;
        
        ItemUpgradeConfig config;
        if (ParseUpgradeConfigLine(line, config)) {
            std::unique_lock<std::shared_mutex> lock(m_upgradeMutex);
            m_upgradeConfigs[config.itemType] = config;
        }
    }
    
    LogItemOperation("LoadUpgradeConfigFromFile", "Upgrade config loaded from " + fileName);
    return true;
}

bool ItemManager::ParseDropConfigLine(const std::string& line, std::string& monsterName, ItemDropConfig& config) {
    std::istringstream iss(line);
    std::string token;
    
    // 解析格式: MonsterName ItemIndex DropRate MinCount MaxCount MinLevel MaxLevel
    if (!(iss >> monsterName >> config.itemIndex >> config.dropRate >> 
          config.minCount >> config.maxCount >> config.minLevel >> config.maxLevel)) {
        return false;
    }
    
    return true;
}

bool ItemManager::ParseUpgradeConfigLine(const std::string& line, ItemUpgradeConfig& config) {
    std::istringstream iss(line);
    
    // 解析格式: ItemType MaxLevel SuccessRate MaterialIndex MaterialCount GoldCost
    if (!(iss >> config.itemType >> config.maxUpgradeLevel >> config.successRate >> 
          config.materialIndex >> config.materialCount >> config.goldCost)) {
        return false;
    }
    
    return true;
}

int ItemManager::CalculateItemDurability(int itemIndex, int level) const {
    const StdItem* stdItem = GetStdItem(itemIndex);
    if (!stdItem) return 0;
    
    // 根据等级调整耐久度
    return stdItem->DuraMax + (level * 100);
}

int ItemManager::CalculateItemValue(const TUserItem& item) const {
    const StdItem* stdItem = GetStdItem(item.wIndex);
    if (!stdItem) return 0;
    
    // 根据耐久度和强化等级计算价值
    int baseValue = stdItem->Price;
    int durabilityFactor = (item.Dura * 100) / item.DuraMax;
    
    return (baseValue * durabilityFactor) / 100;
}

bool ItemManager::RollDropChance(int dropRate) const {
    if (dropRate <= 0) return false;
    if (dropRate >= 10000) return true;
    
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(1, 10000);
    
    return dis(gen) <= dropRate;
}

void ItemManager::HandlePlayerLogin(const PlayerLoginEventData& data) {
    // 处理玩家登录，验证物品等
    LogItemOperation("HandlePlayerLogin", "Player " + data.playerName + " logged in");
}

void ItemManager::HandleMonsterKilled(const MonsterKilledEventData& data) {
    // 处理怪物死亡，生成掉落
    auto drops = GenerateMonsterDrops(data.monsterName, data.monsterLevel);
    
    if (!drops.empty()) {
        LogItemOperation("HandleMonsterKilled", 
            "Generated " + std::to_string(drops.size()) + " drops for " + data.monsterName);
        
        // 这里需要将掉落物品放置到地图上
        // 需要通过事件系统通知地图管理器
    }
}

void ItemManager::HandleItemUsed(const ItemUsedEventData& data) {
    // 处理物品使用事件
    LogItemOperation("HandleItemUsed", "Item used: " + std::to_string(data.itemIndex));
}

void ItemManager::LogItemOperation(const std::string& operation, const std::string& details) const {
    std::cout << "[ItemManager] " << operation << ": " << details << std::endl;
}

void ItemManager::LogItemError(const std::string& operation, const std::string& error) const {
    std::cerr << "[ItemManager] ERROR in " << operation << ": " << error << std::endl;
}
