# PlayObject重构实施计划

## 概述

基于对原项目delphi/EM2Engine/ObjBase.pas中TPlayObject（24000+行）的深入分析，制定详细的PlayObject重构实施计划。当前PlayObject只有简化的占位实现，需要完整重构以实现原项目的所有功能。

## 当前实现状态

### ✅ 已有基础
- BaseObject模块化架构 (100%完成)
- 基础PlayObject类框架 (占位实现)
- Manager系统集成 (100%完成)

### 🔄 需要重构的核心功能

#### 1. 网络连接管理 (0%完成)
**原项目对应**:
```delphi
m_nSocket: Integer;           // 0x59C nSocket
m_nGSocketIdx: Integer;       // 0x5A0 wGateIndex
m_nGateIdx: Integer;          // 0x5A8 nGateIdx
m_sUserID: string[11];        // 0x58C 登录账号名
m_sIPaddr: string[15];        // 0x598 玩家IP地址
```

#### 2. 玩家数据管理 (0%完成)
**原项目对应**:
```delphi
m_dLogonTime: TDateTime;      // 0x5B0 登录时间
m_nSessionID: Integer;        // 0x5C0
m_nPayMent: Integer;          // 0x5C4 当前模式
m_boReadyRun: Boolean;        // 0x5BC 是否进入游戏
```

#### 3. 背包系统 (0%完成)
**原项目对应**:
```delphi
m_ItemList: TList;            // 0x40C 背包
m_UseItems: THumanUseItems;   // 装备
```

#### 4. 仓库系统 (0%完成)
**原项目对应**:
```delphi
m_StorageItemList: TList;     // 0x4F8 仓库
```

#### 5. 交易系统 (0%完成)
**原项目对应**:
```delphi
m_DealItemList: TList;        // 0x410 交易物品
m_nDealGolds: Integer;        // 0x414 交易金币
m_boDealOK: Boolean;          // 0x418 确认交易
m_DealCreat: TBaseObject;     // 0x31C 交易对象
```

#### 6. 脚本变量系统 (0%完成)
**原项目对应**:
```delphi
m_nVal: array[0..9] of Integer;     // 0x634-658 脚本变量
m_nMval: array[0..99] of Integer;   // M变量
m_DyVal: array[0..9] of Integer;    // 0x65C-680 动态变量
```

#### 7. 客户端消息处理 (0%完成)
**原项目方法**: 100+个Client*方法需要实现

#### 8. GM命令系统 (0%完成)
**原项目方法**: 50+个Cmd*方法需要实现

## 详细重构计划

### 第1周：PlayObject核心重构

#### 1.1 PlayObject类重构 (2天)
**目标**: 建立完整的PlayObject类结构

**实施内容**:
```cpp
// PlayObject.h - 完整重构
#pragma once

#include "BaseObject.h"
#include "PlayerNetwork.h"
#include "PlayerClient.h"
#include "PlayerBag.h"
#include "PlayerStorage.h"
#include "PlayerTrade.h"
#include "PlayerScript.h"
#include "PlayerQuest.h"
#include "PlayerRelation.h"
#include "PlayerMember.h"
#include "PlayerGameCoin.h"
#include "PlayerCommand.h"

class PlayObject : public BaseObject {
private:
    // 网络连接管理
    std::unique_ptr<PlayerNetwork> m_network_manager;
    
    // 客户端处理
    std::unique_ptr<PlayerClient> m_client_manager;
    
    // 背包系统
    std::unique_ptr<PlayerBag> m_bag_manager;
    
    // 仓库系统
    std::unique_ptr<PlayerStorage> m_storage_manager;
    
    // 交易系统
    std::unique_ptr<PlayerTrade> m_trade_manager;
    
    // 脚本系统
    std::unique_ptr<PlayerScript> m_script_manager;
    
    // 任务系统
    std::unique_ptr<PlayerQuest> m_quest_manager;
    
    // 关系系统
    std::unique_ptr<PlayerRelation> m_relation_manager;
    
    // 会员系统
    std::unique_ptr<PlayerMember> m_member_manager;
    
    // 游戏币系统
    std::unique_ptr<PlayerGameCoin> m_gamecoin_manager;
    
    // 命令系统
    std::unique_ptr<PlayerCommand> m_command_manager;

    // 玩家基础数据 (对应原项目字段)
    std::string m_user_id;              // m_sUserID
    std::string m_ip_address;           // m_sIPaddr
    std::string m_ip_local;             // m_sIPLocal
    DWORD m_logon_time;                 // m_dLogonTime
    DWORD m_logon_tick;                 // m_dwLogonTick
    bool m_ready_run;                   // m_boReadyRun
    int m_session_id;                   // m_nSessionID
    int m_payment;                      // m_nPayMent
    int m_pay_mode;                     // m_nPayMode
    
    // 连接相关
    int m_socket;                       // m_nSocket
    int m_gate_socket_index;            // m_nGSocketIdx
    int m_gate_index;                   // m_nGateIdx
    
    // 状态标志
    bool m_emergency_close;             // m_boEmergencyClose
    bool m_soft_close;                  // m_boSoftClose
    bool m_kick_flag;                   // m_boKickFlag
    bool m_reconnection;                // m_boReconnection
    bool m_rcd_saved;                   // m_boRcdSaved
    bool m_switch_data;                 // m_boSwitchData

public:
    PlayObject();
    virtual ~PlayObject();
    
    // 基础虚函数重写 (基于原项目)
    void Initialize() override;
    void Finalize() override;
    bool Operate(const ProcessMessage& msg) override;
    void Run() override;
    void RecalcAbilitys() override;
    void UserLogon() override;
    void Disappear() override;
    void MakeGhost() override;
    void ScatterBagItems(BaseObject* itemOfCreat) override;
    void DropUseItems(BaseObject* baseObject) override;
    std::string GetShowName() override;
    
    // 网络连接接口
    void SetSocket(int socket, int gateIndex, int gateSocketIndex);
    void SendSocket(const DefaultMessage& msg, const std::string& data);
    bool IsConnected() const;
    void Disconnect();
    
    // 玩家数据接口
    void SetUserInfo(const std::string& userId, const std::string& ipAddr);
    void SetSessionInfo(int sessionId, int payment, int payMode);
    bool IsReadyRun() const { return m_ready_run; }
    void SetReadyRun(bool ready) { m_ready_run = ready; }
    
    // 组件访问接口
    PlayerBag* GetBagManager() const { return m_bag_manager.get(); }
    PlayerStorage* GetStorageManager() const { return m_storage_manager.get(); }
    PlayerTrade* GetTradeManager() const { return m_trade_manager.get(); }
    PlayerScript* GetScriptManager() const { return m_script_manager.get(); }
    PlayerCommand* GetCommandManager() const { return m_command_manager.get(); }

private:
    void InitializeComponents();
    void FinalizeComponents();
    void UpdateComponents();
};
```

#### 1.2 网络连接管理模块 (1天)
**目标**: 实现PlayerNetwork模块

**实施内容**:
```cpp
// PlayerNetwork.h
class PlayerNetwork {
public:
    PlayerNetwork(PlayObject* owner);
    ~PlayerNetwork();
    
    // 连接管理
    void SetSocket(int socket, int gateIndex, int gateSocketIndex);
    void SendSocket(const DefaultMessage& msg, const std::string& data);
    void SendDefMessage(WORD ident, int recog, WORD param, WORD tag, WORD series, const std::string& msg);
    bool IsConnected() const;
    void Disconnect();
    
    // 消息发送 (基于原项目SendSocket方法)
    void SendUpdateMsg(BaseObject* baseObject, WORD ident, WORD param, 
                      int param1, int param2, int param3, const std::string& msg);
    void SendActionMsg(BaseObject* baseObject, WORD ident, WORD param,
                      int param1, int param2, int param3, const std::string& msg);
    
    // 状态管理
    void SetEmergencyClose(bool close) { m_emergency_close = close; }
    void SetSoftClose(bool close) { m_soft_close = close; }
    void SetKickFlag(bool kick) { m_kick_flag = kick; }
    
private:
    PlayObject* m_owner;
    int m_socket;
    int m_gate_index;
    int m_gate_socket_index;
    bool m_connected;
    bool m_emergency_close;
    bool m_soft_close;
    bool m_kick_flag;
};
```

#### 1.3 基础数据管理 (2天)
**目标**: 实现玩家基础数据管理

**实施内容**:
- 登录时间管理
- 会话信息管理
- 用户认证数据
- 状态标志管理

### 第2周：背包和仓库系统

#### 2.1 背包系统实现 (3天)
**目标**: 实现完整的背包系统

**实施内容**:
```cpp
// PlayerBag.h
class PlayerBag {
public:
    PlayerBag(PlayObject* owner);
    ~PlayerBag();
    
    // 物品管理 (基于原项目逻辑)
    bool AddItem(const TUserItem& item);
    bool DelItem(int index);
    bool DelItem(const TUserItem& item);
    TUserItem* FindItem(const std::string& itemName);
    TUserItem* FindItem(int itemIndex);
    int GetItemCount(const std::string& itemName);
    
    // 重量管理
    bool IsEnoughSpace(int weight);
    int GetCurrentWeight() const;
    int GetMaxWeight() const;
    void RecalcBagWeight();
    
    // 客户端同步
    void SendBagItems();
    void SendAddItem(const TUserItem& item);
    void SendDelItems(const TUserItem& item);
    void SendUpdateItem(const TUserItem& item);
    
    // 装备管理
    bool TakeOnItem(BYTE where, const TUserItem& item);
    bool TakeOffItem(BYTE where, TUserItem& item);
    TUserItem* GetUseItem(BYTE where);
    void SendUseItems();
    
    // 物品使用
    bool UseItem(int itemIndex, const std::string& itemName);
    bool EatItems(const StdItem& stdItem);
    bool ReadBook(const StdItem& stdItem);
    
private:
    PlayObject* m_owner;
    std::vector<TUserItem> m_items;           // m_ItemList
    THumanUseItems m_use_items;               // m_UseItems
    int m_max_weight;
    int m_current_weight;
    
    // 内部方法
    bool CheckItemsNeed(const StdItem& stdItem);
    bool CheckItemBindUse(const TUserItem& item);
    void GetBagUseItems(BYTE& dc, BYTE& sc, BYTE& mc, BYTE& dura);
};
```

#### 2.2 仓库系统实现 (2天)
**目标**: 实现仓库系统

**实施内容**:
```cpp
// PlayerStorage.h
class PlayerStorage {
public:
    PlayerStorage(PlayObject* owner);
    ~PlayerStorage();
    
    // 仓库管理
    bool StoreItem(NPC* npc, const TUserItem& item);
    bool TakeItem(NPC* npc, int itemIndex);
    void SendStorageItems();
    void SendSaveItemList(int baseObject);
    void SendSaveBigStorageItemList(int baseObject, int page);
    
    // 状态管理
    bool IsStorageOpen() const { return m_storage_npc != nullptr; }
    void OpenStorage(NPC* npc);
    void CloseStorage();
    
    // 密码系统 (基于原项目)
    bool CheckStoragePassword(const std::string& password);
    void SetStoragePassword(const std::string& password);
    bool IsPasswordLocked() const { return m_password_locked; }
    
private:
    PlayObject* m_owner;
    std::vector<TUserItem> m_storage_items;   // m_StorageItemList
    NPC* m_storage_npc;
    bool m_password_locked;                   // m_boPasswordLocked
    std::string m_storage_password;           // m_sStoragePwd
    int m_big_storage_page;                   // m_nBigStoragePage
};
```

### 第3周：交易和脚本系统

#### 3.1 交易系统实现 (3天)
**目标**: 实现安全交易系统

**实施内容**:
```cpp
// PlayerTrade.h
class PlayerTrade {
public:
    PlayerTrade(PlayObject* owner);
    ~PlayerTrade();
    
    // 交易管理 (基于原项目逻辑)
    bool StartTrade(PlayObject* target);
    bool AddTradeItem(const TUserItem& item);
    bool DelTradeItem(const TUserItem& item);
    bool SetTradeGold(int gold);
    bool ConfirmTrade();
    void CancelTrade();
    void DealEnd();
    
    // 状态查询
    bool IsTrading() const { return m_trade_target != nullptr; }
    PlayObject* GetTradeTarget() const { return m_trade_target; }
    bool IsTradeConfirmed() const { return m_deal_ok; }
    
    // 客户端同步
    void SendAddDealItem(const TUserItem& item);
    void SendDelDealItem(const TUserItem& item);
    void OpenDealDlg(BaseObject* baseObject);
    
private:
    PlayObject* m_owner;
    PlayObject* m_trade_target;               // m_DealCreat
    std::vector<TUserItem> m_trade_items;     // m_DealItemList
    int m_trade_gold;                         // m_nDealGolds
    bool m_deal_ok;                           // m_boDealOK
    DWORD m_deal_last_tick;                   // m_DealLastTick
    
    // 内部方法
    void GetBackDealItems();
    bool ValidateTradeItems();
    bool ValidateTradeGold();
};
```

#### 3.2 脚本变量系统 (2天)
**目标**: 实现脚本变量管理

**实施内容**:
```cpp
// PlayerScript.h
class PlayerScript {
public:
    PlayerScript(PlayObject* owner);
    ~PlayerScript();
    
    // 变量管理 (基于原项目)
    void SetVariable(int index, int value);
    int GetVariable(int index) const;
    void SetMVariable(int index, int value);
    int GetMVariable(int index) const;
    void SetDynamicVariable(int index, int value);
    int GetDynamicVariable(int index) const;
    
    // 脚本标签管理
    void SetScriptLabel(const std::string& label);
    void GetScriptLabel(const std::string& msg);
    bool LabelIsCanJmp(const std::string& label);
    
    // NPC脚本交互
    void SetCurrentNPC(BaseObject* npc) { m_current_npc = npc; }
    BaseObject* GetCurrentNPC() const { return m_current_npc; }
    
private:
    PlayObject* m_owner;
    std::array<int, 10> m_variables;          // m_nVal
    std::array<int, 100> m_m_variables;       // m_nMval
    std::array<int, 10> m_dy_variables;       // m_DyVal
    
    BaseObject* m_current_npc;                // m_NPC
    std::string m_script_label;               // m_sScriptLable
    std::string m_script_curr_label;          // m_sScriptCurrLable
    std::string m_script_go_back_label;       // m_sScriptGoBackLable
    
    TStringList m_can_jmp_script_label_list;  // m_CanJmpScriptLableList
    int m_script_goto_count;                  // m_nScriptGotoCount
};
```

### 第4周：客户端消息处理系统

#### 4.1 客户端消息分类 (2天)
**目标**: 建立客户端消息处理框架

**实施内容**:
```cpp
// PlayerClient.h
class PlayerClient {
public:
    PlayerClient(PlayObject* owner);
    ~PlayerClient();
    
    // 主要消息处理入口
    bool ProcessClientMessage(const ProcessMessage& msg);
    
    // 移动相关 (基于原项目Client*方法)
    bool ClientWalkXY(WORD ident, int x, int y, bool lateDelivery, DWORD& delayTime);
    bool ClientRunXY(WORD ident, int x, int y, int flag, DWORD& delayTime);
    bool ClientHorseRunXY(WORD ident, int x, int y, bool lateDelivery, DWORD& delayTime);
    bool ClientChangeDir(WORD ident, int x, int y, int dir, DWORD& delayTime);
    
    // 战斗相关
    bool ClientHitXY(WORD ident, int x, int y, int dir, bool lateDelivery, DWORD& delayTime);
    bool ClientSitDownHit(int x, int y, int dir, DWORD& delayTime);
    bool ClientSpellXY(WORD ident, int key, int targetX, int targetY, 
                      BaseObject* target, bool lateDelivery, DWORD& delayTime);
    
    // 物品相关
    bool ClientDropGold(int gold);
    bool ClientDropItem(const std::string& itemName, int itemIndex);
    bool ClientPickUpItem();
    void ClientTakeOnItems(BYTE where, int itemIndex, const std::string& itemName);
    void ClientTakeOffItems(BYTE where, int itemIndex, const std::string& itemName);
    void ClientUseItems(int itemIndex, const std::string& itemName);
    
    // 其他功能
    void ClientQueryBagItems();
    void ClientQueryUserState(PlayObject* playObject, int x, int y);
    void ClientOpenDoor(int x, int y);
    void ClientChangeMagicKey(int skillIndex, int key);
    
private:
    PlayObject* m_owner;
    
    // 内部处理方法
    bool CheckActionStatus(WORD ident, DWORD& delayTime);
    bool ValidateClientAction(WORD ident);
    void LogClientAction(const std::string& action, const std::string& details);
};
```

#### 4.2 具体Client方法实现 (3天)
**目标**: 实现所有Client*方法

**重点方法实现**:
- ClientDropGold, ClientPickUpItem
- ClientTakeOnItems, ClientTakeOffItems
- ClientUseItems, ClientChangeMagicKey
- ClientWalkXY, ClientRunXY, ClientHitXY
- ClientSpellXY等

### 第5周：GM命令系统

#### 5.1 命令处理框架 (2天)
**目标**: 建立GM命令处理系统

**实施内容**:
```cpp
// PlayerCommand.h
class PlayerCommand {
public:
    PlayerCommand(PlayObject* owner);
    ~PlayerCommand();
    
    // 命令处理入口
    bool ProcessCommand(const std::string& command, const std::vector<std::string>& params);
    
    // 基于原项目Cmd*方法
    void CmdUserCmd(const std::string& label);
    void CmdSearchHuman(const std::string& humanName);
    void CmdMapMove(const std::string& mapName);
    void CmdPositionMove(const std::string& mapName, int x, int y);
    void CmdHumanInfo(const std::string& humanName);
    void CmdGroupRecall();
    void CmdGuildRecall(const std::string& param);
    void CmdMemberFunction(const std::string& cmd, const std::string& param);
    void CmdReloadNpc(const std::string& param);
    void CmdAdjustExp(PlayObject* human, int exp);
    
private:
    PlayObject* m_owner;
    std::unordered_map<std::string, std::function<void(const std::vector<std::string>&)>> m_commands;
    
    // 权限检查
    bool CheckCommandPermission(const std::string& command);
    void LogCommand(const std::string& command, const std::string& params);
};
```

#### 5.3 命令实现 (3天)
**目标**: 实现所有Cmd*方法

**重点命令实现**:
- CmdUserCmd, CmdMemberFunction
- CmdSearchHuman, CmdGroupRecall
- CmdMapMove, CmdPositionMove
- CmdHumanInfo, CmdReloadNpc
- CmdAdjustExp等

## 集成和测试计划

### 第6周：系统集成
1. **组件集成**: 将所有模块集成到PlayObject
2. **接口对接**: 与Manager系统对接
3. **消息路由**: 建立完整的消息处理流程
4. **状态同步**: 确保各模块状态一致性

### 第7周：测试和优化
1. **功能测试**: 逐个功能与原项目对比测试
2. **集成测试**: 多模块协作测试
3. **性能测试**: 确保性能不低于原项目
4. **兼容性测试**: 协议和数据格式兼容性

## 技术要求

### 1. 原项目兼容性
- 100%遵循原项目TPlayObject逻辑
- 保持相同的数据结构和算法
- 维护协议编号一致性
- 不简化原项目复杂逻辑

### 2. 代码质量
- 现代C++17标准
- 线程安全设计
- 完整错误处理
- 详细代码注释

### 3. 性能要求
- 内存使用优化
- 高效的消息处理
- 快速的数据访问
- 最小化锁竞争

## 风险评估

### 高风险项
1. **复杂度管理**: 24000+行代码重构
2. **状态一致性**: 多模块间状态同步
3. **消息处理**: 100+个客户端方法精确实现

### 缓解措施
1. **模块化设计**: 降低单个模块复杂度
2. **渐进式实施**: 每周完成一个主要功能
3. **充分测试**: 每个模块完成后立即测试
4. **原项目对比**: 逐个方法验证兼容性

## 总结

通过7周的集中开发，将原项目24000+行的TPlayObject完全重构为现代化的模块化架构。采用组件化设计降低复杂度，通过渐进式实施控制风险，确保与原项目100%兼容的同时，提供更好的可维护性和扩展性。
