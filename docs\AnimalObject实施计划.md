# AnimalObject实施计划

## 概述

基于对原项目delphi/EM2Engine/ObjBase.pas中TAnimalObject的分析，AnimalObject是BaseObject和PlayObject/Monster之间的关键中间层，提供AI行为基础。当前重构中完全遗漏了这个重要层次，需要紧急实现。

## 原项目TAnimalObject分析

### 核心功能
基于delphi/EM2Engine/ObjBase.pas第510-533行：

```delphi
TAnimalObject = class(TBaseObject)
  m_nNotProcessCount: Integer;    // 未处理计数
  m_nTargetX: Integer;           // 目标X坐标
  m_nTargetY: Integer;           // 目标Y坐标
  m_boRunAwayMode: Boolean;      // 逃跑模式
  m_dwRunAwayStart: LongWord;    // 逃跑开始时间
  m_dwRunAwayTime: LongWord;     // 逃跑持续时间
```

### 核心方法
- `SearchTarget()` - 搜索目标
- `SetTargetXY(nX, nY: Integer)` - 设置目标坐标
- `GotoTargetXY()` - 前往目标坐标
- `Wondering()` - 游荡行为
- `Attack(TargeTBaseObject, nDir)` - 攻击行为
- `Struck(hiter)` - 受击处理
- `HitMagAttackTarget()` - 魔法攻击目标

### 继承关系
```
TBaseObject
└── TAnimalObject (中间层)
    ├── TPlayObject (玩家)
    ├── TMonster (怪物 - 在ObjMon.pas)
    ├── TNormNpc (NPC)
    └── 各种特殊怪物 (在ObjMon2.pas等)
```

## 当前问题分析

### 1. 继承关系错误
- **当前**: PlayObject直接继承BaseObject
- **正确**: PlayObject应继承AnimalObject
- **影响**: 缺失AI基础功能，无法支持怪物系统

### 2. 缺失的核心功能
- **目标管理**: 无法设置和追踪目标
- **AI行为基础**: 无游荡、逃跑等基础行为
- **动物移动**: 缺少智能移动逻辑
- **战斗基础**: 缺少动物战斗行为

### 3. 系统影响
- **怪物系统**: 无法实现Monster类
- **NPC系统**: NPC行为受限
- **AI扩展**: 无法支持复杂AI行为

## 实施计划 (1周)

### 第1-2天：AnimalObject类设计

#### 1.1 AnimalObject基础结构
```cpp
// AnimalObject.h
#pragma once

#include "BaseObject.h"
#include "AnimalAI.h"
#include "AnimalTarget.h"

class AnimalObject : public BaseObject {
protected:
    // 基于原项目TAnimalObject字段
    int m_not_process_count;        // m_nNotProcessCount
    int m_target_x;                 // m_nTargetX
    int m_target_y;                 // m_nTargetY
    bool m_run_away_mode;           // m_boRunAwayMode
    DWORD m_run_away_start;         // m_dwRunAwayStart
    DWORD m_run_away_time;          // m_dwRunAwayTime
    
    // AI组件
    std::unique_ptr<AnimalAI> m_ai_manager;
    std::unique_ptr<AnimalTarget> m_target_manager;

public:
    AnimalObject();
    virtual ~AnimalObject();
    
    // 基础虚函数重写
    bool Operate(const DefaultMessage& msg) override;
    void Run() override;
    void DelTargetCreat() override;
    
    // AnimalObject特有方法 (基于原项目)
    virtual void SearchTarget();
    virtual void SetTargetXY(int x, int y);
    virtual void GotoTargetXY();
    virtual void Wondering();
    virtual void Attack(BaseObject* target, int dir);
    virtual void Struck(BaseObject* hiter);
    virtual void HitMagAttackTarget(BaseObject* target, int hitPower, int magPower, bool flag);
    
    // 目标管理
    void SetTarget(BaseObject* target);
    BaseObject* GetTarget() const;
    bool HasTarget() const;
    void ClearTarget();
    
    // 逃跑模式
    void StartRunAwayMode(DWORD duration);
    void StopRunAwayMode();
    bool IsRunAwayMode() const;
    
    // AI状态
    bool IsWondering() const;
    bool IsChasing() const;
    bool IsAttacking() const;
};
```

#### 1.2 AnimalAI组件设计
```cpp
// AnimalAI.h
class AnimalAI {
public:
    AnimalAI(AnimalObject* owner);
    ~AnimalAI();
    
    // AI状态枚举
    enum class AIState {
        IDLE,           // 空闲
        WONDERING,      // 游荡
        CHASING,        // 追击
        ATTACKING,      // 攻击
        RUNNING_AWAY,   // 逃跑
        RETURNING       // 返回
    };
    
    // AI行为方法
    void Update();
    void ProcessWondering();
    void ProcessChasing();
    void ProcessAttacking();
    void ProcessRunningAway();
    
    // 状态管理
    void SetState(AIState state);
    AIState GetState() const;
    bool CanChangeState(AIState newState) const;
    
private:
    AnimalObject* m_owner;
    AIState m_current_state;
    DWORD m_state_start_time;
    DWORD m_last_action_time;
};
```

### 第3-4天：目标管理系统

#### 3.1 AnimalTarget组件
```cpp
// AnimalTarget.h
class AnimalTarget {
public:
    AnimalTarget(AnimalObject* owner);
    ~AnimalTarget();
    
    // 目标搜索 (基于原项目SearchTarget)
    BaseObject* SearchNearestTarget(int range = 12);
    BaseObject* SearchNearestEnemy(int range = 12);
    BaseObject* SearchNearestPlayer(int range = 12);
    
    // 目标验证
    bool IsValidTarget(BaseObject* target) const;
    bool IsInAttackRange(BaseObject* target) const;
    bool IsTargetVisible(BaseObject* target) const;
    
    // 目标管理
    void SetTarget(BaseObject* target);
    void SetTargetXY(int x, int y);
    BaseObject* GetTarget() const;
    void GetTargetXY(int& x, int& y) const;
    void ClearTarget();
    
    // 路径计算
    bool CanReachTarget() const;
    bool FindPathToTarget();
    bool FindPathToXY(int x, int y);
    
private:
    AnimalObject* m_owner;
    BaseObject* m_target;
    int m_target_x;
    int m_target_y;
    bool m_has_target_xy;
    
    // 内部方法
    bool IsProperTarget(BaseObject* obj) const;
    int CalculateDistance(BaseObject* obj) const;
    bool HasLineOfSight(BaseObject* obj) const;
};
```

#### 3.2 目标搜索实现
```cpp
// 基于原项目SearchTarget方法
BaseObject* AnimalTarget::SearchNearestTarget(int range) {
    if (!m_owner || !m_owner->GetEnvironment()) {
        return nullptr;
    }
    
    BaseObject* nearest_target = nullptr;
    int min_distance = range + 1;
    
    // 获取周围对象列表
    auto nearby_objects = m_owner->GetEnvironment()->GetNearbyObjects(
        m_owner->GetCurrX(), m_owner->GetCurrY(), range);
    
    for (auto obj : nearby_objects) {
        if (!IsProperTarget(obj)) continue;
        
        int distance = CalculateDistance(obj);
        if (distance < min_distance) {
            min_distance = distance;
            nearest_target = obj;
        }
    }
    
    return nearest_target;
}
```

### 第5天：AI行为实现

#### 5.1 游荡行为 (Wondering)
```cpp
// 基于原项目Wondering方法
void AnimalObject::Wondering() {
    if (!m_ai_manager) return;
    
    // 随机移动逻辑
    if (g_functions::Random(10) == 0) {
        BYTE dir = g_functions::Random(8);
        if (CanWalkTo(dir)) {
            WalkTo(dir, false);
        }
    }
    
    // 定期搜索目标
    if (g_functions::GetCurrentTime() - m_last_search_time > 3000) {
        SearchTarget();
        m_last_search_time = g_functions::GetCurrentTime();
    }
}
```

#### 5.2 前往目标 (GotoTargetXY)
```cpp
// 基于原项目GotoTargetXY方法
void AnimalObject::GotoTargetXY() {
    if (!m_target_manager) return;
    
    int target_x, target_y;
    m_target_manager->GetTargetXY(target_x, target_y);
    
    if (target_x == m_curr_x && target_y == m_curr_y) {
        // 已到达目标位置
        m_ai_manager->SetState(AnimalAI::AIState::IDLE);
        return;
    }
    
    // 计算移动方向
    BYTE dir = g_functions::GetDirection(m_curr_x, m_curr_y, target_x, target_y);
    
    // 尝试移动
    if (CanWalkTo(dir)) {
        WalkTo(dir, false);
    } else {
        // 寻找替代路径
        m_target_manager->FindPathToXY(target_x, target_y);
    }
}
```

#### 5.3 逃跑模式
```cpp
// 逃跑模式实现
void AnimalObject::StartRunAwayMode(DWORD duration) {
    m_run_away_mode = true;
    m_run_away_start = g_functions::GetCurrentTime();
    m_run_away_time = duration;
    
    if (m_ai_manager) {
        m_ai_manager->SetState(AnimalAI::AIState::RUNNING_AWAY);
    }
    
    // 清除当前目标
    if (m_target_manager) {
        m_target_manager->ClearTarget();
    }
}

void AnimalObject::ProcessRunningAway() {
    if (!m_run_away_mode) return;
    
    // 检查逃跑时间
    DWORD current_time = g_functions::GetCurrentTime();
    if (current_time - m_run_away_start >= m_run_away_time) {
        StopRunAwayMode();
        return;
    }
    
    // 随机逃跑方向
    BYTE dir = g_functions::Random(8);
    if (CanWalkTo(dir)) {
        WalkTo(dir, false);
    }
}
```

## 集成计划

### 1. 修正继承关系
```cpp
// 修正前
class PlayObject : public BaseObject { ... }

// 修正后
class PlayObject : public AnimalObject { ... }
```

### 2. 更新现有代码
- 修改PlayObject继承关系
- 更新Monster类设计
- 调整NPC类结构
- 修正Manager系统引用

### 3. 测试验证
- AnimalObject基础功能测试
- AI行为测试
- 目标管理测试
- 与BaseObject集成测试

## 技术要求

### 1. 原项目兼容性
- 100%遵循原项目TAnimalObject逻辑
- 保持相同的AI行为模式
- 维护原项目的方法签名
- 不简化原项目的复杂逻辑

### 2. 代码质量
- 现代C++17标准
- 组件化设计
- 线程安全考虑
- 完整错误处理

### 3. 性能要求
- 高效的目标搜索
- 优化的AI计算
- 最小化内存分配
- 快速的路径计算

## 风险评估

### 高风险项
1. **继承关系修改**: 影响现有PlayObject实现
2. **AI复杂度**: 原项目AI逻辑复杂
3. **性能影响**: AI计算可能影响性能

### 缓解措施
1. **渐进式修改**: 先实现AnimalObject，再修改继承关系
2. **充分测试**: 每个功能完成后立即测试
3. **性能监控**: 实时监控AI性能影响

## 总结

AnimalObject是被遗漏的关键中间层，必须优先实现。通过1周的集中开发，可以建立完整的AI基础架构，为后续的PlayObject重构和Monster系统实现奠定坚实基础。这个修正是整个对象系统重构成功的关键前提。
