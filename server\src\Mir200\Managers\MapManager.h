#pragma once

#include "IManager.h"
#include "EventData.h"
#include "../Common/Types.h"
#include <string>
#include <vector>
#include <unordered_map>
#include <mutex>
#include <shared_mutex>
#include <memory>

// 前向声明
class EventBus;
class Environment;
class BaseObject;

/**
 * @brief 地图传送点配置
 */
struct MapGateConfig {
    std::string fromMap;        // 源地图
    int fromX, fromY;          // 源坐标
    std::string toMap;         // 目标地图
    int toX, toY;              // 目标坐标
    int minLevel;              // 最小等级要求
    int maxLevel;              // 最大等级要求
    int cost;                  // 传送费用
    bool needItem;             // 是否需要道具
    int itemIndex;             // 需要的道具索引
    
    MapGateConfig() : fromX(0), fromY(0), toX(0), toY(0), minLevel(0), maxLevel(999), 
                     cost(0), needItem(false), itemIndex(0) {}
};

/**
 * @brief 地图安全区配置
 */
struct SafeZoneConfig {
    std::string mapName;       // 地图名称
    int x, y;                  // 安全区坐标
    int range;                 // 安全区范围
    bool allowPK;              // 是否允许PK
    bool allowTrade;           // 是否允许交易
    bool allowDrop;            // 是否允许丢弃物品
    
    SafeZoneConfig() : x(0), y(0), range(10), allowPK(false), allowTrade(true), allowDrop(true) {}
};

/**
 * @brief 地图刷新点配置
 */
struct MapSpawnConfig {
    std::string mapName;       // 地图名称
    std::string monsterName;   // 怪物名称
    int x, y;                  // 刷新坐标
    int range;                 // 刷新范围
    int count;                 // 刷新数量
    int interval;              // 刷新间隔(秒)
    int maxCount;              // 最大数量
    
    MapSpawnConfig() : x(0), y(0), range(5), count(1), interval(60), maxCount(10) {}
};

/**
 * @brief 地图管理器
 * 负责管理所有地图相关功能
 * 对应原项目的地图系统功能
 */
class MapManager : public IManager, public IMapProvider, public IEventSubscriber {
private:
    std::string m_managerName;
    bool m_initialized;
    
    // 依赖注入
    EventBus* m_eventBus;
    
    // 地图数据
    std::unordered_map<std::string, std::unique_ptr<Environment>> m_environments;
    std::unordered_map<std::string, std::vector<MapGateConfig>> m_mapGates;
    std::unordered_map<std::string, std::vector<SafeZoneConfig>> m_safeZones;
    std::unordered_map<std::string, std::vector<MapSpawnConfig>> m_spawnConfigs;
    
    // 线程安全
    mutable std::shared_mutex m_environmentMutex;
    mutable std::shared_mutex m_gateMutex;
    mutable std::shared_mutex m_safeMutex;
    mutable std::shared_mutex m_spawnMutex;
    
    // 统计信息
    std::atomic<uint64_t> m_totalMapLoads;
    std::atomic<uint64_t> m_totalTeleports;
    std::atomic<uint64_t> m_totalSpawns;

public:
    MapManager();
    virtual ~MapManager();
    
    // IManager接口实现
    bool Initialize() override;
    void Finalize() override;
    void Update() override;
    const std::string& GetManagerName() const override;
    
    // 依赖注入
    void SetEventBus(EventBus* eventBus);
    
    // IMapProvider接口实现
    Environment* GetEnvironment(const std::string& mapName) override;
    bool CanWalk(const std::string& mapName, int x, int y) override;
    std::vector<BaseObject*> GetObjectsInRange(const std::string& mapName, 
                                              const Point& center, int range) override;
    
    // IEventSubscriber接口实现
    void OnEvent(const std::string& eventType, const EventData& data) override;
    
    // 地图管理功能
    bool LoadMapData();
    bool LoadMapConfigs(const std::string& configFile);
    bool LoadGateConfigs(const std::string& configFile);
    bool LoadSafeZoneConfigs(const std::string& configFile);
    bool LoadSpawnConfigs(const std::string& configFile);
    
    // 地图操作
    bool LoadMap(const std::string& mapName);
    bool UnloadMap(const std::string& mapName);
    bool IsMapLoaded(const std::string& mapName) const;
    std::vector<std::string> GetLoadedMaps() const;
    
    // 传送系统
    bool CanTeleport(const std::string& fromMap, int fromX, int fromY, 
                    const std::string& toMap, int toX, int toY) const;
    bool TeleportPlayer(class PlayObject* player, const std::string& toMap, int toX, int toY);
    MapGateConfig* FindGate(const std::string& mapName, int x, int y);
    
    // 安全区系统
    bool IsInSafeZone(const std::string& mapName, int x, int y) const;
    SafeZoneConfig* GetSafeZone(const std::string& mapName, int x, int y);
    bool CanPKInArea(const std::string& mapName, int x, int y) const;
    bool CanTradeInArea(const std::string& mapName, int x, int y) const;
    bool CanDropInArea(const std::string& mapName, int x, int y) const;
    
    // 刷新系统
    void ProcessSpawns();
    bool SpawnMonster(const std::string& mapName, const std::string& monsterName, int x, int y);
    int GetMonsterCount(const std::string& mapName, const std::string& monsterName) const;
    void AddSpawnConfig(const MapSpawnConfig& config);
    void RemoveSpawnConfig(const std::string& mapName, const std::string& monsterName);
    
    // 地图查询
    bool IsValidPosition(const std::string& mapName, int x, int y) const;
    Point FindRandomWalkablePosition(const std::string& mapName, const Point& center, int range) const;
    std::vector<Point> GetWalkablePositions(const std::string& mapName, const Point& center, int range) const;
    
    // 对象管理
    bool AddObjectToMap(const std::string& mapName, BaseObject* obj);
    bool RemoveObjectFromMap(const std::string& mapName, BaseObject* obj);
    std::vector<BaseObject*> GetNearbyObjects(const std::string& mapName, int x, int y, int range) const;
    
    // 统计信息
    uint64_t GetTotalMapLoads() const { return m_totalMapLoads; }
    uint64_t GetTotalTeleports() const { return m_totalTeleports; }
    uint64_t GetTotalSpawns() const { return m_totalSpawns; }
    
    // 配置管理
    void AddGate(const MapGateConfig& gate);
    void RemoveGate(const std::string& mapName, int x, int y);
    void AddSafeZone(const SafeZoneConfig& safeZone);
    void RemoveSafeZone(const std::string& mapName, int x, int y);

private:
    // 内部辅助方法
    bool LoadMapFromFile(const std::string& mapName);
    bool LoadGateConfigFromFile(const std::string& fileName);
    bool LoadSafeZoneConfigFromFile(const std::string& fileName);
    bool LoadSpawnConfigFromFile(const std::string& fileName);
    
    bool ParseGateConfigLine(const std::string& line, MapGateConfig& config);
    bool ParseSafeZoneConfigLine(const std::string& line, SafeZoneConfig& config);
    bool ParseSpawnConfigLine(const std::string& line, MapSpawnConfig& config);
    
    // 地图验证
    bool ValidateMapData(Environment* env) const;
    bool ValidatePosition(Environment* env, int x, int y) const;
    
    // 刷新处理
    void ProcessMapSpawns(const std::string& mapName);
    bool ShouldSpawnMonster(const MapSpawnConfig& config) const;
    Point FindSpawnPosition(const std::string& mapName, const MapSpawnConfig& config) const;
    
    // 事件处理
    void HandlePlayerMapChange(const struct MapChangeEventData& data);
    void HandleMonsterKilled(const struct MonsterKilledEventData& data);
    void HandleObjectSpawned(const struct ObjectSpawnedEventData& data);
    
    // 日志记录
    void LogMapOperation(const std::string& operation, const std::string& details) const;
    void LogMapError(const std::string& operation, const std::string& error) const;
};
