cmake_minimum_required(VERSION 3.16)
project(Mir200_Manager_Phase1)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置编译选项
if(MSVC)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /W3")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} /Od /Zi")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /O2")
else()
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -g -O0")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O3")
endif()

# 包含目录
include_directories(${CMAKE_CURRENT_SOURCE_DIR})
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/Common)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/Managers)

# Manager架构源文件
set(MANAGER_SOURCES
    # Manager接口和基础类
    Managers/GameConfigManager.cpp
    Managers/LogManager.cpp
    Managers/DatabaseManager.cpp
    Managers/EventBus.cpp
    Managers/ServiceContainer.cpp
    
    # 通用源文件
    Common/M2Share.cpp
)

# Manager架构头文件
set(MANAGER_HEADERS
    # Manager接口和基础类
    Managers/IManager.h
    Managers/EventData.h
    Managers/GameConfigManager.h
    Managers/LogManager.h
    Managers/DatabaseManager.h
    Managers/EventBus.h
    Managers/ServiceContainer.h
    
    # 通用头文件
    Common/Types.h
    Common/M2Share.h
    Common/Protocol.h
    Common/GameStructs.h
    Common/SimpleTypes.h
)

# 创建Manager架构静态库
add_library(Mir200_Managers STATIC ${MANAGER_SOURCES} ${MANAGER_HEADERS})

# 设置库的属性
set_target_properties(Mir200_Managers PROPERTIES
    OUTPUT_NAME "Mir200_Managers"
    ARCHIVE_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/lib"
)

# 链接线程库
find_package(Threads REQUIRED)
target_link_libraries(Mir200_Managers Threads::Threads)

# 如果是Windows，链接额外的库
if(WIN32)
    target_link_libraries(Mir200_Managers ws2_32 winmm)
endif()

# 创建第一阶段测试程序
add_executable(test_manager_phase1 
    test_manager_phase1.cpp
    ${MANAGER_SOURCES}
    ${MANAGER_HEADERS}
)

# 链接库
target_link_libraries(test_manager_phase1 Threads::Threads)

if(WIN32)
    target_link_libraries(test_manager_phase1 ws2_32 winmm)
endif()

# 设置输出目录
set_target_properties(test_manager_phase1 PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
)

# 创建配置和数据目录
file(MAKE_DIRECTORY "${CMAKE_BINARY_DIR}/bin/config")
file(MAKE_DIRECTORY "${CMAKE_BINARY_DIR}/bin/data")
file(MAKE_DIRECTORY "${CMAKE_BINARY_DIR}/bin/logs")

# 复制测试配置文件
configure_file(
    "${CMAKE_CURRENT_SOURCE_DIR}/config/M2Server.ini"
    "${CMAKE_BINARY_DIR}/bin/config/server.ini"
    COPYONLY
)

# 安装目标
install(TARGETS Mir200_Managers
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
)

install(TARGETS test_manager_phase1
    RUNTIME DESTINATION bin
)

# 安装头文件
install(DIRECTORY Managers/
    DESTINATION include/Mir200/Managers
    FILES_MATCHING PATTERN "*.h"
)

install(DIRECTORY Common/
    DESTINATION include/Mir200/Common
    FILES_MATCHING PATTERN "*.h"
)

# 打印配置信息
message(STATUS "=== Mir200 Manager Phase 1 Configuration ===")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "Compiler: ${CMAKE_CXX_COMPILER_ID}")
message(STATUS "Output directory: ${CMAKE_BINARY_DIR}")
message(STATUS "Manager sources: ${MANAGER_SOURCES}")
message(STATUS "==========================================")

# 添加自定义目标用于清理
add_custom_target(clean-all
    COMMAND ${CMAKE_COMMAND} -E remove_directory "${CMAKE_BINARY_DIR}/bin"
    COMMAND ${CMAKE_COMMAND} -E remove_directory "${CMAKE_BINARY_DIR}/lib"
    COMMAND ${CMAKE_COMMAND} -E make_directory "${CMAKE_BINARY_DIR}/bin"
    COMMAND ${CMAKE_COMMAND} -E make_directory "${CMAKE_BINARY_DIR}/lib"
    COMMENT "Cleaning all build outputs"
)

# 添加自定义目标用于运行测试
add_custom_target(run-test
    COMMAND test_manager_phase1
    DEPENDS test_manager_phase1
    WORKING_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
    COMMENT "Running Manager Phase 1 test"
)
